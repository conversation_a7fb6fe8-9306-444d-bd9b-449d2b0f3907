package com.dzpk.crazypoker.business.repositories.mysql;


import com.dzpk.crazypoker.business.handler.bean.PromotionChannelsDaily;
import com.dzpk.crazypoker.business.handler.bean.PromotionChannelsDailyUser;
import com.dzpk.crazypoker.business.handler.bean.UserBalanceCount;
import com.dzpk.crazypoker.business.handler.bean.UserRegisterChannel;
import org.apache.ibatis.annotations.*;

/**
 * PromotionChannelsDao
 *
 * <AUTHOR>
 * @since 2025/8/13
 */
@Mapper
public interface PromotionChannelsDao {

    @Select({
            "SELECT ",
            "urc.register_day, ",
            "urc.channel_id, ",
            "urc.user_id ",
            "FROM user_register_channel urc ",
            "WHERE urc.user_id = #{userId} limit 1"
    })
    @Results({
            @Result(property = "registerDay", column = "register_day"),
            @Result(property = "channelId", column = "channel_id"),
            @Result(property = "userId", column = "user_id")
    })
    UserRegisterChannel findByUserId(@Param("userId") Integer userId);


    @Select("select 1 from promotion_channels_daily where channel_id = #{channelId} and count_day = ${countDay} LIMIT 1")
    Integer existsByChannelIdAndCountDay(@Param("channelId") Integer channelId, @Param("countDay") Integer countDay);

    @Update("update promotion_channels_daily set register_user_count = #{registerUserCount} where channel_id = #{channelId} and count_day = #{countDay}")
    void updateRegisterUserCount(@Param("registerUserCount") Integer registerUserCount,
                                    @Param("channelId") Integer channelId,
                                    @Param("countDay") Integer countDay);

    @Update("update promotion_channels_daily set login_user_count = #{loginUserCount} where channel_id = #{channelId} and count_day = #{countDay}")
    void updateLoginUserCount(@Param("loginUserCount") Integer loginUserCount,
                                 @Param("channelId") Integer channelId,
                                 @Param("countDay") Integer countDay);

    @Update("update promotion_channels_daily set active_user_count = #{activeUserCount} where channel_id = #{channelId} and count_day = #{countDay}")
    void updateActiveUserCount(@Param("activeUserCount") Integer activeUserCount,
                              @Param("channelId") Integer channelId,
                              @Param("countDay") Integer countDay);

    @Update("update promotion_channels_daily set chip_recharge_user_count = #{chipRechargeUserCount}, chip_recharge_count = #{chipRechargeCount}, chip_recharge_amount = #{chipRechargeAmount}, chip_withdraw_user_count = #{chipWithdrawUserCount}, chip_withdraw_count = #{chipWithdrawCount}, chip_withdraw_amount = #{chipWithdrawAmount} where channel_id = #{channelId} and count_day = #{countDay}")
    void updateChipCount(
            @Param("chipRechargeUserCount") Integer chipRechargeUserCount,
            @Param("chipRechargeCount") Integer chipRechargeCount,
            @Param("chipRechargeAmount") Long chipRechargeAmount,
            @Param("chipWithdrawUserCount") Integer chipWithdrawUserCount,
            @Param("chipWithdrawCount") Integer chipWithdrawCount,
            @Param("chipWithdrawAmount") Long chipWithdrawAmount,
            @Param("channelId") Integer channelId,
            @Param("countDay") Integer countDay
    );

    @Update("update promotion_channels_daily set diamond_recharge_user_count = #{diamondRechargeUserCount}, diamond_recharge_count = #{diamondRechargeCount}, diamond_recharge_amount = #{diamondRechargeAmount}, gold_recharge_user_count = #{goldRechargeUserCount}, gold_recharge_count = #{goldRechargeCount}, gold_recharge_amount = #{goldRechargeAmount} where channel_id = #{channelId} and count_day = #{countDay}")
    void updateDiamondAndGoldCount(
            @Param("diamondRechargeUserCount") Integer diamondRechargeUserCount,
            @Param("diamondRechargeCount") Integer diamondRechargeCount,
            @Param("diamondRechargeAmount") Long diamondRechargeAmount,
            @Param("goldRechargeUserCount") Integer goldRechargeUserCount,
            @Param("goldRechargeCount") Integer goldRechargeCount,
            @Param("goldRechargeAmount") Long goldRechargeAmount,
            @Param("channelId") Integer channelId,
            @Param("countDay") Integer countDay
    );


    @Update("update promotion_channels_daily_user set login_count = #{loginCount} where user_id = #{userId} and count_day = #{countDay}")
    void updateLoginCountByUser(@Param("loginCount") Integer loginCount,
                                 @Param("userId") Integer userId,
                                 @Param("countDay") Integer countDay);

    @Update("update promotion_channels_daily_user set active_count = #{activeCount} where user_id = #{userId} and count_day = #{countDay}")
    void updateActiveCountByUser(@Param("activeCount") Integer activeCount,
                                @Param("userId") Integer userId,
                                @Param("countDay") Integer countDay);

    @Update("update promotion_channels_daily_user set chip_recharge_count = #{chipRechargeCount}, chip_recharge_amount = #{chipRechargeAmount}, chip_withdraw_count = #{chipWithdrawCount}, chip_withdraw_amount = #{chipWithdrawAmount}  where user_id = #{userId} and count_day = #{countDay}")
    void updateChipCountByUser(
            @Param("chipRechargeCount") Integer chipRechargeCount,
            @Param("chipRechargeAmount") Long chipRechargeAmount,
            @Param("chipWithdrawCount") Integer chipWithdrawCount,
            @Param("chipWithdrawAmount") Long chipWithdrawAmount,
            @Param("userId") Integer userId,
            @Param("countDay") Integer countDay
    );

    @Update("update promotion_channels_daily_user set diamond_recharge_count = #{diamondRechargeCount}, diamond_recharge_amount = #{diamondRechargeAmount}, gold_recharge_count = #{goldRechargeCount}, gold_recharge_amount = #{goldRechargeAmount} where user_id = #{userId} and count_day = #{countDay}")
    void updateDiamondAndGoldCountByUser(
            @Param("diamondRechargeCount") Integer diamondRechargeCount,
            @Param("diamondRechargeAmount") Long diamondRechargeAmount,
            @Param("goldRechargeCount") Integer goldRechargeCount,
            @Param("goldRechargeAmount") Long goldRechargeAmount,
            @Param("userId") Integer userId,
            @Param("countDay") Integer countDay
    );


    @Select("select count(distinct user_id) from user_register_channel where register_day = #{registerDay} and channel_id = #{channelId}")
    Integer countRegisterUserByChannelAndDay(@Param("registerDay") Integer registerDay, @Param("channelId") Integer channelId);


    @Insert("INSERT into promotion_channels_daily (unique_id, count_day, year, month, day, channel_id) " +
            "VALUES (#{uniqueId}, #{countDay}, #{year}, #{month}, #{day}, #{channelId}) ")
    void insertPromotionChannelsDaily(
            @Param("uniqueId") String uniqueId,
            @Param("countDay") Integer countDay,
            @Param("year") Integer year,
            @Param("month") Integer month,
            @Param("day") Integer day,
            @Param("channelId") Integer channelId
    );

    @Insert("INSERT into promotion_channels_daily_user (unique_id, count_day, year, month, day, channel_id, user_id, registered) " +
            "VALUES (#{uniqueId}, #{countDay}, #{year}, #{month}, #{day}, #{channelId}, #{userId}, #{registered}) ")
    void insertPromotionChannelsDailyUser(
            @Param("uniqueId") String uniqueId,
            @Param("countDay") Integer countDay,
            @Param("year") Integer year,
            @Param("month") Integer month,
            @Param("day") Integer day,
            @Param("channelId") Integer channelId,
            @Param("userId") Integer userId,
            @Param("registered") Integer registered
    );


    @Select("select 1 from promotion_channels_daily_user where user_id = #{userId} and count_day = ${countDay} LIMIT 1")
    Integer existsByUserIdAndCountDay(@Param("userId") Integer userId, @Param("countDay") Integer countDay);

    @Select("SELECT COUNT(*) AS login_count FROM user_login_log WHERE USER_ID = #{userId} AND DATE_FORMAT(LOGIN_TIME, '%Y%m%d') = #{countDay}")
    Integer countLoginByUserAndDay(@Param("userId") Integer userId, @Param("countDay") Integer countDay);

    @Select("SELECT COUNT(*) AS user_login_count FROM promotion_channels_daily_user WHERE count_day = #{countDay} AND channel_id = #{channelId} AND login_count > 0")
    Integer countLoginByChannelAndDay(@Param("countDay") Integer countDay, @Param("channelId") Integer channelId);

    @Select("select count(distinct room_id) from user_balance_audit_log where balance_type = 2 AND type = 2 AND user_id = #{userId} AND DATE_FORMAT(create_time, '%Y%m%d') = #{countDay}")
    Integer countGameRecordByUserAndDay(@Param("userId") Integer userId, @Param("countDay") Integer countDay);

    @Select("SELECT COUNT(*) AS user_login_count FROM promotion_channels_daily_user WHERE count_day = #{countDay} AND channel_id = #{channelId} AND active_count > 0")
    Integer countActiveByChannelAndDay(@Param("countDay") Integer countDay, @Param("channelId") Integer channelId);

    @Insert("INSERT into tribe_member_transaction_daily_count (unique_id, count_day, year, month, day, channel_id, user_id, tribe_id, club_id) " +
            "VALUES (#{uniqueId}, #{countDay}, #{year}, #{month}, #{day}, #{channelId}, #{userId}, #{tribeId}, #{clubId}) ")
    void insertTribeMemberTransactionDailyCount(
            @Param("uniqueId") String uniqueId,
            @Param("countDay") Integer countDay,
            @Param("year") Integer year,
            @Param("month") Integer month,
            @Param("day") Integer day,
            @Param("channelId") Integer channelId,
            @Param("userId") Integer userId,
            @Param("tribeId") Integer tribeId,
            @Param("clubId") Integer clubId
    );

    @Select("SELECT 1 FROM tribe_member_transaction_daily_count WHERE unique_id = #{uniqueId} LIMIT 1")
    Integer existsTribeMemberTransactionDailyCount(@Param("uniqueId") String uniqueId);


    @Update("update tribe_member_transaction_daily_count set recharge_count = #{rechargeCount}, recharge_amount = #{rechargeAmount}, withdraw_count = #{withdrawCount}, withdraw_amount = #{withdrawAmount} where unique_id = #{uniqueId};")
    void updateTribeMemberTransactionDailyCount(
            @Param("uniqueId") String uniqueId,
            @Param("rechargeCount") Integer rechargeCount,
            @Param("rechargeAmount") Long rechargeAmount,
            @Param("withdrawCount") Integer withdrawCount,
            @Param("withdrawAmount") Long withdrawAmount
    );

    @Select("SELECT " +
            "IFNULL(sum(recharge_count), 0) AS recharge_quantity, " +
            "IFNULL(sum(recharge_amount), 0) AS recharge_amount, " +
            "IFNULL(sum(withdraw_count), 0) AS withdraw_quantity, " +
            "IFNULL(sum(withdraw_amount), 0) AS withdraw_amount " +
            "FROM tribe_member_transaction_daily_count " +
            "WHERE channel_id = #{channelId} AND count_day = #{countDay} AND user_id = #{userId};")
    @Results({
            @Result(property = "rechargeQuantity", column = "recharge_quantity"),
            @Result(property = "rechargeAmount", column = "recharge_amount"),
            @Result(property = "withdrawQuantity", column = "withdraw_quantity"),
            @Result(property = "withdrawAmount", column = "withdraw_amount")
    })
    UserBalanceCount countChannelTribeMemberTransactionDailyByUser(
            @Param("channelId") Integer channelId,
            @Param("countDay") Integer countDay,
            @Param("userId") Integer userId
    );

    @Select("select " +
            "sum(chip_recharge_count)  as chip_recharge_count, " +
            "sum(chip_recharge_amount) as chip_recharge_amount, " +
            "sum(chip_withdraw_count) as chip_withdraw_count, " +
            "sum(chip_withdraw_amount) as chip_withdraw_amount, " +
            "count(distinct case when chip_recharge_count > 0 then user_id end) as chip_recharge_user_count, " +
            "count(distinct case when chip_withdraw_count > 0 then user_id end) as chip_withdraw_user_count " +
            "from promotion_channels_daily_user " +
            "where count_day = #{countDay} and channel_id = #{channelId};")
    @Results({
            @Result(property = "chipRechargeCount", column = "chip_recharge_count"),
            @Result(property = "chipRechargeAmount", column = "chip_recharge_amount"),
            @Result(property = "chipWithdrawCount", column = "chip_withdraw_count"),
            @Result(property = "chipWithdrawAmount", column = "chip_withdraw_amount"),
            @Result(property = "chipRechargeUserCount", column = "chip_recharge_user_count"),
            @Result(property = "chipWithdrawUserCount", column = "chip_withdraw_user_count")
    })
    PromotionChannelsDaily countChannelChipByDay(
            @Param("countDay") Integer countDay,
            @Param("channelId") Integer channelId
    );

    @Select("select " +
            "sum(diamond_recharge_count)  as diamond_recharge_count, " +
            "sum(diamond_recharge_amount) as diamond_recharge_amount, " +
            "sum(gold_recharge_count) as gold_recharge_count, " +
            "sum(gold_recharge_amount) as gold_recharge_amount, " +
            "count(distinct case when diamond_recharge_count > 0 then user_id end) as diamond_recharge_user_count, " +
            "count(distinct case when gold_recharge_count > 0 then user_id end) as gold_recharge_user_count " +
            "from promotion_channels_daily_user " +
            "where count_day = #{countDay} and channel_id = #{channelId};")
    @Results({
            @Result(property = "diamondRechargeCount", column = "diamond_recharge_count"),
            @Result(property = "diamondRechargeAmount", column = "diamond_recharge_amount"),
            @Result(property = "goldRechargeCount", column = "gold_recharge_count"),
            @Result(property = "goldRechargeAmount", column = "gold_recharge_amount"),
            @Result(property = "diamondRechargeUserCount", column = "diamond_recharge_user_count"),
            @Result(property = "goldRechargeUserCount", column = "gold_recharge_user_count")
    })
    PromotionChannelsDaily countChannelDiamondAndGoldByDay(
            @Param("countDay") Integer countDay,
            @Param("channelId") Integer channelId
    );

    @Select("SELECT " +
            " SUM(diamond_recharge_count) AS diamond_recharge_count, " +
            " SUM(diamond_recharge_amount) AS diamond_recharge_amount, " +
            " SUM(gold_recharge_count) AS gold_recharge_count, " +
            " SUM(gold_recharge_amount) AS gold_recharge_amount " +
            "FROM ( " +
            " SELECT " +
            "  COUNT(1) AS diamond_recharge_count, " +
            "  IFNULL(SUM(balance_change), 0) AS diamond_recharge_amount, " +
            "  0 AS gold_recharge_count, " +
            "  0 AS gold_recharge_amount " +
            " FROM user_balance_audit_log " +
            " WHERE balance_type = 0 AND type = 11 AND user_id = #{userId} AND DATE_FORMAT(create_time, '%Y%m%d') = #{countDay} " +
            " UNION ALL " +
            " SELECT " +
            "  COUNT(1) AS diamond_recharge_count, " +
            "  IFNULL(SUM(change_chip), 0) AS diamond_recharge_amount, " +
            "  0 AS gold_recharge_count, " +
            "  0 AS gold_recharge_amount " +
            " FROM user_account_log " +
            " WHERE type IN (32, 33) AND user_id = #{userId} AND DATE_FORMAT(created_time, '%Y%m%d') = #{countDay} " +
            " UNION ALL " +
            " SELECT " +
            "  0 AS diamond_recharge_count, " +
            "  0 AS diamond_recharge_amount, " +
            "  COUNT(1) AS gold_recharge_count, " +
            "  IFNULL(SUM(balance_change), 0) AS gold_recharge_amount " +
            " FROM user_balance_audit_log " +
            " WHERE balance_type = 3 AND type = 11 AND user_id = #{userId} AND DATE_FORMAT(create_time, '%Y%m%d') = #{countDay} " +
            " UNION ALL " +
            " SELECT " +
            "  0 AS diamond_recharge_count, " +
            "  0 AS diamond_recharge_amount, " +
            "  COUNT(1) AS gold_recharge_count, " +
            "  IFNULL(SUM(balance_change), 0) AS gold_recharge_amount " +
            " FROM user_balance_audit_log " +
            " WHERE balance_type = 3 AND type = 19 AND user_id = #{userId} AND DATE_FORMAT(create_time, '%Y%m%d') = #{countDay} " +
            ") t;")
    @Results({
            @Result(property = "diamondRechargeCount", column = "diamond_recharge_count"),
            @Result(property = "diamondRechargeAmount", column = "diamond_recharge_amount"),
            @Result(property = "goldRechargeCount", column = "gold_recharge_count"),
            @Result(property = "goldRechargeAmount", column = "gold_recharge_amount")
    })
    PromotionChannelsDailyUser countUserChannelDiamondAndGoldByDay(
            @Param("userId") Integer userId,
            @Param("countDay") Integer countDay
    );

    @Select("select count(1) > 0 from at_user where user_id = #{userId}")
    Boolean isAiUser(@Param("userId") Integer userId);

}
