package com.dzpk.crazypoker.business.handler;


import com.dzpk.crazypoker.business.config.PromotionChannelsCountAction;
import com.dzpk.crazypoker.business.handler.bean.*;
import com.dzpk.crazypoker.business.redis.LockedActuator;
import com.dzpk.crazypoker.business.redis.RedisDistributedLockGenerator;
import com.dzpk.crazypoker.business.redis.RedisLockConfigCode;
import com.dzpk.crazypoker.business.redis.RedisLockKeyGenerator;
import com.dzpk.crazypoker.business.repositories.mysql.PromotionChannelsDao;
import com.dzpk.crazypoker.business.repositories.mysql.TribePaymentActivityTierDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * PromotionChannelsCountHandler
 *
 * <AUTHOR>
 * @since 2025/8/11
 */
@Slf4j
@Service
public class PromotionChannelsCountHandler {

    @Resource
    PromotionChannelsDao promotionChannelsDao;

    @Resource
    TribePaymentActivityTierDao tribePaymentActivityTierDao;

    /**
     * 处理统计
     * @param message 统计信息
     */
    public void handle(PromotionChannelsCount message) {
        // 根据用户查询渠道信息
        UserRegisterChannel userRegisterChannel = promotionChannelsDao.findByUserId(message.getUserId());
        message.setChannelId(userRegisterChannel.getChannelId().intValue());

        // countDay => yyyyMMdd => 拆分 year/month/day
        message.setYear(message.getCountDay() / 10000);
        message.setMonth((message.getCountDay() % 10000) / 100);
        message.setDay(message.getCountDay() % 100);

        LockedActuator.withLock(() -> {
            switch (message.getAction()) {
                case PromotionChannelsCountAction.REGISTER:
                    countRegister(message);
                    break;
                case PromotionChannelsCountAction.LOGIN:
                    countLogin(message);
                    break;
                case PromotionChannelsCountAction.ACTIVE:
                    countActive(message);
                    break;
                case PromotionChannelsCountAction.CHIP:
                    countChip(message);
                    break;
                case PromotionChannelsCountAction.DIAMOND_GOLD:
                    countDiamondGold(message);
                    break;
                default:
                    log.warn("未知的操作类型: {}", message.getAction());
            }

        }, RedisDistributedLockGenerator.generate(
                RedisLockConfigCode.PROMOTION_CHANNELS_COUNT_CHANNEL,
                RedisLockKeyGenerator.generatePromotionChannelsCountLock(message.getCountDay(), message.getChannelId())
        ), RedisDistributedLockGenerator.generate(
                RedisLockConfigCode.PROMOTION_CHANNELS_COUNT_USER,
                RedisLockKeyGenerator.generatePromotionChannelsCountUserLock(message.getCountDay(), message.getUserId())
        ));

    }

    /**
     * 检查记录是否存在
     * @param isExists
     * @return
     */
    private boolean isExistsCheck(Integer isExists) {
        return isExists != null && isExists == 1;
    }

    /**
     * 统计注册人数
     * @param message 统计信息
     */
    private void countRegister(PromotionChannelsCount message) {
        // 统计当天注册的人数
        Integer registerUserCount = promotionChannelsDao.countRegisterUserByChannelAndDay(message.getCountDay(), message.getChannelId());
        // 先查询是否存在记录
        Integer isExists = promotionChannelsDao.existsByChannelIdAndCountDay(message.getChannelId(), message.getCountDay());
        if (!isExistsCheck(isExists)) {
            // 如果没有，则生成一条默认数据
            promotionChannelsDao.insertPromotionChannelsDaily(message.getCountDay() + ":" + message.getChannelId(), message.getCountDay(), message.getYear(), message.getMonth(), message.getDay(), message.getChannelId());
        }
        // 更新注册人数
        promotionChannelsDao.updateRegisterUserCount(registerUserCount, message.getChannelId(), message.getCountDay());
        // 更新注册人数
        log.info("更新渠道 {} 在 {} 的注册人数为 {}", message.getChannelId(), message.getCountDay(), registerUserCount);
        Integer isExistsUser = promotionChannelsDao.existsByUserIdAndCountDay(message.getUserId(), message.getCountDay());
        if (!isExistsCheck(isExistsUser)) {
            // 如果用户数据没有，则插入一条
            promotionChannelsDao.insertPromotionChannelsDailyUser(message.getCountDay() + ":" + message.getUserId(), message.getCountDay(), message.getYear(), message.getMonth(), message.getDay(), message.getChannelId(), message.getUserId(), 1);
        }
    }

    /**
     * 统计登录人数
     * @param message 统计信息
     */
    private void countLogin(PromotionChannelsCount message) {
        Integer isExistsUser = promotionChannelsDao.existsByUserIdAndCountDay(message.getUserId(), message.getCountDay());
        if (!isExistsCheck(isExistsUser)) {
            // 如果用户数据没有，则插入一条
            promotionChannelsDao.insertPromotionChannelsDailyUser(message.getCountDay() + ":" + message.getUserId(), message.getCountDay(), message.getYear(), message.getMonth(), message.getDay(), message.getChannelId(), message.getUserId(), 0);
        }
        // 统计当天用户登录次数
        Integer loginCount = promotionChannelsDao.countLoginByUserAndDay(message.getUserId(), message.getCountDay());
        // 更新用户登录次数
        promotionChannelsDao.updateLoginCountByUser(loginCount, message.getUserId(), message.getCountDay());
        // 先查询是否存在记录
        Integer isExists = promotionChannelsDao.existsByChannelIdAndCountDay(message.getChannelId(), message.getCountDay());
        if (!isExistsCheck(isExists)) {
            // 如果没有，则生成一条默认数据
            promotionChannelsDao.insertPromotionChannelsDaily(message.getCountDay() + ":" + message.getChannelId(), message.getCountDay(), message.getYear(), message.getMonth(), message.getDay(), message.getChannelId());
        }
        // 合计一次人数
        Integer loginUserCount = promotionChannelsDao.countLoginByChannelAndDay(message.getCountDay(), message.getChannelId());
        promotionChannelsDao.updateLoginUserCount(loginUserCount, message.getChannelId(), message.getCountDay());
        log.info("更新渠道 {} 在 {} 的登录人数为 {}", message.getChannelId(), message.getCountDay(), loginUserCount);
    }

    /**
     * 统计活跃人数
     * @param message 统计信息
     */
    private void countActive(PromotionChannelsCount message) {
        Integer isExistsUser = promotionChannelsDao.existsByUserIdAndCountDay(message.getUserId(), message.getCountDay());
        if (!isExistsCheck(isExistsUser)) {
            // 如果用户数据没有，则插入一条
            promotionChannelsDao.insertPromotionChannelsDailyUser(message.getCountDay() + ":" + message.getUserId(), message.getCountDay(), message.getYear(), message.getMonth(), message.getDay(), message.getChannelId(), message.getUserId(), 0);
        }
        // 统计带入房间记录（一个房间算一次）
        Integer activeCount = promotionChannelsDao.countGameRecordByUserAndDay(message.getUserId(), message.getCountDay());
        // 更新用户活跃次数
        promotionChannelsDao.updateActiveCountByUser(activeCount, message.getUserId(), message.getCountDay());
        // 先查询是否存在记录
        Integer isExists = promotionChannelsDao.existsByChannelIdAndCountDay(message.getChannelId(), message.getCountDay());
        if (!isExistsCheck(isExists)) {
            // 如果没有，则生成一条默认数据
            promotionChannelsDao.insertPromotionChannelsDaily(message.getCountDay() + ":" + message.getChannelId(), message.getCountDay(), message.getYear(), message.getMonth(), message.getDay(), message.getChannelId());
        }
        // 合计一次人数
        Integer activeUserCount = promotionChannelsDao.countActiveByChannelAndDay(message.getCountDay(), message.getChannelId());
        promotionChannelsDao.updateActiveUserCount(activeUserCount, message.getChannelId(), message.getCountDay());
        log.info("更新渠道 {} 在 {} 的活跃人数为 {}", message.getChannelId(), message.getCountDay(), activeUserCount);
    }

    /**
     * 统计带入筹码
     * @param message 统计信息
     */
    private void countChip(PromotionChannelsCount message) {
        // unique_id = YYYYMMDD:tribe_id:club_id:user_id
        String tmtdcUniqueId = message.getCountDay() + ":" + message.getTribeId() + ":" + message.getClubId() + ":" + message.getUserId();

        Integer isExistsTribeMemberTransactionDailyCount = promotionChannelsDao.existsTribeMemberTransactionDailyCount(tmtdcUniqueId);
        if (!isExistsCheck(isExistsTribeMemberTransactionDailyCount)) {
            // 如果没有，则生成一条默认数据
            promotionChannelsDao.insertTribeMemberTransactionDailyCount(
                    tmtdcUniqueId,
                    message.getCountDay(),
                    message.getYear(),
                    message.getMonth(),
                    message.getDay(),
                    message.getChannelId(),
                    message.getUserId(),
                    message.getTribeId(),
                    message.getClubId());
        }

        // 统计今日的带入筹码
        UserBalanceCount userBalanceCount = tribePaymentActivityTierDao.countUserBalanceByDay(message.getUserId(), message.getClubId(), message.getTribeId(), message.getCountDay());

        // 更新用户带入筹码
        promotionChannelsDao.updateTribeMemberTransactionDailyCount(
                tmtdcUniqueId,
                userBalanceCount.getRechargeQuantity().intValue(),
                userBalanceCount.getRechargeAmount(),
                userBalanceCount.getWithdrawQuantity().intValue(),
                userBalanceCount.getWithdrawAmount());

        Integer isExistsUser = promotionChannelsDao.existsByUserIdAndCountDay(message.getUserId(), message.getCountDay());
        if (!isExistsCheck(isExistsUser)) {
            // 如果用户数据没有，则插入一条
            promotionChannelsDao.insertPromotionChannelsDailyUser(message.getCountDay() + ":" + message.getUserId(), message.getCountDay(), message.getYear(), message.getMonth(), message.getDay(), message.getChannelId(), message.getUserId(), 0);
        }

        // 查询渠道下的所有用户的联盟币统计
        UserBalanceCount channelUserBalanceCount = promotionChannelsDao.countChannelTribeMemberTransactionDailyByUser(message.getChannelId(), message.getCountDay(), message.getUserId());

        log.info("统计用户 {} 在渠道 {} 在 {} 的联盟币变动为 充值数量: {}, 充值金额: {}, 提现数量: {}, 提现金额: {}",
                message.getUserId(),
                message.getChannelId(),
                message.getCountDay(),
                channelUserBalanceCount.getRechargeQuantity().intValue(),
                channelUserBalanceCount.getRechargeAmount(),
                channelUserBalanceCount.getWithdrawQuantity().intValue(),
                channelUserBalanceCount.getWithdrawAmount());

        // 更新渠道联盟币统计
        promotionChannelsDao.updateChipCountByUser(
                channelUserBalanceCount.getRechargeQuantity().intValue(),
                channelUserBalanceCount.getRechargeAmount(),
                channelUserBalanceCount.getWithdrawQuantity().intValue(),
                channelUserBalanceCount.getWithdrawAmount(),
                message.getUserId(),
                message.getCountDay());


        // 先查询是否存在记录
        Integer isExists = promotionChannelsDao.existsByChannelIdAndCountDay(message.getChannelId(), message.getCountDay());
        if (!isExistsCheck(isExists)) {
            // 如果没有，则生成一条默认数据
            promotionChannelsDao.insertPromotionChannelsDaily(message.getCountDay() + ":" + message.getChannelId(), message.getCountDay(), message.getYear(), message.getMonth(), message.getDay(), message.getChannelId());
        }

        // 合计有多少人
        PromotionChannelsDaily channelChipCount = promotionChannelsDao.countChannelChipByDay(message.getCountDay(), message.getChannelId());
        promotionChannelsDao.updateChipCount(
                channelChipCount.getChipRechargeUserCount(),
                channelChipCount.getChipRechargeCount(),
                channelChipCount.getChipRechargeAmount(),
                channelChipCount.getChipWithdrawUserCount(),
                channelChipCount.getChipWithdrawCount(),
                channelChipCount.getChipWithdrawAmount(),
                message.getChannelId(),
                message.getCountDay());

        log.info("更新渠道 {} 在 {} 联盟币变动为 充值人数: {}, 充值数量: {}, 充值金额: {}, 提现人数: {}, 提现数量: {}, 提现金额: {}",
                message.getChannelId(),
                message.getCountDay(),
                channelChipCount.getChipRechargeUserCount(),
                channelChipCount.getChipRechargeCount(),
                channelChipCount.getChipRechargeAmount(),
                channelChipCount.getChipWithdrawUserCount(),
                channelChipCount.getChipWithdrawCount(),
                channelChipCount.getChipWithdrawAmount());
    }

    /**
     * 统计钻石充值
     * @param message 统计信息
     */
    private void countDiamondGold(PromotionChannelsCount message) {
        Integer isExistsUser = promotionChannelsDao.existsByUserIdAndCountDay(message.getUserId(), message.getCountDay());
        if (!isExistsCheck(isExistsUser)) {
            // 如果用户数据没有，则插入一条
            promotionChannelsDao.insertPromotionChannelsDailyUser(message.getCountDay() + ":" + message.getUserId(), message.getCountDay(), message.getYear(), message.getMonth(), message.getDay(), message.getChannelId(), message.getUserId(), 0);
        }
        // 统计钻石与金币充值
        PromotionChannelsDailyUser promotionChannelsDailyUser = promotionChannelsDao.countUserChannelDiamondAndGoldByDay(message.getUserId(), message.getCountDay());
        // 更新用户钻石与金币充值
        promotionChannelsDao.updateDiamondAndGoldCountByUser(
                promotionChannelsDailyUser.getDiamondRechargeCount(),
                promotionChannelsDailyUser.getDiamondRechargeAmount(),
                promotionChannelsDailyUser.getGoldRechargeCount(),
                promotionChannelsDailyUser.getGoldRechargeAmount(),
                message.getUserId(),
                message.getCountDay());

        // 先查询是否存在记录
        Integer isExists = promotionChannelsDao.existsByChannelIdAndCountDay(message.getChannelId(), message.getCountDay());
        if (!isExistsCheck(isExists)) {
            // 如果没有，则生成一条默认数据
            promotionChannelsDao.insertPromotionChannelsDaily(message.getCountDay() + ":" + message.getChannelId(), message.getCountDay(), message.getYear(), message.getMonth(), message.getDay(), message.getChannelId());
        }

        // 合计渠道钻石与金币充值
        PromotionChannelsDaily channelDiamondAndGoldCount = promotionChannelsDao.countChannelDiamondAndGoldByDay(
                message.getCountDay(),
                message.getChannelId());

        // 更新渠道钻石与金币充值
        promotionChannelsDao.updateDiamondAndGoldCount(
                channelDiamondAndGoldCount.getDiamondRechargeUserCount(),
                channelDiamondAndGoldCount.getDiamondRechargeCount(),
                channelDiamondAndGoldCount.getDiamondRechargeAmount(),
                channelDiamondAndGoldCount.getGoldRechargeUserCount(),
                channelDiamondAndGoldCount.getGoldRechargeCount(),
                channelDiamondAndGoldCount.getGoldRechargeAmount(),
                message.getChannelId(),
                message.getCountDay());

    }

}
