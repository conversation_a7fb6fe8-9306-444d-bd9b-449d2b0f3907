package com.dzpk.crazypoker.business.handler;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dzpk.crazypoker.business.config.BusinessRabbitMqConfig;
import com.dzpk.crazypoker.business.config.PromotionChannelsCountAction;
import com.dzpk.crazypoker.business.handler.bean.PromotionChannelsCount;
import com.dzpk.crazypoker.business.handler.bean.TransactionCountMessage;
import com.dzpk.crazypoker.business.handler.bean.TribeMemberTransactionCount;
import com.dzpk.crazypoker.business.handler.bean.UserBalanceCount;
import com.dzpk.crazypoker.business.receiver.AbstractBusinessReceiver;
import com.dzpk.crazypoker.business.repositories.mysql.PromotionChannelsDao;
import com.dzpk.crazypoker.business.repositories.mysql.TribeMemberTransactionCountDao;
import com.dzpk.crazypoker.business.repositories.mysql.TribePaymentActivityTierDao;
import com.dzpk.crazypoker.business.sender.BusinessMessageSender;
import com.dzpk.crazypoker.business.util.DateTimeFormatUtils;
import com.dzpk.crazypoker.business.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * TribeMemberTransactionCountHandler
 *
 * <AUTHOR>
 * @since 2025/7/3
 */
@Slf4j
@Service
public class TribeMemberTransactionCountHandler {


    @Resource
    TribePaymentActivityTierDao tribePaymentActivityTierDao;

    @Resource
    TribeMemberTransactionCountDao tribeMemberTransactionCountDao;

    @Resource
    PromotionChannelsDao promotionChannelsDao;

    @Resource
    BusinessMessageSender businessMessageSender;

    /**
     * 处理单个用户的交易统计消息
     * @param message 交易统计消息
     * @return 处理结果，true表示成功
     */
    public Boolean handle(TransactionCountMessage message) {
        try {
            UserBalanceCount userBalanceCount = tribePaymentActivityTierDao.countUserBalance(message.getUserId(), message.getClubId(), message.getTribeId());
            Boolean exists = tribeMemberTransactionCountDao.exists(message.getTribeId(), message.getClubId(), message.getUserId());
            if (exists != null && exists) {
                // update
                tribeMemberTransactionCountDao.update(TribeMemberTransactionCount.builder()
                        .tribeId(message.getTribeId())
                        .clubId(message.getClubId())
                        .userId(message.getUserId())
                        .rechargeQuantity(userBalanceCount.getRechargeQuantity().intValue())
                        .rechargeAmount(new BigDecimal(Math.abs(userBalanceCount.getRechargeAmount())))
                        .withdrawQuantity(userBalanceCount.getWithdrawQuantity().intValue())
                        .withdrawAmount(new BigDecimal(Math.abs(userBalanceCount.getWithdrawAmount())))
                        .build());

                log.info("[TribeMemberTransactionCountHandler] Updated transaction count for user: {}", message.getUserId());

            } else {
                // insert
                tribeMemberTransactionCountDao.insert(TribeMemberTransactionCount.builder()
                        .tribeId(message.getTribeId())
                        .clubId(message.getClubId())
                        .userId(message.getUserId())
                        .rechargeQuantity(userBalanceCount.getRechargeQuantity().intValue())
                        .rechargeAmount(new BigDecimal(Math.abs(userBalanceCount.getRechargeAmount())))
                        .withdrawQuantity(userBalanceCount.getWithdrawQuantity().intValue())
                        .withdrawAmount(new BigDecimal(Math.abs(userBalanceCount.getWithdrawAmount())))
                        .build());

                log.info("[TribeMemberTransactionCountHandler] Inserted transaction count for user: {}", message.getUserId());
            }

            if (message.getAutoAssignTier() != null && message.getAutoAssignTier() == 1) {
                businessMessageSender.sendAutoAssignPaymentActivityTierMessage(message);
            }

            // 查询用户是否是AI
            if (!promotionChannelsDao.isAiUser(message.getUserId())) {
                log.info("[TribeMemberTransactionCountHandler] User is not AI, sending promotion channels count message for user: {}", message.getUserId());
                // 发送统计
                businessMessageSender.sendPromotionChannelsCountMessage(PromotionChannelsCount.builder()
                        .userId(message.getUserId())
                        .tribeId(message.getTribeId())
                        .clubId(message.getClubId())
                        .action(PromotionChannelsCountAction.CHIP)
                        .countDay(DateTimeFormatUtils.nowCountDay())
                        .build());
            }

            return true;
        } catch (Exception e) {
            log.error("[TribeMemberTransactionCountHandler] User Error: {}", JSONObject.toJSONString(message), e);
            return false;
        }
    }

    /**
     * 初始化所有联盟成员的交易统计数据
     * @return 处理结果，true表示成功
     */
    public Boolean handleInit() {
        // 初始化所有联盟成员的交易统计数据
        List<TribeMemberTransactionCount> allTribeMember = tribeMemberTransactionCountDao.findAllTribeMember();
        if (CollectionUtils.isEmpty(allTribeMember)) {
            log.info("[TribeMemberTransactionCountHandler] Init: No tribe member data found.");
            return true;
        }
        for (TribeMemberTransactionCount tm : allTribeMember) {
            TransactionCountMessage message = TransactionCountMessage.builder()
                    .tribeId(tm.getTribeId())
                    .clubId(tm.getClubId())
                    .userId(tm.getUserId())
                    .build();
            Boolean handle = handle(message);
            if (!handle) {
                log.error("[TribeMemberTransactionCountHandler] Init Error: {}", JSONObject.toJSONString(tm));
            }
        }
        return true;
    }





}
