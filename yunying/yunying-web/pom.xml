<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>yunying</artifactId>
        <groupId>com.allinpokers</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yunying-web</artifactId>
    <version>${yunying.version}</version>
    <packaging>jar</packaging>

    <properties>
        <!-- Related to structure of app -->
        <app.com.dzpk.processor.main.class>com.allinpokers.yunying.YunyingWebApplication</app.com.dzpk.processor.main.class>
        <app.classpath.prefix>lib</app.classpath.prefix>
        <app.lib.dir>${project.build.directory}/${app.classpath.prefix}</app.lib.dir>
        <app.config.dir>${project.build.directory}/config</app.config.dir>
        <app.bin.dir>${project.build.directory}/bin</app.bin.dir>

        <!-- 设置打包时间格式， 对应${maven.build.timestamp}变量 -->
        <maven.build.timestamp.format>yyMMdd</maven.build.timestamp.format>

        <swagger-version>2.7.0</swagger-version>
        <java.version>1.8</java.version>
        <poi.version>4.1.0</poi.version>
        <openfeign.version>10.2.0</openfeign.version>
        <!-- 阿里云oss -->
        <ali.oss.version>3.18.1</ali.oss.version>
        <commons-fileupload.version>1.5</commons-fileupload.version>
    </properties>

    <dependencies>
        <!--内部项目依赖-->
        <dependency>
            <groupId>com.allinpokers</groupId>
            <artifactId>yunying-permission</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.allinpokers</groupId>
            <artifactId>yunying-commons</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.allinpokers</groupId>
            <artifactId>yunying-sms</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- mongodb支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>2.8.9</version>
        </dependency>
        <!-- mybatis支持 -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.0.0</version>
        </dependency>

        <!-- mybatis分页插件 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.4.7</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.49</version>
<!--            <scope>runtime</scope>-->
        </dependency>

        <!-- rabbit mq start-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>5.1.5.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
        </dependency>

        <!-- openfeign支持（目前用于调用第三方接口） -->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-core</artifactId>
            <version>${openfeign.version}</version>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-hystrix</artifactId>
            <version>${openfeign.version}</version>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-jackson</artifactId>
            <version>${openfeign.version}</version>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
            <version>${openfeign.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.75</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.aliyun.oss/aliyun-sdk-oss -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>${ali.oss.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>${commons-fileupload.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>4.0.3</version>
        </dependency>



        <!--<dependency>
             <groupId>org.springframework.boot</groupId>
             <artifactId>spring-boot-devtools</artifactId>
             <optional>true</optional>
         </dependency>-->
    </dependencies>

    <build>
        <finalName>${project.artifactId}-${env}-v${project.version}-${maven.build.timestamp}</finalName>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>config</directory>
                <targetPath>${app.config.dir}</targetPath>
                <filtering>false</filtering>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
            <resource>
                <directory>bin</directory>
                <targetPath>${app.bin.dir}</targetPath>
                <filtering>true</filtering>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <!-- 常规打包方式-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <outputDirectory>${app.lib.dir}</outputDirectory>
                    <archive>
                        <addMavenDescriptor>false</addMavenDescriptor>
                        <manifest>
                            <mainClass>${app.com.dzpk.processor.main.class}</mainClass>
                            <!-- 这种配置对scope=system的无效 -->
                            <addClasspath>true</addClasspath>
                            <!--<classpathPrefix>${app.classpath.prefix}</classpathPrefix>-->
                            <classpathLayoutType>custom</classpathLayoutType>
                            <customClasspathLayout>$${artifact.artifactId}-$${artifact.version}.jar</customClasspathLayout>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>

            <!-- filter忽略某些文件 -->
<!--            <plugin>-->
<!--                <artifactId>maven-resources-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <nonFilteredFileExtensions>-->
<!--                        <nonFilteredFileExtension>ipdb</nonFilteredFileExtension>-->
<!--                    </nonFilteredFileExtensions>-->
<!--                </configuration>-->
<!--            </plugin>-->

            <!-- filter之后将配置文件中的中文转换为ascii -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>native2ascii-maven-plugin</artifactId>
                <version>2.0.1</version>
                <executions>
                    <execution>
                        <id>native2ascii</id>
                        <goals>
                            <goal>resources</goal>
                        </goals>
                        <phase>generate-sources</phase><!-- default -->
                        <configuration>
                            <srcDir>${project.basedir}/filters</srcDir><!-- default -->
                            <targetDir>${project.build.directory}/filters</targetDir><!-- default -->
                            <encoding>${project.build.sourceEncoding}</encoding><!-- default -->
                            <!--<includes>
                                <include>*.yml</include>&lt;!&ndash; default &ndash;&gt;
                            </includes>
                            <excludes>
                                <exclude></exclude>&lt;!&ndash; nothing by default &ndash;&gt;
                            </excludes>-->
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- copy应用的依赖jar到lib目录 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <configuration>
                            <includeScope>runtime</includeScope>
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>false</overWriteSnapshots>
                            <overWriteIfNewer>true</overWriteIfNewer>
                            <outputDirectory>${app.lib.dir}</outputDirectory>
                        </configuration>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>cn.caohongliang</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.0.3</version>
                <configuration>
                    <includeAllDependencies>true</includeAllDependencies>
                    <daoRootInterface>com.allinpokers.yunying.dao.BaseDao</daoRootInterface>
                    <daoRootInterfaceNotPrimaryKey>com.allinpokers.yunying.dao.BaseNotPrimaryKeyDao</daoRootInterfaceNotPrimaryKey>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>develop</id>
            <properties>
                <env>develop</env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>aws-online</id>
            <properties>
                <env>aws_online</env>
            </properties>
        </profile>
        <profile>
            <id>aws-test</id>
            <properties>
                <env>aws_test</env>
            </properties>
        </profile>
    </profiles>

</project>