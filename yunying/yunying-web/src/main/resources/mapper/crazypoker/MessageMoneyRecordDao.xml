<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.MessageMoneyRecordDao">

    <insert id="batchInsert">
        insert into message_money_record (
        msg_id, sender_id, reciver_id,
        header, title, content,
        remark, type, msg_status,
        create_time
        )
        values
        <foreach collection="records" item="record" separator=",">
            (
            #{record.msgId,jdbcType=VARCHAR}, #{record.senderId,jdbcType=VARCHAR}, #{record.reciverId,jdbcType=VARCHAR},
            #{record.header,jdbcType=VARCHAR}, #{record.title,jdbcType=VARCHAR}, #{record.content,jdbcType=VARCHAR},
            #{record.remark,jdbcType=VARCHAR}, #{record.type,jdbcType=INTEGER}, #{record.msgStatus,jdbcType=INTEGER},
            #{record.createTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
</mapper>