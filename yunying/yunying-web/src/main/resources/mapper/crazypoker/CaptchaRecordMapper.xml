<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.captcha.mapper.CaptchaRecordMapper">

    <select id="findAll" resultMap="CaptchaRecordResultMap" parameterType="com.allinpokers.yunying.captcha.bean.CaptchaRecordQuery">
        SELECT
        cr.id,
        cr.account,
        cr.account_type,
        cr.code,
        cr.type,
        cr.requested_at,
        cr.status,
        cr.created_at
        FROM crazy_poker.captcha_record cr
        <where>
            <if test="account != null"> AND cr.account LIKE CONCAT('%', #{account}, '%') </if>
            <if test="accountType != null"> AND cr.account_type = #{accountType} </if>
            <if test="code != null"> AND cr.code LIKE CONCAT('%', #{code}, '%') </if>
            <if test="type != null"> AND cr.type = #{type} </if>
            <if test="status != null"> AND cr.status = #{status} </if>
            <if test="startRequestedAt != null and endRequestedAt != null">
                AND cr.requested_at BETWEEN #{startRequestedAt} AND #{endRequestedAt}
            </if>
        </where>
        ORDER BY cr.requested_at DESC
    </select>

    <resultMap id="CaptchaRecordResultMap" type="com.allinpokers.yunying.captcha.bean.CaptchaRecord">
        <id property="id" column="id"/>
        <result property="account" column="account"/>
        <result property="accountType" column="account_type"/>
        <result property="code" column="code"/>
        <result property="type" column="type"/>
        <result property="requestedAt" column="requested_at"/>
        <result property="status" column="status"/>
        <result property="createdAt" column="created_at"/>
    </resultMap>

</mapper>