<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.AuditWarnRecordDao">
  <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->

  <select id="countRecord" resultType="long">
      SELECT count(*)
      FROM audit_warn_record
  </select>

  <select id="countRecordBystatus" resultType="long">
      SELECT count(*)
      FROM audit_warn_record
      where status = #{status}
  </select>

  <select id="findRecord" resultType="com.allinpokers.yunying.entity.crazypoker.AuditWarnRecord">
      SELECT *
      from audit_warn_record
      ORDER BY created_time DESC
  </select>

  <select id="findRecordByStatus" resultType="com.allinpokers.yunying.entity.crazypoker.AuditWarnRecord">
      SELECT *
      from audit_warn_record
      where status = #{status}
      ORDER BY created_time DESC
  </select>

  <select id="countRecordByKey" resultType="long">
      SELECT count(*)
      FROM audit_warn_record
      where nick_name = #{key} or random_id = #{key}
  </select>

  <select id="countRecordBystatusAndKey" resultType="long">
      SELECT count(*)
      FROM audit_warn_record
      where status = #{status} and nick_name = #{key} or random_id = #{key}
  </select>

  <select id="findRecordByKey" resultType="com.allinpokers.yunying.entity.crazypoker.AuditWarnRecord">
      SELECT *
      from audit_warn_record
      where nick_name = #{key} or random_id = #{key}
      ORDER BY created_time DESC
  </select>

  <select id="findRecordByStatusAndKey" resultType="com.allinpokers.yunying.entity.crazypoker.AuditWarnRecord">
      SELECT *
      from audit_warn_record
      where status = #{status} and nick_name = #{key} or random_id = #{key}
      ORDER BY created_time DESC
  </select>

</mapper>