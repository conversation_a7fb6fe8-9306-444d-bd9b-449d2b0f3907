<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.UserDetailsInfoDao">

  <select id="selectUserClubList" resultType="com.allinpokers.yunying.entity.plus.user.SmallUserInfo" parameterType="java.util.List">
    SELECT u.USER_ID userId,u.`random_num`, u.nike_name nickName,labelConfig.name userLabel,u.PHONE, IFNULL(basic.`forbid`,1) forbid,
    record.id record_id,record.name clubName ,IFNULL(club.promotion_type,0) promotionType, basic.REG_TIME createdTime,old_nike_name old<PERSON><PERSON><PERSON><PERSON>, u.inner_type innerType,
    u.head as userHead, u.use_custom as useCustom, u.custom_url as customUrl, case when u.ROOM_PERSION_TYPE = 0 then 0 else 1 end as regType, pc.name as channelName, pc.id as channelId, pc.code as channelCode
    FROM user_details_info u LEFT JOIN club_members club ON u.USER_ID=club.user_id
    LEFT JOIN user_basic_info basic  ON basic.USER_ID=u.USER_ID
    LEFT JOIN user_label label ON basic.USER_ID=label.user_id
    LEFT JOIN user_label_config labelConfig ON label.user_label_config_id=labelConfig.id
    LEFT JOIN club_record record ON record.id=club.club_id
    LEFT JOIN user_register_channel urc ON urc.user_id = u.USER_ID
    LEFT JOIN promotion_channels pc ON pc.id = urc.channel_id
    <where>
      <if test="list!=null and list.size>0">
        (u.nike_name in
        <foreach collection="list" separator="," item="item" open="(" close=")">
          #{item}
        </foreach>
        OR u.`random_num` IN
        <foreach collection="list" separator="," item="item" open="(" close=")">
          #{item}
        </foreach>
        )
      </if>
      <if test="clubId!=null and clubId != 0">
        and record.id = #{clubId}
      </if>
      <if test="regType != null">
         and urc.channel_id = #{regType}
      </if>
      <if test="startTime!=null and endTime!=null">
        and basic.REG_TIME between #{startTime} and #{endTime}
      </if>
    </where>
    order by basic.REG_TIME desc, u.USER_ID desc
  </select>
  <select id="selectUserNoClubList" resultType="com.allinpokers.yunying.entity.plus.user.SmallUserInfo" parameterType="java.util.List">
    SELECT u.USER_ID userId,u.`random_num`, u.nike_name nickName,labelConfig.name userLabel,u.PHONE, IFNULL(basic.`forbid`,1) forbid,
    record.id record_id,record.name clubName ,IFNULL(club.promotion_type,0) promotionType, basic.REG_TIME createdTime,old_nike_name oldNickName, u.inner_type innerType,
    u.head as userHead, u.use_custom as useCustom, u.custom_url as customUrl, case when u.ROOM_PERSION_TYPE = 0 then 0 else 1 end as regType, pc.name as channelName, pc.id as channelId, pc.code as channelCode
    FROM user_details_info u LEFT JOIN club_members club ON u.USER_ID=club.user_id
    LEFT JOIN user_basic_info basic  ON basic.USER_ID=u.USER_ID
    LEFT JOIN user_label label ON basic.USER_ID=label.user_id
    LEFT JOIN user_label_config labelConfig ON label.user_label_config_id=labelConfig.id
    LEFT JOIN club_record record ON record.id=club.club_id
    LEFT JOIN user_register_channel urc ON urc.user_id = u.USER_ID
    LEFT JOIN promotion_channels pc ON pc.id = urc.channel_id
    <where>
      record.id IS NULL
      <if test="list!=null and list.size>0">
        and (u.nike_name in
        <foreach collection="list" separator="," item="item" open="(" close=")">
          #{item}
        </foreach>
        OR u.`random_num` IN
        <foreach collection="list" separator="," item="item" open="(" close=")">
          #{item}
        </foreach>
        )
      </if>
      <if test="regType!=null">
        and urc.channel_id = #{regType}
      </if>
      <if test="startTime!=null and endTime!=null">
        and basic.REG_TIME between #{startTime} and #{endTime}
      </if>
    </where>
    order by basic.REG_TIME desc, u.USER_ID desc
  </select>

  <select id="selectUserLeaveTableList" resultType="com.allinpokers.yunying.entity.plus.user.SmallUserInfo" parameterType="java.util.List">
    SELECT u.USER_ID userId,u.`random_num`, u.nike_name nickName,labelConfig.name userLabel,u.PHONE, IFNULL(basic.`forbid`,1) forbid,
    record.id record_id,record.name clubName ,IFNULL(club.promotion_type,0) promotionType, basic.REG_TIME createdTime,old_nike_name oldNickName
    FROM user_details_info u LEFT JOIN club_members club ON u.USER_ID=club.user_id
    LEFT JOIN user_basic_info basic  ON basic.USER_ID=u.USER_ID
    LEFT JOIN user_label label ON basic.USER_ID=label.user_id
    LEFT JOIN user_label_config labelConfig ON label.user_label_config_id=labelConfig.id
    LEFT JOIN club_record record ON record.id=club.club_id
    <where>
        1=1
      <if test="list!=null and list.size>0">
         and u.USER_ID IN
        <foreach collection="list" separator="," item="item" open="(" close=")">
          #{item}
        </foreach>
      </if>
      <if test="userRandomId!=null and userRandomId!=''">
        and u.`random_num`=#{userRandomId}
      </if>
    </where>
  </select>
  <select id="findUsersByNicknameRandomId" resultMap="BaseResultMap">
    SELECT * FROM user_details_info where nike_name = #{nickname} or random_num = #{randomId}
  </select>
</mapper>