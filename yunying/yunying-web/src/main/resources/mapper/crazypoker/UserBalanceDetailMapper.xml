<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.allinpokers.yunying.dao.crazypoker.UserBalanceDetailMapper">

    <select id="findUserDiamondBalanceDetail" resultType="com.allinpokers.yunying.model.response.balance.UserBalanceDetail">
        SELECT
        UAL.user_id as userId,
        UDI.random_num as userRandomNum,
        UDI.nike_name as nickname,
        UAL.type,
        1 as balanceType,
        UAL.created_time as changeTime,
        UAL.current_chip as beforeValue,
        UAL.change_chip as changeValue,
        (UAL.current_chip + UAL.change_chip) as afterValue
        FROM user_account_log AS UAL
        LEFT JOIN user_details_info AS UDI ON UAL.user_id = UDI.user_id
        WHERE UAL.user_id = #{userId}
        <if test="type != null">
            AND UAL.type = #{type}
        </if>
        <if test="startTime != null">
            AND UAL.created_time &gt;= STR_TO_DATE(#{startTime}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="endTime != null">
            AND UAL.created_time &lt;= STR_TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')
        </if>
        ORDER BY UAL.created_time DESC
    </select>

    <select id="countUserDiamondBalanceDetail" resultType="long">
        SELECT
        COUNT(UAL.id)
        FROM user_account_log AS UAL
        LEFT JOIN user_details_info AS UDI ON UAL.user_id = UDI.user_id
        WHERE UAL.user_id = #{userId}
        <if test="type != null">
            AND UAL.type = #{type}
        </if>
        <if test="startTime != null">
            AND UAL.created_time &gt;= STR_TO_DATE(#{startTime}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="endTime != null">
            AND UAL.created_time &lt;= STR_TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')
        </if>
    </select>


    <select id="findUserGoldBalanceDetail" resultType="com.allinpokers.yunying.model.response.balance.UserBalanceDetail">
        select
        UBAL.user_id as userId,
        UDI.random_num as userRandomNum,
        UDI.nike_name as nickname,
        3 as balanceType,
        UBAL.type,
        UBAL.create_time as changeTime,
        UBAL.balance_before as beforeValue,
        UBAL.balance_change as changeValue,
        (UBAL.balance_before + UBAL.balance_change) as afterValue,
        UBAL.room_id as roomId,
        GR.name as roomName,
        UBAL.club_id as clubId,
        CR.random_id as clubRandomId,
        CR.name as clubName
        FROM user_balance_audit_log AS UBAL
        LEFT JOIN user_details_info AS UDI ON UBAL.user_id = UDI.user_id
        LEFT JOIN group_room AS GR ON UBAL.room_id = GR.room_id
        LEFT JOIN club_record AS CR ON CR.id = UBAL.club_id
        WHERE UBAL.balance_type = 3
        AND UBAL.user_id = #{userId}
        <if test="type != null">
            AND UBAL.type = #{type}
        </if>
        <if test="startTime != null">
            AND UBAL.create_time &gt;= STR_TO_DATE(#{startTime}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="endTime != null">
            AND UBAL.create_time &lt;=  STR_TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')
        </if>
        ORDER BY UBAL.create_time DESC
    </select>

    <select id="countUserGoldBalanceDetail" resultType="long">
        select
        COUNT(UBAL.id)
        FROM user_balance_audit_log AS UBAL
        LEFT JOIN user_details_info AS UDI ON UBAL.user_id = UDI.user_id
        LEFT JOIN group_room AS GR ON UBAL.room_id = GR.room_id
        LEFT JOIN club_record AS CR ON CR.id = UBAL.club_id
        WHERE UBAL.balance_type = 3
        AND UBAL.user_id = #{userId}
        <if test="type != null">
            AND UBAL.type = #{type}
        </if>
        <if test="startTime != null">
            AND UBAL.create_time &gt;= STR_TO_DATE(#{startTime}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="endTime != null">
            AND UBAL.create_time &lt;=  STR_TO_DATE(#{endTime}, '%Y-%m-%d %H:%i:%s')
        </if>
    </select>

</mapper>