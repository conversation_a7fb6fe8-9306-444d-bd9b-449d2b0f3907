<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.ManualRechargePayChannelChipDao">
    <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->

    <select id="sumPayChannelAbsRechargeChip" resultType="long">
        select ifnull(sum(abs(recharge_chip)), 0)
        from manual_recharge_pay_channel_chip
        <where>
            <if test="payChannelId != null">
                and pay_channel_id = #{payChannelId}
            </if>
            <if test="rechargeType != null">
                and recharge_type = #{rechargeType}
            </if>
            <if test="startTime != null and endTime != null">
                and created_time between #{startTime} and #{endTime}
            </if>
        </where>
    </select>
</mapper>