<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.TribeConfigDao">
  <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->


  <select id="findById" resultType="com.allinpokers.yunying.entity.crazypoker.TribeConfig">


    select * from tribe_config where id = #{id}

  </select>
</mapper>