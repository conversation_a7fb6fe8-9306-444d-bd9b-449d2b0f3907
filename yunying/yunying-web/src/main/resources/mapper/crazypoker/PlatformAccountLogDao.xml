<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.PlatformAccountLogDao">
    <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->

    <select id="sumJpLeaveRoomChip" resultType="long">
        SELECT ifnull(sum(change_chip),0) as total
        from platform_account_log
        WHERE type = 28
        <if test='roomIds != null and roomIds.size>0 '> and `desction` in
            <foreach collection='roomIds' item='item' open='(' close=')' separator=','>#{item}</foreach>
        </if>
    </select>
</mapper>