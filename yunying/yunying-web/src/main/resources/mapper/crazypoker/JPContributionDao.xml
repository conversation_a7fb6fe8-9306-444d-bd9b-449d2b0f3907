<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.JPContributionDao">
    <resultMap id="BaseResultMap" type="com.allinpokers.yunying.mongodb.model.JPContribution">
        <result column="jp.room_id" jdbcType="INTEGER" property="roomId" />
        <result column="jp.room_name" jdbcType="VARCHAR" property="roomName" />
        <result column="jp.user_id" jdbcType="INTEGER" property="userId"/>
        <result column="user.nike_name" jdbcType="VARCHAR" property="userName" />
        <result column="jp.AIstate" jdbcType="INTEGER" property="AT"/>
        <result column="jp.mangzhu" jdbcType="INTEGER" property="mangzhu"/>
        <result column="jp.jp_contribution" jdbcType="INTEGER" property="contribution"/>
        <result column="jp.creat_time" jdbcType="TIMESTAMP" property="creatTime"/>
    </resultMap>
    <!-- 通过子彩池更新对应的表 -->
    <select id="selectJPContribution" resultMap="BaseResultMap" >
select jp.room_id,jp.room_name,jp.user_id,user.nike_name,jp.AIstate,jp.mangzhu,jp.jp_contribution,jp.creat_time from (
        select * from JP_contribution_9 where sign=#{sign}
        union select * from JP_contribution_8 where sign=#{sign}
        union select * from JP_contribution_7 where sign=#{sign}
         union select * from JP_contribution_6 where sign=#{sign}
         union select * from JP_contribution_5 where sign=#{sign}
         union select * from JP_contribution_4 where sign=#{sign}
         union select * from JP_contribution_3 where sign=#{sign}
         union select * from JP_contribution_2 where sign=#{sign}
        union select * from JP_contribution_1 where sign=#{sign}
        union select * from JP_contribution_0 where sign=#{sign}
        ) as jp LEFT JOIN user_details_info as user ON jp.user_id=user.user_id where jp.user_id=#{userId}
    </select>
</mapper>