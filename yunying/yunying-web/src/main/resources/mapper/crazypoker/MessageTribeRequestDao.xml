<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.MessageTribeRequestDao">
    <select id="findByTypeCode" resultMap="BaseResultMap">
        select *
        from message_tribe_request mtr
        where mtr.type = #{messageCode.code}
        order by mtr.create_time desc
    </select>

    <select id="findByTribeIdTypeCode" resultMap="BaseResultMap">
        select *
        from message_tribe_request mtr
        where mtr.tribe_id = #{tribeId}
          and mtr.type = #{messageCode.code}
        order by mtr.create_time desc
    </select>
</mapper>