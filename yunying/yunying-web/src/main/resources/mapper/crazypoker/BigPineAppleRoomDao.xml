<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.BigPineAppleRoomDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunying.entity.crazypoker.BigPineAppleRoom">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="blind" jdbcType="INTEGER" property="blind" />
    <result column="init_chip" jdbcType="INTEGER" property="initChip" />
    <result column="mode" jdbcType="TINYINT" property="mode" />
    <result column="player_count" jdbcType="TINYINT" property="playerCount" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="pause" jdbcType="TINYINT" property="pause" />
    <result column="room_id" jdbcType="INTEGER" property="roomId" />
    <result column="ip" jdbcType="TINYINT" property="ip" />
    <result column="limit_gps" jdbcType="TINYINT" property="limitGps" />
    <result column="max_play_time" jdbcType="INTEGER" property="maxPlayTime" />
    <result column="min_rate" jdbcType="INTEGER" property="minRate" />
    <result column="max_rate" jdbcType="INTEGER" property="maxRate" />
    <result column="joker_on" jdbcType="BIT" property="jokerOn" />
    <result column="tillOver_on" jdbcType="BIT" property="tilloverOn" />
    <result column="game_min_chip" jdbcType="INTEGER" property="gameMinChip" />
    <result column="competition_on" jdbcType="BIT" property="competitionOn" />
    <result column="competition_bringin_timeout" jdbcType="INTEGER" property="competitionBringinTimeout" />
    <result column="bringin_limit_on" jdbcType="BIT" property="bringinLimitOn" />
    <result column="bringin_limit_chip" jdbcType="INTEGER" property="bringinLimitChip" />
    <result column="op_time" jdbcType="INTEGER" property="opTime" />
    <result column="deal_mode" jdbcType="INTEGER" property="dealMode" />
    <result column="logo_url" jdbcType="VARCHAR" property="logoUrl" />
    <result column="server_id" jdbcType="VARCHAR" property="serverId" />
    <result column="access_ip" jdbcType="VARCHAR" property="accessIp" />
    <result column="access_port" jdbcType="INTEGER" property="accessPort" />
    <result column="control" jdbcType="INTEGER" property="control" />
    <result column="charge" jdbcType="INTEGER" property="charge" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="source_id" jdbcType="INTEGER" property="sourceId" />
    <result column="advance" jdbcType="INTEGER" property="advance" />
    <result column="tribe_id" jdbcType="INTEGER" property="tribeId" />
    <result column="credit_control" jdbcType="INTEGER" property="creditControl" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, name, blind, init_chip, mode, player_count, status, creator, create_time, update_time, 
    start_time, pause, room_id, ip, limit_gps, max_play_time, min_rate, max_rate, joker_on, 
    tillOver_on, game_min_chip, competition_on, competition_bringin_timeout, bringin_limit_on, 
    bringin_limit_chip, op_time, deal_mode, logo_url, server_id, access_ip, access_port, 
    control, charge, source, source_id, advance, tribe_id, credit_control
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.BigPineAppleRoomExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bigpineapple_room
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from bigpineapple_room
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from bigpineapple_room
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.BigPineAppleRoomExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from bigpineapple_room
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunying.entity.crazypoker.BigPineAppleRoom">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bigpineapple_room (name, blind, init_chip, 
      mode, player_count, status, 
      creator, create_time, update_time, 
      start_time, pause, room_id, 
      ip, limit_gps, max_play_time, 
      min_rate, max_rate, joker_on, 
      tillOver_on, game_min_chip, competition_on, 
      competition_bringin_timeout, bringin_limit_on, 
      bringin_limit_chip, op_time, deal_mode, 
      logo_url, server_id, access_ip, 
      access_port, control, charge, 
      source, source_id, advance, 
      tribe_id, credit_control)
    values (#{name,jdbcType=VARCHAR}, #{blind,jdbcType=INTEGER}, #{initChip,jdbcType=INTEGER}, 
      #{mode,jdbcType=TINYINT}, #{playerCount,jdbcType=TINYINT}, #{status,jdbcType=INTEGER}, 
      #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{startTime,jdbcType=TIMESTAMP}, #{pause,jdbcType=TINYINT}, #{roomId,jdbcType=INTEGER}, 
      #{ip,jdbcType=TINYINT}, #{limitGps,jdbcType=TINYINT}, #{maxPlayTime,jdbcType=INTEGER}, 
      #{minRate,jdbcType=INTEGER}, #{maxRate,jdbcType=INTEGER}, #{jokerOn,jdbcType=BIT}, 
      #{tilloverOn,jdbcType=BIT}, #{gameMinChip,jdbcType=INTEGER}, #{competitionOn,jdbcType=BIT}, 
      #{competitionBringinTimeout,jdbcType=INTEGER}, #{bringinLimitOn,jdbcType=BIT}, 
      #{bringinLimitChip,jdbcType=INTEGER}, #{opTime,jdbcType=INTEGER}, #{dealMode,jdbcType=INTEGER}, 
      #{logoUrl,jdbcType=VARCHAR}, #{serverId,jdbcType=VARCHAR}, #{accessIp,jdbcType=VARCHAR}, 
      #{accessPort,jdbcType=INTEGER}, #{control,jdbcType=INTEGER}, #{charge,jdbcType=INTEGER}, 
      #{source,jdbcType=INTEGER}, #{sourceId,jdbcType=INTEGER}, #{advance,jdbcType=INTEGER}, 
      #{tribeId,jdbcType=INTEGER}, #{creditControl,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunying.entity.crazypoker.BigPineAppleRoom">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bigpineapple_room
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="blind != null">
        blind,
      </if>
      <if test="initChip != null">
        init_chip,
      </if>
      <if test="mode != null">
        mode,
      </if>
      <if test="playerCount != null">
        player_count,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="pause != null">
        pause,
      </if>
      <if test="roomId != null">
        room_id,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="limitGps != null">
        limit_gps,
      </if>
      <if test="maxPlayTime != null">
        max_play_time,
      </if>
      <if test="minRate != null">
        min_rate,
      </if>
      <if test="maxRate != null">
        max_rate,
      </if>
      <if test="jokerOn != null">
        joker_on,
      </if>
      <if test="tilloverOn != null">
        tillOver_on,
      </if>
      <if test="gameMinChip != null">
        game_min_chip,
      </if>
      <if test="competitionOn != null">
        competition_on,
      </if>
      <if test="competitionBringinTimeout != null">
        competition_bringin_timeout,
      </if>
      <if test="bringinLimitOn != null">
        bringin_limit_on,
      </if>
      <if test="bringinLimitChip != null">
        bringin_limit_chip,
      </if>
      <if test="opTime != null">
        op_time,
      </if>
      <if test="dealMode != null">
        deal_mode,
      </if>
      <if test="logoUrl != null">
        logo_url,
      </if>
      <if test="serverId != null">
        server_id,
      </if>
      <if test="accessIp != null">
        access_ip,
      </if>
      <if test="accessPort != null">
        access_port,
      </if>
      <if test="control != null">
        control,
      </if>
      <if test="charge != null">
        charge,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="advance != null">
        advance,
      </if>
      <if test="tribeId != null">
        tribe_id,
      </if>
      <if test="creditControl != null">
        credit_control,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="blind != null">
        #{blind,jdbcType=INTEGER},
      </if>
      <if test="initChip != null">
        #{initChip,jdbcType=INTEGER},
      </if>
      <if test="mode != null">
        #{mode,jdbcType=TINYINT},
      </if>
      <if test="playerCount != null">
        #{playerCount,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pause != null">
        #{pause,jdbcType=TINYINT},
      </if>
      <if test="roomId != null">
        #{roomId,jdbcType=INTEGER},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=TINYINT},
      </if>
      <if test="limitGps != null">
        #{limitGps,jdbcType=TINYINT},
      </if>
      <if test="maxPlayTime != null">
        #{maxPlayTime,jdbcType=INTEGER},
      </if>
      <if test="minRate != null">
        #{minRate,jdbcType=INTEGER},
      </if>
      <if test="maxRate != null">
        #{maxRate,jdbcType=INTEGER},
      </if>
      <if test="jokerOn != null">
        #{jokerOn,jdbcType=BIT},
      </if>
      <if test="tilloverOn != null">
        #{tilloverOn,jdbcType=BIT},
      </if>
      <if test="gameMinChip != null">
        #{gameMinChip,jdbcType=INTEGER},
      </if>
      <if test="competitionOn != null">
        #{competitionOn,jdbcType=BIT},
      </if>
      <if test="competitionBringinTimeout != null">
        #{competitionBringinTimeout,jdbcType=INTEGER},
      </if>
      <if test="bringinLimitOn != null">
        #{bringinLimitOn,jdbcType=BIT},
      </if>
      <if test="bringinLimitChip != null">
        #{bringinLimitChip,jdbcType=INTEGER},
      </if>
      <if test="opTime != null">
        #{opTime,jdbcType=INTEGER},
      </if>
      <if test="dealMode != null">
        #{dealMode,jdbcType=INTEGER},
      </if>
      <if test="logoUrl != null">
        #{logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="serverId != null">
        #{serverId,jdbcType=VARCHAR},
      </if>
      <if test="accessIp != null">
        #{accessIp,jdbcType=VARCHAR},
      </if>
      <if test="accessPort != null">
        #{accessPort,jdbcType=INTEGER},
      </if>
      <if test="control != null">
        #{control,jdbcType=INTEGER},
      </if>
      <if test="charge != null">
        #{charge,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="advance != null">
        #{advance,jdbcType=INTEGER},
      </if>
      <if test="tribeId != null">
        #{tribeId,jdbcType=INTEGER},
      </if>
      <if test="creditControl != null">
        #{creditControl,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunying.entity.crazypoker.example.BigPineAppleRoomExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from bigpineapple_room
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update bigpineapple_room
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.blind != null">
        blind = #{record.blind,jdbcType=INTEGER},
      </if>
      <if test="record.initChip != null">
        init_chip = #{record.initChip,jdbcType=INTEGER},
      </if>
      <if test="record.mode != null">
        mode = #{record.mode,jdbcType=TINYINT},
      </if>
      <if test="record.playerCount != null">
        player_count = #{record.playerCount,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=INTEGER},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.pause != null">
        pause = #{record.pause,jdbcType=TINYINT},
      </if>
      <if test="record.roomId != null">
        room_id = #{record.roomId,jdbcType=INTEGER},
      </if>
      <if test="record.ip != null">
        ip = #{record.ip,jdbcType=TINYINT},
      </if>
      <if test="record.limitGps != null">
        limit_gps = #{record.limitGps,jdbcType=TINYINT},
      </if>
      <if test="record.maxPlayTime != null">
        max_play_time = #{record.maxPlayTime,jdbcType=INTEGER},
      </if>
      <if test="record.minRate != null">
        min_rate = #{record.minRate,jdbcType=INTEGER},
      </if>
      <if test="record.maxRate != null">
        max_rate = #{record.maxRate,jdbcType=INTEGER},
      </if>
      <if test="record.jokerOn != null">
        joker_on = #{record.jokerOn,jdbcType=BIT},
      </if>
      <if test="record.tilloverOn != null">
        tillOver_on = #{record.tilloverOn,jdbcType=BIT},
      </if>
      <if test="record.gameMinChip != null">
        game_min_chip = #{record.gameMinChip,jdbcType=INTEGER},
      </if>
      <if test="record.competitionOn != null">
        competition_on = #{record.competitionOn,jdbcType=BIT},
      </if>
      <if test="record.competitionBringinTimeout != null">
        competition_bringin_timeout = #{record.competitionBringinTimeout,jdbcType=INTEGER},
      </if>
      <if test="record.bringinLimitOn != null">
        bringin_limit_on = #{record.bringinLimitOn,jdbcType=BIT},
      </if>
      <if test="record.bringinLimitChip != null">
        bringin_limit_chip = #{record.bringinLimitChip,jdbcType=INTEGER},
      </if>
      <if test="record.opTime != null">
        op_time = #{record.opTime,jdbcType=INTEGER},
      </if>
      <if test="record.dealMode != null">
        deal_mode = #{record.dealMode,jdbcType=INTEGER},
      </if>
      <if test="record.logoUrl != null">
        logo_url = #{record.logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.serverId != null">
        server_id = #{record.serverId,jdbcType=VARCHAR},
      </if>
      <if test="record.accessIp != null">
        access_ip = #{record.accessIp,jdbcType=VARCHAR},
      </if>
      <if test="record.accessPort != null">
        access_port = #{record.accessPort,jdbcType=INTEGER},
      </if>
      <if test="record.control != null">
        control = #{record.control,jdbcType=INTEGER},
      </if>
      <if test="record.charge != null">
        charge = #{record.charge,jdbcType=INTEGER},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=INTEGER},
      </if>
      <if test="record.sourceId != null">
        source_id = #{record.sourceId,jdbcType=INTEGER},
      </if>
      <if test="record.advance != null">
        advance = #{record.advance,jdbcType=INTEGER},
      </if>
      <if test="record.tribeId != null">
        tribe_id = #{record.tribeId,jdbcType=INTEGER},
      </if>
      <if test="record.creditControl != null">
        credit_control = #{record.creditControl,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update bigpineapple_room
    set id = #{record.id,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      blind = #{record.blind,jdbcType=INTEGER},
      init_chip = #{record.initChip,jdbcType=INTEGER},
      mode = #{record.mode,jdbcType=TINYINT},
      player_count = #{record.playerCount,jdbcType=TINYINT},
      status = #{record.status,jdbcType=INTEGER},
      creator = #{record.creator,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=INTEGER},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      pause = #{record.pause,jdbcType=TINYINT},
      room_id = #{record.roomId,jdbcType=INTEGER},
      ip = #{record.ip,jdbcType=TINYINT},
      limit_gps = #{record.limitGps,jdbcType=TINYINT},
      max_play_time = #{record.maxPlayTime,jdbcType=INTEGER},
      min_rate = #{record.minRate,jdbcType=INTEGER},
      max_rate = #{record.maxRate,jdbcType=INTEGER},
      joker_on = #{record.jokerOn,jdbcType=BIT},
      tillOver_on = #{record.tilloverOn,jdbcType=BIT},
      game_min_chip = #{record.gameMinChip,jdbcType=INTEGER},
      competition_on = #{record.competitionOn,jdbcType=BIT},
      competition_bringin_timeout = #{record.competitionBringinTimeout,jdbcType=INTEGER},
      bringin_limit_on = #{record.bringinLimitOn,jdbcType=BIT},
      bringin_limit_chip = #{record.bringinLimitChip,jdbcType=INTEGER},
      op_time = #{record.opTime,jdbcType=INTEGER},
      deal_mode = #{record.dealMode,jdbcType=INTEGER},
      logo_url = #{record.logoUrl,jdbcType=VARCHAR},
      server_id = #{record.serverId,jdbcType=VARCHAR},
      access_ip = #{record.accessIp,jdbcType=VARCHAR},
      access_port = #{record.accessPort,jdbcType=INTEGER},
      control = #{record.control,jdbcType=INTEGER},
      charge = #{record.charge,jdbcType=INTEGER},
      source = #{record.source,jdbcType=INTEGER},
      source_id = #{record.sourceId,jdbcType=INTEGER},
      advance = #{record.advance,jdbcType=INTEGER},
      tribe_id = #{record.tribeId,jdbcType=INTEGER},
      credit_control = #{record.creditControl,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunying.entity.crazypoker.BigPineAppleRoom">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update bigpineapple_room
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="blind != null">
        blind = #{blind,jdbcType=INTEGER},
      </if>
      <if test="initChip != null">
        init_chip = #{initChip,jdbcType=INTEGER},
      </if>
      <if test="mode != null">
        mode = #{mode,jdbcType=TINYINT},
      </if>
      <if test="playerCount != null">
        player_count = #{playerCount,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pause != null">
        pause = #{pause,jdbcType=TINYINT},
      </if>
      <if test="roomId != null">
        room_id = #{roomId,jdbcType=INTEGER},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=TINYINT},
      </if>
      <if test="limitGps != null">
        limit_gps = #{limitGps,jdbcType=TINYINT},
      </if>
      <if test="maxPlayTime != null">
        max_play_time = #{maxPlayTime,jdbcType=INTEGER},
      </if>
      <if test="minRate != null">
        min_rate = #{minRate,jdbcType=INTEGER},
      </if>
      <if test="maxRate != null">
        max_rate = #{maxRate,jdbcType=INTEGER},
      </if>
      <if test="jokerOn != null">
        joker_on = #{jokerOn,jdbcType=BIT},
      </if>
      <if test="tilloverOn != null">
        tillOver_on = #{tilloverOn,jdbcType=BIT},
      </if>
      <if test="gameMinChip != null">
        game_min_chip = #{gameMinChip,jdbcType=INTEGER},
      </if>
      <if test="competitionOn != null">
        competition_on = #{competitionOn,jdbcType=BIT},
      </if>
      <if test="competitionBringinTimeout != null">
        competition_bringin_timeout = #{competitionBringinTimeout,jdbcType=INTEGER},
      </if>
      <if test="bringinLimitOn != null">
        bringin_limit_on = #{bringinLimitOn,jdbcType=BIT},
      </if>
      <if test="bringinLimitChip != null">
        bringin_limit_chip = #{bringinLimitChip,jdbcType=INTEGER},
      </if>
      <if test="opTime != null">
        op_time = #{opTime,jdbcType=INTEGER},
      </if>
      <if test="dealMode != null">
        deal_mode = #{dealMode,jdbcType=INTEGER},
      </if>
      <if test="logoUrl != null">
        logo_url = #{logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="serverId != null">
        server_id = #{serverId,jdbcType=VARCHAR},
      </if>
      <if test="accessIp != null">
        access_ip = #{accessIp,jdbcType=VARCHAR},
      </if>
      <if test="accessPort != null">
        access_port = #{accessPort,jdbcType=INTEGER},
      </if>
      <if test="control != null">
        control = #{control,jdbcType=INTEGER},
      </if>
      <if test="charge != null">
        charge = #{charge,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="advance != null">
        advance = #{advance,jdbcType=INTEGER},
      </if>
      <if test="tribeId != null">
        tribe_id = #{tribeId,jdbcType=INTEGER},
      </if>
      <if test="creditControl != null">
        credit_control = #{creditControl,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunying.entity.crazypoker.BigPineAppleRoom">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update bigpineapple_room
    set name = #{name,jdbcType=VARCHAR},
      blind = #{blind,jdbcType=INTEGER},
      init_chip = #{initChip,jdbcType=INTEGER},
      mode = #{mode,jdbcType=TINYINT},
      player_count = #{playerCount,jdbcType=TINYINT},
      status = #{status,jdbcType=INTEGER},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      pause = #{pause,jdbcType=TINYINT},
      room_id = #{roomId,jdbcType=INTEGER},
      ip = #{ip,jdbcType=TINYINT},
      limit_gps = #{limitGps,jdbcType=TINYINT},
      max_play_time = #{maxPlayTime,jdbcType=INTEGER},
      min_rate = #{minRate,jdbcType=INTEGER},
      max_rate = #{maxRate,jdbcType=INTEGER},
      joker_on = #{jokerOn,jdbcType=BIT},
      tillOver_on = #{tilloverOn,jdbcType=BIT},
      game_min_chip = #{gameMinChip,jdbcType=INTEGER},
      competition_on = #{competitionOn,jdbcType=BIT},
      competition_bringin_timeout = #{competitionBringinTimeout,jdbcType=INTEGER},
      bringin_limit_on = #{bringinLimitOn,jdbcType=BIT},
      bringin_limit_chip = #{bringinLimitChip,jdbcType=INTEGER},
      op_time = #{opTime,jdbcType=INTEGER},
      deal_mode = #{dealMode,jdbcType=INTEGER},
      logo_url = #{logoUrl,jdbcType=VARCHAR},
      server_id = #{serverId,jdbcType=VARCHAR},
      access_ip = #{accessIp,jdbcType=VARCHAR},
      access_port = #{accessPort,jdbcType=INTEGER},
      control = #{control,jdbcType=INTEGER},
      charge = #{charge,jdbcType=INTEGER},
      source = #{source,jdbcType=INTEGER},
      source_id = #{sourceId,jdbcType=INTEGER},
      advance = #{advance,jdbcType=INTEGER},
      tribe_id = #{tribeId,jdbcType=INTEGER},
      credit_control = #{creditControl,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>