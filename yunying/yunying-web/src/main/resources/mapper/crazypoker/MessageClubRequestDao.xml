<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.MessageClubRequestDao">
    <select id="findByTypeCode" resultMap="BaseResultMap">
        select *
        from message_club_request mcr
        where mcr.type = #{messageCode.code}
        order by mcr.create_time desc
    </select>
</mapper>