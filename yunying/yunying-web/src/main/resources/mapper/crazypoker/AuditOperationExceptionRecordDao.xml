<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.AuditOperationExceptionRecordDao">
  <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->
  <select id="findRecordByStatusAndKey" resultType="com.allinpokers.yunying.entity.crazypoker.AuditOperationExceptionRecord">
    SELECT *
    from audit_operation_exception_record
    where 1=1
    <if test="type != null and type >= 0">
      and status = #{type}
    </if>
    <if test="key != null and key !=''">
      and (username = #{key} or user_random_id = #{key})
    </if>
    ORDER BY create_time DESC
  </select>

  <select id="countRecordBystatusAndKey" resultType="long">
    SELECT count(*)
    from audit_operation_exception_record
    where 1=1
    <if test="type != null and type >= 0 ">
      and status = #{type}
    </if>
    <if test="key != null and key !=''">
      and (username = #{key} or user_random_id = #{key})
    </if>
  </select>

</mapper>