<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.UserLevelRemarkDao">
    <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->

    <select id="sumNumByUserId" resultType="long">
        select ifnull(sum(num), 0)
        from user_level_remark
        where user_id = #{userId}
    </select>
</mapper>