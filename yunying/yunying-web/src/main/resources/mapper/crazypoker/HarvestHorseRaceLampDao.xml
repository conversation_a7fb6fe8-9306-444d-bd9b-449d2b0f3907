<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.HarvestHorseRaceLampDao">
    <resultMap id="BaseResultMap" type="com.allinpokers.yunying.entity.crazypoker.HarvestHorseRaceLamp">
        <result column="horse.room_id" jdbcType="INTEGER" property="roomId" />
        <result column="room_search.name" jdbcType="VARCHAR" property="roomName" />
        <result column="horse.user_id" jdbcType="INTEGER" property="userId" />
        <result column="user.nike_name" jdbcType="VARCHAR" property="userName" />
        <result column="horse.kdou" jdbcType="INTEGER" property="kdou"/>
        <result column="room_search.room_path" jdbcType="INTEGER" property="roomPath"/>
        <result column="room_search.sb_chip" jdbcType="INTEGER" property="mangzhu"/>
        <result column="horse.create_time" jdbcType="TIMESTAMP" property="creatTime"/>
    </resultMap>
<!--    &lt;!&ndash; 通过子彩池更新对应的表 &ndash;&gt;-->
<!--    <select id="selectHarvestHorseRaceLamp" resultType="com.allinpokers.yunying.entity.crazypoker.HarvestHorseRaceLamp" >-->
<!--    select horse.room_id as roomId,room_search.name as roomName ,horse.user_id as userId,user.nike_name as userName,horse.kdou as kdou-->
<!--    ,room_search.room_path as roomPath ,room_search.sb_chip as mangzhu,horse.create_time as creatTime from-->
<!--    horse_race_lamp_activity_user_prize as horse left join room_search on horse.room_id=room_search.room_id-->
<!--    LEFT JOIN user_details_info as user	ON horse.user_id=user.user_id-->
<!--    where horse.create_time>#{startTime} and #{endTime}>horse.create_time-->
<!--    </select>-->
</mapper>