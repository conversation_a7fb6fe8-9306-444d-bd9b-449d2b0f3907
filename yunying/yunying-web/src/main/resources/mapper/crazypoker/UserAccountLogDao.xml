<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.UserAccountLogDao">
    <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->

    <insert id="insertChipPlCountChangeLog">
        insert into user_account_log (user_id, type, change_source,
                                      current_chip, change_chip,
                                      curr_pl_count, change_pl_count,
                                      external_id, op_id)
        select ua.user_id                  as user_id,
               #{type}                     as type,
               7                           as change_source,
               ua.chip - #{addChip}        as current_chip,
               #{addChip}                  as change_chip,
               ua.pl_count - #{addPlCount} as curr_pl_count,
               #{addPlCount}               as change_pl_count,
               #{externalId}               as external_id,
               #{operatorId}               as op_id
        from user_account ua
        where user_id = #{userId}
    </insert>

    <select id="sumRoomChip" resultType="long">
        SELECT 	ifnull( SUM(IFNULL(change_chip, 0)) + SUM(
        IFNULL(change_not_extract_chip, 0)
        ), 0) AS chip
        from user_account_log
        WHERE type in(5,7)
        <if test='roomIds != null and roomIds.size>0 '> and external_id in
            <foreach collection='roomIds' item='item' open='(' close=')' separator=','>#{item}</foreach>
        </if>
    </select>

    <insert id="batchMTTBackFeeChipChangeLog">
        insert into user_account_log (user_id, type, change_source,
                                      current_chip, change_chip, external_id)
        select ua.user_id                  as user_id,
               #{type}                     as type,
               7                           as change_source,
               ua.chip - #{addChip}        as current_chip,
               #{addChip}                  as change_chip,
               #{externalId}               as external_id,
               ua.user_id                  as op_id
        from user_account ua
        where user_id in
        "<foreach collection='backUserIds' item='item' separator=',' open='(' close=')' >",
        " #{item}",
        "</foreach>",
    </insert>

    <insert id="insertUserAccountLog">
        insert into user_account_log (user_id, type, change_source,
                                      current_chip, change_chip, curr_not_extract_chip, change_not_extract_chip, desction)
        select ua.user_id                  as user_id,
               #{type}                     as type,
               7                           as change_source,
               ua.chip - #{changeChip}     as current_chip,
               #{changeChip}               as change_chip,
               ua.not_extract_chip - #{notExtractChangeChip}               as curr_not_extract_chip,
               #{notExtractChangeChip}         as change_not_extract_chip,
               #{description}              as desction
        from user_account ua
        where user_id = #{userId}
    </insert>
</mapper>