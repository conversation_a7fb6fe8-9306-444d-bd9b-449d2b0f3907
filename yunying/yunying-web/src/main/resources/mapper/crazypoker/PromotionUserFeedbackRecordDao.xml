<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.PromotionUserFeedbackRecordDao">


  <select id="selectClubChipsByClubIdListAndTime" resultType="com.allinpokers.yunying.entity.plus.promotion.ClubPromotionBackPo">
    SELECT IFNULL(SUM(beans),0) AS club_chips,club_id FROM  promotion_user_feedback_record
    <where>
      STATUS='1'
      <if test="end!=null and start!=null">
       and grant_time BETWEEN #{start} AND #{end}
      </if>
      <if test="clubIdList!=null and clubIdList.size()>0">
      and   club_id in
        <foreach collection="clubIdList" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
    </where>
    GROUP BY club_id
  </select>
</mapper>