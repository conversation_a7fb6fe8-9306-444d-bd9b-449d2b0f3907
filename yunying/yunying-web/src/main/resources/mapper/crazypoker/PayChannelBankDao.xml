<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.PayChannelBankDao">
    <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->

    <delete id="deleteByPrimaryKeys">
        delete from pay_channel_bank where pay_channel_id = #{payChannelId} and bank_code in
        <foreach collection="bankCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <insert id="batchInsert">
        insert into pay_channel_bank(pay_channel_id,bank_code,created_time)values
        <foreach collection="payChannelBank" item="item" separator=",">
            (#{item.payChannelId},#{item.bankCode},#{item.createdTime})
        </foreach>

    </insert>

</mapper>