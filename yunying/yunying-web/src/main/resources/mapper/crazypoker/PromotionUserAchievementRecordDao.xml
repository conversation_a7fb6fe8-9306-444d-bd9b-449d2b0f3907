<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.PromotionUserAchievementRecordDao">


    <select id="selectUserAchievementTotalByUserIdListAndTime"
            resultType="com.allinpokers.yunying.entity.plus.promotion.UserPromotionAchievementMumPo">
        SELECT user_id,IFNULL(SUM(total_achievement),0) totalAchievementAll,IFNULL(SUM(my_achievement),0)
        myAchievementAll
        FROM promotion_user_achievement_record
        <where>
            and promotion_type='1'
            <if test="userIdList!=null and  userIdList.size>0">
                AND user_id IN
                <foreach collection="userIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="start!=null and end!=null">
                and `date` BETWEEN  #{start} and #{end}
            </if>
        </where>
        GROUP BY user_id
    </select>
    <!--获取俱乐部推广员的业绩总和-->
    <select id="selectClubPromotersAchievementByClubIdListAndTime"
            resultType="com.allinpokers.yunying.entity.plus.promotion.ClubPromotersAchievementPo">
        SELECT IFNULL(SUM(total_achievement),0) totalAchievementAll,IFNULL(SUM(my_achievement),0)
        myAchievementAll,club_id FROM
        promotion_user_achievement_record
        <where>
            and promotion_type='1'
            <if test="clubIdList!=null and  clubIdList.size>0">
                AND club_id IN
                <foreach collection="clubIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="start!=null and end!=null">
                and 'date' BETWEEN  #{start} and #{end}
            </if>
        </where>
        GROUP BY club_id

    </select>
</mapper>