<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.ManualRechargeUserChipDao">
    <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->

    <select id="sumUserAndPayChannelRechargeChip" resultType="int">
        select sum(totalRechargeChip)
        from (
                 select ifnull(sum(abs(recharge_chip)), 0) as totalRechargeChip
                 from manual_recharge_user_chip
                 where creator_id = #{authUserId}
                   and created_time between #{startTime} and #{endTime}
                 union all
                 select ifnull(sum(abs(recharge_chip)), 0) as totalRechargeChip
                 from manual_recharge_pay_channel_chip
                 where creator_id = #{authUserId}
                   and created_time between #{startTime} and #{endTime}
             ) alias
    </select>

    <select id="sumUserAbsRechargeChip" resultType="long">
        select ifnull(sum(abs(recharge_chip)), 0)
        from manual_recharge_user_chip
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="rechargeType != null">
                and recharge_type = #{rechargeType}
            </if>
            <if test="startTime != null and endTime != null">
                and created_time between #{startTime} and #{endTime}
            </if>
        </where>
    </select>
</mapper>