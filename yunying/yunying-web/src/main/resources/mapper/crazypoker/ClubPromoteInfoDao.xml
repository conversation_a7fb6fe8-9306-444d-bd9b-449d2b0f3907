<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.ClubPromoteInfoDao">
    <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->

    <!--  List<ClubPromoteInfoAndClubName> selectByClubPromoteInfoList(LocalDateTime start, LocalDateTime end, Integer clubId);-->

    <select id="selectByClubPromoteInfoList"
            resultType="com.allinpokers.yunying.entity.plus.clube.ClubPromoteInfoAndClubName">
        select club_record.name clubName,club_record.random_id,club_promote_info.*
        from club_record right join club_promote_info on club_record.id=club_promote_info.club_id
        <where>
            <if test="content!=null and content!=''">
                and (club_record.name=#{content} or club_record.random_id=#{content})
            </if>
            <if test="start!=null and end!=null">
                and club_promote_info.create_time between #{start} and #{end}
            </if>
        </where>
    </select>
</mapper>