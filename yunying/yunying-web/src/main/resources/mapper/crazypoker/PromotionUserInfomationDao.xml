<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.PromotionUserInfomationDao">

    <select id="selectClubPromotionMumRecordByClubIdList"
            resultType="com.allinpokers.yunying.entity.plus.promotion.PromotionClubPo">
        SELECT IFNULL(COUNT(1),0) promotion_num,club_id FROM promotion_user_infomation
        <where>
            promotion_type='1'
            <if test="clubIdList!=null and  clubIdList.size>0">
                AND club_id IN
                <foreach collection="clubIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        group by club_id
    </select>

    <!-- 俱乐部推广员的玩家数量 -->
    <select id="selectClubPromotionMumRecordByClubIdListAndTime"
            resultType="com.allinpokers.yunying.entity.plus.promotion.PromotionClubPo">
        SELECT IFNULL(COUNT(1),0) promotion_num,club_id FROM promotion_user_infomation
        <where>
            promotion_type='1'
            <if test="clubIdList!=null and  clubIdList.size>0">
                AND club_id IN
                <foreach collection="clubIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime!=null and endTime!=null">
                and promoter_time between #{startTime} and #{endTime}
            </if>
        </where>
        group by club_id
    </select>

    <!-- 俱乐部非推广员的玩家数量 -->
    <select id="selectClubPlayerMumByClubIdListAndTime"
            resultType="com.allinpokers.yunying.entity.plus.promotion.NotPromotionerNumClubPo">
        SELECT IFNULL(COUNT(1),0) num,club_id FROM promotion_user_infomation
        <where>
            promotion_type='0'
            <if test="clubIdList!=null and  clubIdList.size>0">
                AND club_id IN
                <foreach collection="clubIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime!=null and endTime!=null">
                and create_time between #{startTime} and #{endTime}
            </if>
        </where>
        group by club_id
    </select>

    <!--  指定俱乐部列表玩家人数-->
    <select id="selectClubListTotalPlayerMumByClubIdListAndTime"
            resultType="java.lang.Integer">
        SELECT IFNULL(COUNT(1),0) num FROM promotion_user_infomation
        <where>
            promotion_type='0'
            <if test="clubIdList!=null and  clubIdList.size>0">
                AND club_id IN
                <foreach collection="clubIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime!=null and endTime!=null">
                and create_time between #{startTime} and #{endTime}
            </if>
        </where>
    </select>

    <!--  指定俱乐部列表推广员人数-->
    <select id="selectClubListTotalPromotionMumByClubIdListAndTime"
            resultType="java.lang.Integer">
        SELECT IFNULL(COUNT(1),0) num FROM promotion_user_infomation
        <where>
            promotion_type='1'
            <if test="clubIdList!=null and  clubIdList.size>0">
                AND club_id IN
                <foreach collection="clubIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startTime!=null and endTime!=null">
                and create_time between #{startTime} and #{endTime}
            </if>
        </where>
    </select>

    <select id="countClubMembersPromotionMumByTime" resultType="com.allinpokers.yunying.entity.plus.promotion.ClubUserPromotionerNumPo">
        select pui.user_id as userId, count(*) as num
        from promotion_user_infomation pui where pui.club_id = #{clubId} and create_time between #{start} and #{end}
        group by pui.user_id;
    </select>
</mapper>