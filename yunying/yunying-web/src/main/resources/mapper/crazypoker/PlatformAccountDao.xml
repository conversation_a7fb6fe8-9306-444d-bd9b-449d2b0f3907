<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.PlatformAccountDao">

    <update id="addChip">
        update platform_account
        set chip = chip + #{chip}
        where code = #{platformCode}
    </update>

    <update id="subtractChipNotLessThanZero">
        update platform_account
        set chip = chip - #{chip}
        where code = #{code} and chip - #{chip} >= 0
    </update>
</mapper>