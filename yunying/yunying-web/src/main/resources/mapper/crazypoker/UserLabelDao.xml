<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.UserLabelDao">
    <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->

    <resultMap id="UserLabelResultMap" type="com.allinpokers.yunying.services.model.UserLabelInfo">
        <result property="userId" column="userId"/>
        <association property="config" column="config_id" columnPrefix="config_" resultMap="com.allinpokers.yunying.dao.crazypoker.UserLabelConfigDao.BaseResultMap"/>
    </resultMap>
    <select id="findUserLabelInfo" resultMap="UserLabelResultMap">
        select u.user_id as userId,
        c.id as config_id,
        c.name as config_name,
        c.created_time as config_created_time,
        c.updated_time as config_updated_time
        from user_label u
        join user_label_config c on u.user_label_config_id = c.id
        where u.user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </select>
</mapper>