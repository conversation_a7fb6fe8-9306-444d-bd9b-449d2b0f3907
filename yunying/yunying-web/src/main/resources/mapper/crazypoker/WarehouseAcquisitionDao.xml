<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.WarehouseAcquisitionDao">
    <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->

    <select id="selectRecentAcquisition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse_acquisition order by acquisition_time desc limit 1
    </select>

    <select id="selectDataQuery" resultType="com.allinpokers.yunying.model.response.warehouseacquisition.DataQuery">
        SELECT 
            (SELECT SUM(uga.gold) / 100 FROM user_gold_account uga) AS gold,
            (SELECT SUM(ua.chip) / 100 FROM user_account ua) AS diamond,
            (SELECT SUM(uta.chips) / 100 FROM user_tribe_account uta) AS chip,
            (
                SELECT SUM(pal.change_chip) / 100 
                FROM platform_account_log pal
                WHERE pal.platform_code = 302 and pal.type = 3
            ) AS chipInsurance,
            (
                SELECT SUM(pal.change_chip) / 100 
                FROM platform_account_log pal
                WHERE pal.platform_code = 303 AND pal.type = 3
            ) AS goldInsurance
    </select>

    <select id="selectUpToDateData" resultType="com.allinpokers.yunying.model.response.warehouseacquisition.DataQuery">
        SELECT
            user_extract_chip AS diamond,
            tribe_chip AS chip,
            gold,
            tribe_chip_insurance AS chipInsurance,
            gold_insurance AS goldInsurance
        FROM
            warehouse_acquisition
        ORDER BY acquisition_time DESC
        LIMIT 1
    </select>
</mapper>