<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.dao.crazypoker.MttRewardDao">
  <!-- 在这里写自定义的sql，autogen 目录下的文件不要手动修改 -->

    <insert id="batchInsertMttReward">
        insert into mtt_reward (
        id, game_id, ranking, prize_name, prize_icon, is_virtual)
        values
        <foreach collection="records" item="record" separator=",">
            (
            #{record.gameId,jdbcType=INTEGER}, #{record.ranking,jdbcType=INTEGER}, #{record.prizeName,jdbcType=VARCHAR},
            #{record.prizeIcon,jdbcType=VARCHAR}, #{record.isVirtual,INTEGER=VARCHAR}}
            )
        </foreach>
    </insert>
</mapper>