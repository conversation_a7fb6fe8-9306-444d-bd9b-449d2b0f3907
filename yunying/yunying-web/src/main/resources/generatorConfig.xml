<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >

<!--
注意：jdbcConnection.connectionURL需要给链接地址后面添加 &amp;useInformationSchema=true
否则会拿不到主键相关信息
-->
<generatorConfiguration>
    <!-- crazy-poker 的相关dao
    修改一下两个属性
    <sqlMapGenerator targetPackage
    javaClientGenerator targetPackage
    -->
    <context id="crazyPoker">
        <!-- 注释 -->
        <commentGenerator>
            <!-- 是否取消注释 -->
            <property name="suppressAllComments" value="false"/>
            <!-- 是否生成注释代时间戳-->
            <property name="suppressDate" value="true"/>
        </commentGenerator>

        <!-- jdbc连接 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="********************************************************************************************************************************************************"
                        userId="work"
                        password="allin168">
            <!-- 加这个是为了防止生成多个数据库下面相同的表 -->
            <property name="nullCatalogMeansCurrent" value="true" />
        </jdbcConnection>

        <!-- 类型转换 -->
        <javaTypeResolver>
            <!-- 是否使用bigDecimal， false可自动转化以下类型（Long, Integer, Short, etc.） -->
            <property name="forceBigDecimals" value="false"/>
            <property name="useJSR310Types" value="true"/>
        </javaTypeResolver>

        <!-- 生成实体类地址 -->
        <javaModelGenerator targetPackage="com.allinpokers.yunying.entity.crazypoker"
                            targetProject="src/main/java">
            <!-- 是否在当前路径下新加一层schema,eg：fase路径com.oop.eksp.user.model， true:com.oop.eksp.user.model.[schemaName] -->
            <property name="enableSubPackages" value="true"/>
            <!-- 是否针对string类型的字段在set的时候进行trim调用 -->
            <property name="trimStrings" value="false"/>
        </javaModelGenerator>

        <!-- 生成mapxml文件 -->
        <sqlMapGenerator targetPackage="mapper/crazypoker"
                         targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- 生成mapxml对应client，也就是接口dao -->
        <javaClientGenerator targetPackage="com.allinpokers.yunying.dao.crazypoker"
                             targetProject="src/main/java" type="XMLMAPPER">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

<!--   <table tableName="cheat_complaint" domainObjectName="CheatComplaint" mapperName="CheatComplaintDao">-->
<!--        <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
<!--    </table>-->

<!--        user_level_config  message_receive_user-->
<!--        <table tableName="user_level_config" domainObjectName="UserLevelConfig" mapperName="UserLevelConfigDao">-->
<!--            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="message_receive_user" domainObjectName="MessageReceiveUser" mapperName="MessageReceiveUserDao">-->
<!--            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="auto_create_user_task" domainObjectName="AutoCreateUserTask" mapperName="AutoCreateUserTaskDao">-->
<!--         <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
<!--         </table>-->
<!--        <table tableName="auto_create_user_log" domainObjectName="AutoCreateUserLog" mapperName="AutoCreateUserLogDao">-->
<!--            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="activity_rp_record" domainObjectName="ActivityRpRecord" mapperName="ActivityRpRecordDao">-->
<!--            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
<!--        </table>-->

        <!-- <table tableName="bigpineapple_room" domainObjectName="BigPineAppleRoom" mapperName="BigPineAppleRoomDao">
         <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
         </table>-->
        <!--<table tableName="club_hot_list" domainObjectName="ClubHotList" mapperName="ClubHotListDao" modelType="flat">-->
        <!--<generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--</table>-->
        <!-- 配置表信息 -->
        <!--<table tableName="room_manager" domainObjectName="RoomManager" mapperName="RoomManagerDao" modelType="flat">-->
        <!--<generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--</table>-->

        <!--<table tableName="mark_account_import_jp_log" domainObjectName="MarkAccountImportJpLog" mapperName="MarkAccountImportJpLogDao" modelType="flat">
        </table>-->
        <!--<table tableName="platform_account" domainObjectName="PlatformAccount" mapperName="PlatformAccountDao" modelType="flat">-->
        <!--</table>-->
        <!--<table tableName="platform_account_log" domainObjectName="PlatformAccountLog" mapperName="PlatformAccountLogDao" modelType="flat">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
        </table>-->
        <!--<table tableName="marketing_gifts" domainObjectName="MarketingGiftsPo" mapperName="MarketingGiftsPoDao" modelType="flat">-->
        <!--<generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--</table>-->



        <!--<table tableName="promotion_user_infomation" domainObjectName="PromotionUserInfomation" mapperName="PromotionUserInfomationDao" modelType="flat"></table>-->
        <!--<table tableName="promotion_user_achievement_record" domainObjectName="PromotionUserAchievementRecord" mapperName="PromotionUserAchievementRecordDao" modelType="flat"></table>-->
        <!--<table tableName="promotion_user_feedback_record" domainObjectName="PromotionUserFeedbackRecord" mapperName="PromotionUserFeedbackRecordDao" modelType="flat"></table>-->

        <!--<table tableName="jackpot_pool_setting" domainObjectName="JackpotPoolSetting" mapperName="JackpotPoolSettingDao" modelType="flat"/>-->
        <!--<table tableName="jackpot_pool" domainObjectName="JackpotPool" mapperName="JackpotPoolDao" modelType="flat"/>-->
        <!--<table tableName="club_record" domainObjectName="ClubRecord" mapperName="ClubRecordDao" modelType="flat">-->
        <!--<generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--</table>-->
        <!--<table tableName="club_members" domainObjectName="ClubMembers" mapperName="ClubMembersDao" modelType="flat" />-->
        <!--<table tableName="user_basic_info" domainObjectName="UserBasicInfo" mapperName="UserBasicInfoDao" modelType="flat"/>-->

        <!--<table tableName="user_details_info" domainObjectName="UserDetailsInfo" mapperName="UserDetailsInfoDao" >-->
        <!--</table>-->
        <!--<table tableName="alliance_relations" domainObjectName="AllianceRelations" mapperName="AllianceRelationsDao"></table>-->
        <!--<table tableName="tribe_members" domainObjectName="TribeMembers" mapperName="TribeMembersDao" />-->
        <!--        <table tableName="club_members" domainObjectName="ClubMembers" mapperName="ClubMembersDao"></table>-->
        <!--        <table tableName="tribe_record" domainObjectName="TribeRecord" mapperName="TribeRecordDao">-->
        <!--            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="tribe_group_record" domainObjectName="TribeGroupRecord" mapperName="TribeGroupRecordDao">-->
        <!--            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--        </table>-->
        <!--<table tableName="club_record" domainObjectName="ClubRecord" mapperName="ClubRecordDao">-->
        <!--<generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--</table>-->

        <!--<table tableName="club_hot_list" domainObjectName="ClubHotList" mapperName="ClubHotListDao">-->
        <!--<generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--</table>-->

        <!--<table tableName="user_details_info" domainObjectName="UserDetailsInfo" mapperName="UserDetailsInfoDao" ></table>-->
        <!--<table tableName="alliance_relations" domainObjectName="AllianceRelations" mapperName="AllianceRelationsDao"></table>&ndash;&gt;-->
        <!--<table tableName="message_club_request" domainObjectName="MessageClubRequest" mapperName="MessageClubRequestDao"/>-->
        <!--<table tableName="message_club_record" domainObjectName="MessageClubRecord" mapperName="MessageClubRecordDao"/>-->
        <!--<table tableName="message_tribe_record" domainObjectName="MessageTribeRecord" mapperName="MessageTribeRecordDao"/>-->
        <!--<table tableName="message_tribe_request" domainObjectName="MessageTribeRequest" mapperName="MessageTribeRequestDao"/>-->
        <!--<table tableName="message_system_record" domainObjectName="MessageSystemRecord" mapperName="MessageSystemRecordDao"/>-->
        <!--        <table tableName="message_unread" domainObjectName="MessageUnread" mapperName="MessageUnreadDao"/>-->
        <!--        <table tableName="group_room" domainObjectName="GroupRoom" mapperName="GroupRoomDao"/>-->
        <!--<table tableName="banner" domainObjectName="Banner" mapperName="BannerDao">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
            <columnOverride column="product" javaType="Integer"/>
            <columnOverride column="type" javaType="Integer"/>
            <columnOverride column="status" javaType="Integer"/>
        </table>-->
        <!--<table tableName="promotion_user_infomation" domainObjectName="PromotionUserInformation" mapperName="PromotionUserInformationDao">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
        </table>
        <table tableName="promotion_user_relations" domainObjectName="PromotionUserRelations" mapperName="PromotionUserRelationsDao">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
        </table>
        <table tableName="function_switch_config" domainObjectName="FunctionSwitchConfig" mapperName="FunctionSwitchConfigDao">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
            <columnOverride column="status" javaType="Integer"/>
        </table>-->

        <!--       <table tableName="withdraw_chip" domainObjectName="WithdrawChip" mapperName="WithdrawChipDao">-->
        <!--           <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="account_payee_check" domainObjectName="AccountPayeeCheck" mapperName="AccountPayeeCheckDao">-->
        <!--            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="account_payee" domainObjectName="AccountPayee" mapperName="AccountPayeeDao">-->
        <!--            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="pay_channel" domainObjectName="PayChannel" mapperName="PayChannelDao">-->
        <!--            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="cms_account" domainObjectName="CmsAccount" mapperName="CmsAccountDao"/>-->
<!--                <table tableName="cms_account_log" domainObjectName="CmsAccountLog" mapperName="CmsAccountLogDao">
                    <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
                </table>
        <table tableName="ios_product_config" domainObjectName="IosProductConfig" mapperName="IosProductConfigDao">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
        </table>
        <table tableName="ios_channel_config" domainObjectName="IosChannelConfig" mapperName="IosChannelConfigDao">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
        </table>

                <table tableName="mall_pay_trans_type_config" domainObjectName="MallPayTransTypeConfig" mapperName="MallPayTransTypeConfigDao">
                    <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
                </table>-->
        <!--<table tableName="user_recharge_order" domainObjectName="UserRechargeOrder" mapperName="UserRechargeOrderDao"/>-->
<!--        <table tableName="mall_pay_trans_type_config" domainObjectName="MallPayTransTypeConfig" mapperName="MallPayTransTypeConfigDao">-->
<!--            <columnOverride column="display_start_time" javaType="java.time.LocalDateTime"/>-->
<!--            <columnOverride column="display_end_time" javaType="java.time.LocalDateTime"/>-->
<!--        </table>-->

        <!--        <table tableName="user_recharge_order" domainObjectName="UserRechargeOrder" mapperName="UserRechargeOrderDao"/>-->
<!--                <table tableName="bank" domainObjectName="Bank" mapperName="BankDao"/>-->
<!--                <table tableName="pay_channel_bank" domainObjectName="PayChannelBank" mapperName="PayChannelBankDao"/>-->

        <!--        <table tableName="address" domainObjectName="Address" mapperName="AddressDao">-->
        <!--            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="escrow_support" domainObjectName="EscrowSupport" mapperName="EscrowSupportDao">-->
        <!--            <columnOverride column="status" javaType="Integer"/>-->
        <!--        </table>-->

        <!--        <table tableName="pay_channel_escrow_payment" domainObjectName="PayChannelEscrowPayment" mapperName="PayChannelEscrowPaymentDao">-->
        <!--            <columnOverride column="status" javaType="Integer"/>-->
        <!--        </table>-->
        <!--        <table tableName="user_account" domainObjectName="UserAccount" mapperName="UserAccountDao"/>-->
        <!--        <table tableName="warehouse_acquisition" domainObjectName="WarehouseAcquisition" mapperName="WarehouseAcquisitionDao"/>-->
<!--                <table tableName="manual_recharge_user_chip" domainObjectName="ManualRechargeUserChip"
                       mapperName="ManualRechargeUserChipDao">
                    <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
                </table>
        <table tableName="manual_recharge_pay_channel_chip" domainObjectName="ManualRechargePayChannelChip"
               mapperName="ManualRechargePayChannelChipDao">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
        </table>

        <table tableName="user_level_remark" domainObjectName="UserLevelRemark"
               mapperName="UserLevelRemarkDao">
        </table>-->

        <!--                <table tableName="club_pay_channel" domainObjectName="ClubPayChannel" mapperName="ClubPayChannelDao">-->
        <!--                    <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--                </table>-->

        <!--        <table tableName="club_pay_channel_log" domainObjectName="ClubPayChannelLog" mapperName="ClubPayChannelLogDao">-->
        <!--            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="club_achievement_commission_statistic" domainObjectName="ClubAchievementCommissionStatistic" mapperName="ClubAchievementCommissionStatisticDao">-->
        <!--            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="audit_warn_record" domainObjectName="AuditWarnRecord" mapperName="AuditWarnRecordDao">-->
        <!--            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>-->
        <!--        </table>-->
<!--        <table tableName="user_bankno_binding" domainObjectName="UserBanknoBinding" mapperName="UserBanknoBindingDao"/>-->
<!--        <table tableName="user_weighting_intervention" domainObjectName="UserWeightingIntervention" mapperName="UserWeightingInterventionDao"/>-->
        <table tableName="user_label" domainObjectName="UserLabel" mapperName="UserLabelDao">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
        </table>
        <table tableName="user_label_config" domainObjectName="UserLabelConfig" mapperName="UserLabelConfigDao">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
        </table>
    </context>

    <!--<context id="crazyPoker-assignment">
        &lt;!&ndash; 注释 &ndash;&gt;
        <commentGenerator>
            &lt;!&ndash; 是否取消注释 &ndash;&gt;
            <property name="suppressAllComments" value="false"/>
            &lt;!&ndash; 是否生成注释代时间戳&ndash;&gt;
            <property name="suppressDate" value="true"/>
        </commentGenerator>

        &lt;!&ndash; jdbc连接 &ndash;&gt;
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="********************************************************************************************************************************************************"
                        userId="work"
                        password="allin168">
            &lt;!&ndash; 加这个是为了防止生成多个数据库下面相同的表 &ndash;&gt;
            <property name="nullCatalogMeansCurrent" value="true" />
        </jdbcConnection>

        &lt;!&ndash; 类型转换 &ndash;&gt;
        <javaTypeResolver>
            &lt;!&ndash; 是否使用bigDecimal， false可自动转化以下类型（Long, Integer, Short, etc.） &ndash;&gt;
            <property name="forceBigDecimals" value="false"/>
            <property name="useJSR310Types" value="true"/>
        </javaTypeResolver>

        &lt;!&ndash; 生成实体类地址 &ndash;&gt;
        <javaModelGenerator targetPackage="com.allinpokers.yunying.assignment.dao.model"
                            targetProject="src/main/java">
            &lt;!&ndash; 是否在当前路径下新加一层schema,eg：fase路径com.oop.eksp.user.model， true:com.oop.eksp.user.model.[schemaName] &ndash;&gt;
            <property name="enableSubPackages" value="false"/>
            &lt;!&ndash; 是否针对string类型的字段在set的时候进行trim调用 &ndash;&gt;
            <property name="trimStrings" value="false"/>
        </javaModelGenerator>

        &lt;!&ndash; 生成mapxml文件 &ndash;&gt;
        <sqlMapGenerator targetPackage="mapper/crazypoker"
                         targetProject="src/main/resources">
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>

        &lt;!&ndash; 生成mapxml对应client，也就是接口dao &ndash;&gt;
        <javaClientGenerator targetPackage="com.allinpokers.yunying.assignment.dao"
                             targetProject="src/main/java" type="XMLMAPPER">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <table tableName="at_user" domainObjectName="ATUser" mapperName="ATUserDao">
        </table>
        <table tableName="mt_user" domainObjectName="MTUser" mapperName="MTUserDao">
        </table>
        <table tableName="at_club_statistics" domainObjectName="ClubStatistics" mapperName="ClubStatisticsDao">
        </table>
    </context>-->

    <!-- <context id="crazyPoker-network">
         &lt;!&ndash; 注释 &ndash;&gt;
         <commentGenerator>
             &lt;!&ndash; 是否取消注释 &ndash;&gt;
             <property name="suppressAllComments" value="false"/>
             &lt;!&ndash; 是否生成注释代时间戳&ndash;&gt;
             <property name="suppressDate" value="true"/>
         </commentGenerator>

         &lt;!&ndash; jdbc连接 &ndash;&gt;
         <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                         connectionURL="********************************************************************************************************************************************************"
                         userId="work"
                         password="allin168">
             &lt;!&ndash; 加这个是为了防止生成多个数据库下面相同的表 &ndash;&gt;
             <property name="nullCatalogMeansCurrent" value="true" />
         </jdbcConnection>

         &lt;!&ndash; 类型转换 &ndash;&gt;
         <javaTypeResolver>
             &lt;!&ndash; 是否使用bigDecimal， false可自动转化以下类型（Long, Integer, Short, etc.） &ndash;&gt;
             <property name="forceBigDecimals" value="false"/>
             <property name="useJSR310Types" value="true"/>
         </javaTypeResolver>

         &lt;!&ndash; 生成实体类地址 &ndash;&gt;
         <javaModelGenerator targetPackage="com.allinpokers.yunying.network.dao.model"
                             targetProject="src/main/java">
             &lt;!&ndash; 是否在当前路径下新加一层schema,eg：fase路径com.oop.eksp.user.model， true:com.oop.eksp.user.model.[schemaName] &ndash;&gt;
             <property name="enableSubPackages" value="false"/>
             &lt;!&ndash; 是否针对string类型的字段在set的时候进行trim调用 &ndash;&gt;
             <property name="trimStrings" value="false"/>
         </javaModelGenerator>

         &lt;!&ndash; 生成mapxml文件 &ndash;&gt;
         <sqlMapGenerator targetPackage="mapper/crazypoker"
                          targetProject="src/main/resources">
             <property name="enableSubPackages" value="false"/>
         </sqlMapGenerator>

         &lt;!&ndash; 生成mapxml对应client，也就是接口dao &ndash;&gt;
         <javaClientGenerator targetPackage="com.allinpokers.yunying.network.dao"
                              targetProject="src/main/java" type="XMLMAPPER">
             <property name="enableSubPackages" value="true"/>
         </javaClientGenerator>

         <table tableName="network_config" domainObjectName="NetworkConfig" mapperName="NetworkConfigDao">
             <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
         </table>
         <table tableName="network_switch_log" domainObjectName="NetworkSwitchLog" mapperName="NetworkSwitchLogDao">
             <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
         </table>
     </context>-->
    <!--
        <context id="yunying-payment">
            &lt;!&ndash; 注释 &ndash;&gt;
            <commentGenerator>
                &lt;!&ndash; 是否取消注释 &ndash;&gt;
                <property name="suppressAllComments" value="false"/>
                &lt;!&ndash; 是否生成注释代时间戳&ndash;&gt;
                <property name="suppressDate" value="true"/>
            </commentGenerator>

            &lt;!&ndash; jdbc连接 &ndash;&gt;
            <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                            connectionURL="****************************************************************************************************************************************************"
                            userId="work"
                            password="allin168">
                &lt;!&ndash; 加这个是为了防止生成多个数据库下面相同的表 &ndash;&gt;
                <property name="nullCatalogMeansCurrent" value="true" />
            </jdbcConnection>

            &lt;!&ndash; 类型转换 &ndash;&gt;
            <javaTypeResolver>
                &lt;!&ndash; 是否使用bigDecimal， false可自动转化以下类型（Long, Integer, Short, etc.） &ndash;&gt;
                <property name="forceBigDecimals" value="false"/>
                <property name="useJSR310Types" value="true"/>
            </javaTypeResolver>

            &lt;!&ndash; 生成实体类地址 &ndash;&gt;
            <javaModelGenerator targetPackage="com.allinpokers.yunying.payment.dao.model"
                                targetProject="src/main/java">
                &lt;!&ndash; 是否在当前路径下新加一层schema,eg：fase路径com.oop.eksp.user.model， true:com.oop.eksp.user.model.[schemaName] &ndash;&gt;
                <property name="enableSubPackages" value="false"/>
                &lt;!&ndash; 是否针对string类型的字段在set的时候进行trim调用 &ndash;&gt;
                <property name="trimStrings" value="false"/>
            </javaModelGenerator>

            &lt;!&ndash; 生成mapxml文件 &ndash;&gt;
            <sqlMapGenerator targetPackage="mapper/yunying/autogen"
                             targetProject="src/main/resources">
                <property name="enableSubPackages" value="false"/>
            </sqlMapGenerator>

            &lt;!&ndash; 生成mapxml对应client，也就是接口dao &ndash;&gt;
            <javaClientGenerator targetPackage="com.allinpokers.yunying.payment.dao"
                                 targetProject="src/main/java" type="XMLMAPPER">
                <property name="enableSubPackages" value="true"/>
            </javaClientGenerator>

            <table tableName="payment_product" domainObjectName="PaymentProduct" mapperName="PaymentProductDao">
                <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
            </table>
        </context>-->

    <!-- yunying 的相关dao
    修改一下两个属性
    <sqlMapGenerator targetPackage
    javaClientGenerator targetPackage
    -->
    <!--<context id="yunying">
        &lt;!&ndash; 注释 &ndash;&gt;
        <commentGenerator>
            &lt;!&ndash; 是否取消注释 &ndash;&gt;
            <property name="suppressAllComments" value="false"/>
            &lt;!&ndash; 是否生成注释代时间戳&ndash;&gt;
            <property name="suppressDate" value="true"/>
        </commentGenerator>

        &lt;!&ndash; jdbc连接 &ndash;&gt;
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="*****************************************************************"
                        userId="work"
                        password="allin168"/>

        &lt;!&ndash; 类型转换 &ndash;&gt;
        <javaTypeResolver>
            &lt;!&ndash; 是否使用bigDecimal， false可自动转化以下类型（Long, Integer, Short, etc.） &ndash;&gt;
            <property name="forceBigDecimals" value="false"/>
            <property name="useJSR310Types" value="true"/>
        </javaTypeResolver>

        &lt;!&ndash; 生成实体类地址 &ndash;&gt;
        <javaModelGenerator targetPackage="com.allinpokers.yunying"
                            targetProject="src/main/java">
            &lt;!&ndash; 是否在当前路径下新加一层schema,eg：fase路径com.oop.eksp.user.model， true:com.oop.eksp.user.model.[schemaName] &ndash;&gt;
            <property name="enableSubPackages" value="true"/>
            &lt;!&ndash; 是否针对string类型的字段在set的时候进行trim调用 &ndash;&gt;
            <property name="trimStrings" value="false"/>
        </javaModelGenerator>

        &lt;!&ndash; 生成mapxml文件 &ndash;&gt;
        <sqlMapGenerator targetPackage="mapper/yunying" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        &lt;!&ndash; 生成mapxml对应client，也就是接口dao &ndash;&gt;
        <javaClientGenerator targetPackage="com.allinpokers.yunying"
                             targetProject="src/main/java" type="XMLMAPPER">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        &lt;!&ndash; 配置表信息 &ndash;&gt;
        <table tableName="osm_message" domainObjectName="message.entity.OsmMessage" mapperName="message.dao.OsmMessageDao">
            <columnOverride column="type" javaType="Integer"/>
            <columnOverride column="operation_type" javaType="Integer"/>
            <columnOverride column="status" javaType="Integer"/>
        </table>
        <table tableName="osm_message_auth_user" domainObjectName="message.entity.OsmMessageAuthUser" mapperName="message.dao.OsmMessageAuthUserDao">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
        </table>
        <table tableName="osm_message_unread" domainObjectName="message.entity.OsmMessageUnread" mapperName="message.dao.OsmMessageUnreadDao">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
        </table>
        <table tableName="cms_message" domainObjectName="message.entity.CmsMessage" mapperName="message.dao.CmsMessageDao">
            <columnOverride column="type" javaType="Integer"/>
            <columnOverride column="operation_type" javaType="Integer"/>
            <columnOverride column="status" javaType="Integer"/>
        </table>
        <table tableName="cms_message_auth_user" domainObjectName="message.entity.CmsMessageAuthUser" mapperName="message.dao.CmsMessageAuthUserDao">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
        </table>
        <table tableName="cms_message_unread" domainObjectName="message.entity.CmsMessageUnread" mapperName="message.dao.CmsMessageUnreadDao">
            <generatedKey column="id" sqlStatement="MySQL" identity="true"/>
        </table>

    </context>-->

    <!-- 按模块分包 -->
<!--    <context id="crazyPoker-subPackage">-->
<!--        &lt;!&ndash; 注释 &ndash;&gt;-->
<!--        <commentGenerator>-->
<!--            &lt;!&ndash; 是否取消注释 &ndash;&gt;-->
<!--            <property name="suppressAllComments" value="false"/>-->
<!--            &lt;!&ndash; 是否生成注释代时间戳&ndash;&gt;-->
<!--            <property name="suppressDate" value="true"/>-->
<!--        </commentGenerator>-->

<!--        &lt;!&ndash; jdbc连接 &ndash;&gt;-->
<!--        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"-->
<!--                        connectionURL="********************************************************************************************************************************************************"-->
<!--                        userId="work"-->
<!--                        password="allin168">-->
<!--            &lt;!&ndash; 加这个是为了防止生成多个数据库下面相同的表 &ndash;&gt;-->
<!--            <property name="nullCatalogMeansCurrent" value="true" />-->
<!--        </jdbcConnection>-->

<!--        &lt;!&ndash; 类型转换 &ndash;&gt;-->
<!--        <javaTypeResolver>-->
<!--            &lt;!&ndash; 是否使用bigDecimal， false可自动转化以下类型（Long, Integer, Short, etc.） &ndash;&gt;-->
<!--            <property name="forceBigDecimals" value="false"/>-->
<!--            <property name="useJSR310Types" value="true"/>-->
<!--        </javaTypeResolver>-->

<!--        &lt;!&ndash; 生成实体类地址 &ndash;&gt;-->
<!--        <javaModelGenerator targetPackage="com.allinpokers.yunying"-->
<!--                            targetProject="src/main/java">-->
<!--            &lt;!&ndash; 是否在当前路径下新加一层schema,eg：fase路径com.oop.eksp.user.model， true:com.oop.eksp.user.model.[schemaName] &ndash;&gt;-->
<!--            <property name="enableSubPackages" value="true"/>-->
<!--            &lt;!&ndash; 是否针对string类型的字段在set的时候进行trim调用 &ndash;&gt;-->
<!--            <property name="trimStrings" value="false"/>-->
<!--        </javaModelGenerator>-->

<!--        &lt;!&ndash; 生成mapxml文件 &ndash;&gt;-->
<!--        <sqlMapGenerator targetPackage="mapper/crazypoker"-->
<!--                         targetProject="src/main/resources">-->
<!--            <property name="enableSubPackages" value="true"/>-->
<!--        </sqlMapGenerator>-->

<!--        &lt;!&ndash; 生成mapxml对应client，也就是接口dao &ndash;&gt;-->
<!--        <javaClientGenerator targetPackage="com.allinpokers.yunying"-->
<!--                             targetProject="src/main/java" type="XMLMAPPER">-->
<!--            <property name="enableSubPackages" value="true"/>-->
<!--        </javaClientGenerator>-->

<!--                <table tableName="user_online_statistics"-->
<!--                       domainObjectName="useronline.entity.UserOnlineStatistics"-->
<!--                       mapperName="useronline.dao.UserOnlineStatisticsDao" />-->
<!--        &lt;!&ndash;        <table tableName="user_join_room"&ndash;&gt;-->
<!--        &lt;!&ndash;               domainObjectName="useronline.entity.UserJoinRoom"&ndash;&gt;-->
<!--        &lt;!&ndash;               mapperName="useronline.dao.UserJoinRoomDao" />&ndash;&gt;-->
<!--&lt;!&ndash;                <table tableName="user_level"&ndash;&gt;-->
<!--&lt;!&ndash;                       domainObjectName="useronline.entity.UserLevel"&ndash;&gt;-->
<!--&lt;!&ndash;                       mapperName="useronline.dao.UserLevelDao">&ndash;&gt;-->
<!--&lt;!&ndash;                    <columnOverride column="is_auto" javaType="Boolean"/>&ndash;&gt;-->
<!--&lt;!&ndash;                </table>&ndash;&gt;-->
<!--        &lt;!&ndash;        <table tableName="user_daily_statistics"&ndash;&gt;-->
<!--        &lt;!&ndash;               domainObjectName="useronline.entity.UserDailyStatistics"&ndash;&gt;-->
<!--        &lt;!&ndash;               mapperName="useronline.dao.UserDailyStatisticsDao" />&ndash;&gt;-->
<!--&lt;!&ndash;        <table tableName="user_login_model_statistics"&ndash;&gt;-->
<!--&lt;!&ndash;               domainObjectName="useronline.entity.UserLoginModelStatistics"&ndash;&gt;-->
<!--&lt;!&ndash;               mapperName="useronline.dao.UserLoginModelStatisticsDao" />&ndash;&gt;-->

<!--    </context>-->

</generatorConfiguration>