package com.allinpokers.yunying.payment.dao.model;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Orders  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Orders {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 0 = pending , 1 = finished , 2=error , 3= cancelled , 4=error
     */
    @ApiModelProperty("0 = pending , 1 = finished , 2=error , 3= cancelled , 4=error")
    private Integer status;

    /**
     * orderId
     */
    @ApiModelProperty("orderId")
    private String orderId;

    /**
     * createdAt
     */
    @ApiModelProperty("createdAt")
    private LocalDateTime createdAt;

    /**
     * updatedAt
     */
    @ApiModelProperty("updatedAt")
    private LocalDateTime updatedAt;

    /**
     * userId
     */
    @ApiModelProperty("userId")
    private Integer userId;

    /**
     * clubId
     */
    @ApiModelProperty("clubId")
    private Integer clubId;

    /**
     * tribeId
     */
    @ApiModelProperty("tribeId")
    private Integer tribeId;

    /**
     * userRandomId
     */
    @ApiModelProperty("userRandomId")
    private String userRandomId;

    /**
     * paymentId
     */
    @ApiModelProperty("paymentId")
    private String paymentId;

    /**
     * productId
     */
    @ApiModelProperty("productId")
    private Integer productId;

    /**
     * lockedBy
     */
    @ApiModelProperty("lockedBy")
    private Integer lockedBy;

    /**
     * locked
     */
    @ApiModelProperty("locked")
    private Integer locked;

    /**
     * chip / diamond
     */
    @ApiModelProperty("chip / diamond")
    private String type;

    /**
     * buy / sell
     */
    @ApiModelProperty("buy / sell")
    private String action;

    /**
     * gatewaysId
     */
    @ApiModelProperty("gatewaysId")
    private String gatewaysId;

    /**
     * methodsId
     */
    @ApiModelProperty("methodsId")
    private String methodsId;

    /**
     * amount
     */
    @ApiModelProperty("amount")
    private BigDecimal amount;

    /**
     * source
     */
    @ApiModelProperty("source")
    private String source;
}