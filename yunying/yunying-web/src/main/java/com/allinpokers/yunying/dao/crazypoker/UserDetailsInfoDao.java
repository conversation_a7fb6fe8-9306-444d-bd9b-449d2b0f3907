package com.allinpokers.yunying.dao.crazypoker;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunying.entity.crazypoker.UserDetailsInfo;
import com.allinpokers.yunying.entity.crazypoker.example.UserDetailsInfoExample;
import com.allinpokers.yunying.entity.plus.user.SmallUserInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 德州扑克用户详细信息表  Mapper
 *
 * <AUTHOR>
 */
@Mapper
@Repository
public interface UserDetailsInfoDao extends BaseDao<UserDetailsInfo, UserDetailsInfoExample, Integer> {

    /**
     * 用户信息，只是归属俱乐部信息
     *
     * @param list
     * @return
     */
    List<SmallUserInfo> selectUserClubList(@Param("list") List<String> list, @Param("clubId") Integer clubId, @Param("regType") Integer regType, @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 无俱乐部用户信息
     * @param list
     * @param regType
     * @param startTime
     * @param endTime
     * @return
     */
    List<SmallUserInfo> selectUserNoClubList(@Param("list") List<String> list, @Param("regType") Integer regType, @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 用户信息，只是归属俱乐部信息
     *
     * @param list
     * @return
     */
    List<SmallUserInfo> selectUserLeaveTableList(List<Integer> list,String userRandomId);
    @Select("SELECT COUNT(1) FROM user_details_info u ,user_basic_info basic ,club_members club  " +
            "WHERE basic.forbid=1 AND u.USER_ID=#{userId} AND u.USER_ID=club.user_id  AND basic.USER_ID=u.USER_ID " +
            "AND  EXISTS (SELECT 1 FROM tribe_members tribe WHERE tribe.club_id=club.club_id) ")
    int selectUserCount(@Param("userId") Integer userId);

    @Select("SELECT USER_ID FROM user_details_info WHERE random_num = #{user} or nike_name = #{user} limit 1")
    Integer selectByRandomIdOrUserName(String user);

    @Select("SELECT random_num FROM user_details_info WHERE user_id = #{userId}")
    Long selectRandomIdByUserId(Integer userId);

    @Select("select random_id FROM club_record where id = #{clubId}")
    Long selectRandomIdByClubId(Integer clubId);

    List<UserDetailsInfo> findUsersByNicknameRandomId(@Param("nickname") String nickname, @Param("randomId") String randomId);

    @Select("select ifnull(count(1),0) from user_basic_info " +
            " where REG_TIME between #{startTime} and #{endTime}")
    Integer getNewUserNum(@Param("startTime") LocalDateTime startTime,@Param("endTime") LocalDateTime endTime);

    @Select("select count(DISTINCT if(ro.order_id is not null or co.id is not null,u.USER_ID,null))" +
            "from user_basic_info  u left join user_recharge_order ro on ro.user_id = u.USER_ID "+
            "left join chip_order co on co.uid = u.USER_ID " +
            "where u.REG_TIME between #{startTime} and #{endTime}")
    Integer getNewUserFinishOrderNum(@Param("startTime") LocalDateTime startTime,@Param("endTime") LocalDateTime endTime);

    @Update("update user_details_info set use_custom = 0 and custom_url = null where USER_ID = #{userId}")
    int resetUserHead(@Param("userId") Integer userId);

}