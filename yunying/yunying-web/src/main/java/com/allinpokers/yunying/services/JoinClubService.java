package com.allinpokers.yunying.services;

import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.joinclub.BathJoinClubResp;
import com.allinpokers.yunying.model.response.joinclub.PromotorsList;

public interface JoinClubService {

    /**
     * 根据俱乐部id查询推广员信息列表
     * @param clubId
     * @param page
     * @param size
     * @return
     */
    CommonRespon<PromotorsList> queryPromotors(Long clubId, String search,  Integer page, Integer size);

    /**
     * 批量加入俱乐部
     * @param clubId
     * @param promotorId
     * @param userIds
     * @return
     */
    CommonRespon<BathJoinClubResp> batchJoinClub(Long clubId, Long promotorId, String userIds);
}
