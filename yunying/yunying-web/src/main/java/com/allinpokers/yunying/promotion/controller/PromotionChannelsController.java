package com.allinpokers.yunying.promotion.controller;


import com.allinpokers.yunying.enu.ResponseCodeEnum;
import com.allinpokers.yunying.model.request.balance.UserBalanceDetailQuery;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.permission.security.SpringSecurityUtils;
import com.allinpokers.yunying.promotion.bean.PromotionChannels;
import com.allinpokers.yunying.promotion.bean.PromotionChannelsDaily;
import com.allinpokers.yunying.promotion.bean.PromotionChannelsQuery;
import com.allinpokers.yunying.promotion.service.PromotionChannelsService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * PromotionChannelsController
 *
 * <AUTHOR>
 * @since 2025/8/8
 */
@Slf4j
@Api(tags = "推广渠道")
@RestController
@RequestMapping("/promotionChannels")
public class PromotionChannelsController {

    @Resource
    PromotionChannelsService promotionChannelsService;

    @ApiOperation(value = "推广渠道-列表")
    @PostMapping("/list")
    public CommonRespon<Object> list(@RequestBody PromotionChannelsQuery req) {
        PageInfo<PromotionChannels> page = promotionChannelsService.list(req);
        return CommonRespon.success(PageBean.of(
                page.getTotal(),
                page.getPageNum(),
                page.getPageSize(),
                page.getList()));
    }

    @ApiOperation(value = "推广渠道-新增")
    @PostMapping("/add")
    public CommonRespon<Object> add(@RequestBody PromotionChannels req) {
        // 校验参数
        String checkResult = saveCheck(req, false);
        if (checkResult != null) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_ERROR, checkResult);
        }
        // 处理用户
        Integer userId = SpringSecurityUtils.getUserId();
        req.setCreatedBy(Long.valueOf(userId));
        promotionChannelsService.add(req);
        return CommonRespon.success();
    }

    /**
     * 校验新增或修改的参数
     * @param req
     * @param isUpdate
     * @return
     */
    private String saveCheck(PromotionChannels req, boolean isUpdate) {
        if (StringUtils.isEmpty(req.getCode())) {
            return "渠道号不能为空！";
        }

        if (StringUtils.isEmpty(req.getName())) {
            return "渠道名称不能为空！";
        }

        if (StringUtils.isEmpty(req.getDownloadUrl())) {
            return "下载地址不能为空！";
        }

        if (isUpdate) {
            if (req.getId() == null) {
                return "id不能为空";
            }
        }

        if (promotionChannelsService.isExist(PromotionChannelsQuery.builder()
                .code(req.getCode())
                .ignoreId(isUpdate ? req.getId() : null)
                .build())) {
            return "渠道号已存在！";
        }

        return null;
    }

    @ApiOperation(value = "推广渠道-修改")
    @PostMapping("/update")
    public CommonRespon<Object> update(@RequestBody PromotionChannels req) {
        // 校验参数
        String checkResult = saveCheck(req, true);
        if (checkResult != null) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_ERROR, checkResult);
        }
        // 处理用户
        Integer userId = SpringSecurityUtils.getUserId();
        req.setUpdatedBy(Long.valueOf(userId));
        promotionChannelsService.update(req);
        return CommonRespon.success();
    }

    @ApiOperation(value = "推广渠道-删除")
    @PostMapping("/delete")
    public CommonRespon<Object> delete(@RequestBody PromotionChannels req) {
        // 校验参数
        if (req.getId() == null) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_ERROR, "id不能为空");
        }
        promotionChannelsService.delete(req.getId());
        return CommonRespon.success();
    }

    @ApiOperation(value = "推广渠道-域名查询")
    @PostMapping("/domain")
    public CommonRespon<Object> domain(@RequestBody PromotionChannelsQuery req) {
        return CommonRespon.success(promotionChannelsService.findRegisterDomain());
    }

    @ApiOperation(value = "推广渠道-域名保存")
    @PostMapping("/saveDomain")
    public CommonRespon<Object> saveDomain(@RequestBody PromotionChannelsQuery req) {
        promotionChannelsService.saveRegisterDomain(req);
        return CommonRespon.success();
    }

    @ApiOperation(value = "推广统计-列表")
    @PostMapping("/countList")
    public CommonRespon<Object> countList(@RequestBody PromotionChannelsQuery req) {
        List<PromotionChannelsDaily> list = promotionChannelsService.countList(req);
        return CommonRespon.success(PageBean.of(
                list.size(),
                1,
                list.size(),
                list));
    }


    @ApiOperation(value = "推广统计-导出")
    @PostMapping("/countExport")
    public void countExport(@RequestBody PromotionChannelsQuery req, HttpServletResponse response) {
        promotionChannelsService.countExport(req, response);
    }

}
