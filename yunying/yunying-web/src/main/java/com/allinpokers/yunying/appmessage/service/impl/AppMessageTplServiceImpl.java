package com.allinpokers.yunying.appmessage.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.allinpokers.yunying.appmessage.bean.AppMessageTpl;
import com.allinpokers.yunying.appmessage.bean.AppMessageTplQuery;
import com.allinpokers.yunying.appmessage.dao.AppMessageTplMapper;
import com.allinpokers.yunying.appmessage.rabbitmq.sender.AppMessageConfigSender;
import com.allinpokers.yunying.appmessage.rabbitmq.sender.AppMessageSender;
import com.allinpokers.yunying.appmessage.rabbitmq.sender.bean.TemplateMessage;
import com.allinpokers.yunying.appmessage.service.AppMessageTplService;
import com.allinpokers.yunying.appmessage.utils.AppMessageConstants;
import com.allinpokers.yunying.appmessage.utils.MapBuilder;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AppMessageTplServiceImpl implements AppMessageTplService {


    @Resource
    private AppMessageTplMapper appMessageTplMapper;

    /**
     * 查询消息模板列表，支持分页和筛选
     */
    @Override
    public PageInfo<AppMessageTpl> getMessageTplList(AppMessageTplQuery query) {
        // 设置分页参数
        // 默认分页为1，每页20条
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }
        if (query.getPageSize() == null) {
            query.setPageSize(20);
        }
        PageHelper.startPage(query.getPageNum(), query.getPageSize());

        // 查询模板列表
        List<AppMessageTpl> tplList = appMessageTplMapper.getMessageTplList(query);

//        log.info("查询消息模板列表，总数：{}", tplList.size());
//        log.info("查询消息模板查询结果：{}", tplList);

        return new PageInfo<>(tplList);
    }


    /**
     * 获取消息模板的总数，支持筛选
     */
    @Override
    public long countMessageTpl(AppMessageTplQuery query) {
        return appMessageTplMapper.countMessageTpl(query);
    }

    /**
     * 获取单个模板详情
     */
    @Override
    public AppMessageTpl getMessageTplById(Long tplId) {
        return appMessageTplMapper.getMessageTplById(tplId);
    }

    /**
     * 新增模板
     */
    @Override
    public void addMessageTpl(AppMessageTpl messageTpl) {
        appMessageTplMapper.addMessageTpl(messageTpl);
    }

    /**
     * 更新模板
     */
    @Override
    public void updateMessageTpl(AppMessageTpl messageTpl) {
        appMessageTplMapper.updateMessageTpl(messageTpl);
    }

    /**
     * 删除模板
     */
    @Override
    public void deleteMessageTpl(Long tplId) {
        // 查询模板
        AppMessageTpl tpl = appMessageTplMapper.getMessageTplById(tplId);
        if (tpl == null) {
            throw new RuntimeException("模板不存在");
        }
        appMessageTplMapper.deleteMessageTpl(tplId);
    }


    @Resource
    AppMessageSender appMessageSender;


    public Map<String, Object> convertMockParamsToMap(String mockParams) {
        if (mockParams == null || mockParams.isEmpty()) {
            return new HashMap<>();
        }
        JSONObject jsonObject = JSONObject.parseObject(mockParams);
        return jsonObject.getInnerMap();
    }


    @Override
    public void mockSend(AppMessageTpl messageTpl) {
        // 先查询用户，判断user是否存在
        List<String> receiverUserIdsList = Arrays.asList(messageTpl.getReceiverUserIds().split(","));
        // 转换模板参数
        Map<String, Object> params = convertMockParamsToMap(messageTpl.getMockParams());
        // 直接迭代发送
        for (String receiverUserId : receiverUserIdsList) {
            // 发送模板消息
            appMessageSender.sendTemplateMessage(TemplateMessage.builder()
                    .tplCode(messageTpl.getTplCode())
                    .params(params)
                    .senderId(BigInteger.valueOf(messageTpl.getSenderId()))
                    .receiverUserId(BigInteger.valueOf(Long.parseLong(receiverUserId)))
                    .build());

        }
    }


}
