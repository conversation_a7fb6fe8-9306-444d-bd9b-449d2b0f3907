package com.allinpokers.yunying.promotion.dao;


import com.allinpokers.yunying.promotion.bean.*;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * PromotionChannelsDao
 *
 * <AUTHOR>
 * @since 2025/8/8
 */
@Mapper
public interface PromotionChannelsDao {

    @Select({
            "<script>",
            "SELECT ",
            "COALESCE(COUNT(DISTINCT CASE WHEN pcdu.login_count > 0 THEN pcdu.user_id END), 0) AS login_user_count, ",
            "COALESCE(COUNT(DISTINCT CASE WHEN pcdu.active_count > 0 THEN pcdu.user_id END), 0) AS active_user_count, ",
            "COALESCE(COUNT(DISTINCT CASE WHEN pcdu.diamond_recharge_count > 0 THEN pcdu.user_id END), 0) AS diamond_recharge_user_count, ",
            "COALESCE(COUNT(DISTINCT CASE WHEN pcdu.chip_recharge_count > 0 THEN pcdu.user_id END), 0) AS chip_recharge_user_count ",
            "FROM promotion_channels_daily_user pcdu ",
            "WHERE pcdu.user_id IN ",
            "<foreach collection='userIds' item='id' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "AND pcdu.channel_id = #{channelId} ",
            "AND pcdu.count_day = #{countDay}",
            "</script>"
    })
    PromotionChannelsDailyCount countUserStats(@Param("userIds") List<Long> userIds,
                                               @Param("channelId") Long channelId,
                                               @Param("countDay") Integer countDay);

    @Select({
            "<script>",
            "SELECT ",
            "COALESCE(COUNT(DISTINCT CASE WHEN pcdu.login_count > 0 THEN pcdu.user_id END), 0) AS login_user_count, ",
            "COALESCE(COUNT(DISTINCT CASE WHEN pcdu.active_count > 0 THEN pcdu.user_id END), 0) AS active_user_count, ",
            "COALESCE(COUNT(DISTINCT CASE WHEN pcdu.diamond_recharge_count > 0 THEN pcdu.user_id END), 0) AS diamond_recharge_user_count, ",
            "COALESCE(COUNT(DISTINCT CASE WHEN pcdu.chip_recharge_count > 0 THEN pcdu.user_id END), 0) AS chip_recharge_user_count ",
            "FROM promotion_channels_daily_user pcdu ",
            "WHERE pcdu.user_id IN ",
            "<foreach collection='userIds' item='id' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "AND pcdu.channel_id = #{channelId} ",
            "AND pcdu.count_day BETWEEN #{startCountDay} AND #{endCountDay}",
            "</script>"
    })
    PromotionChannelsDailyCount countUserStatsBetween(@Param("userIds") List<Long> userIds,
                                               @Param("channelId") Long channelId,
                                               @Param("startCountDay") Integer startCountDay,
                                               @Param("endCountDay") Integer endCountDay);


    @Select(
            "SELECT COALESCE(COUNT(DISTINCT user_id),0) FROM user_register_channel " +
            "WHERE channel_id = #{channelId} AND register_day = #{registerDay};"
    )
    Long countRegisterUser(@Param("channelId") Long channelId,
                           @Param("registerDay") Integer registerDay);

    /**
     * 获取推广渠道列表
     */
    @Select({
            "<script>",
            "select urc.* from user_register_channel urc ",
            "WHERE 1=1",
            "<if test='channelId != null'>",
            "AND urc.channel_id = #{channelId}",
            "</if>",
            "<if test='userId != null'>",
            "AND urc.user_id = #{userId}",
            "</if>",
            "<if test='registerDay != null'>",
            "AND urc.register_day = #{registerDay}",
            "</if>",
            "</script>"
    })
    List<UserRegisterChannel> findUserRegisterChannelAll(PromotionChannelsQuery query);

    /**
     * 获取推广渠道列表
     */
    @Select({
            "<script>",
            "SELECT",
            "  pcd.unique_id,",
            "  pcd.channel_id,",
            "  pc.code,",
            "  pc.name,",
            "  pcd.count_day,",
            "  pcd.year,",
            "  pcd.month,",
            "  pcd.day,",
            "  IFNULL(pcd.register_user_count, 0) AS register_user_count,",
            "  IFNULL(pcd.login_user_count, 0) AS login_user_count,",
            "  IFNULL(pcd.active_user_count, 0) AS active_user_count,",
            "  IFNULL(pcd.chip_recharge_user_count, 0) AS chip_recharge_user_count,",
            "  IFNULL(pcd.chip_recharge_count, 0) AS chip_recharge_count,",
            "  IFNULL(pcd.chip_recharge_amount, 0) AS chip_recharge_amount,",
            "  IFNULL(pcd.chip_withdraw_user_count, 0) AS chip_withdraw_user_count,",
            "  IFNULL(pcd.chip_withdraw_count, 0) AS chip_withdraw_count,",
            "  IFNULL(pcd.chip_withdraw_amount, 0) AS chip_withdraw_amount,",
            "  IFNULL(pcd.diamond_recharge_user_count, 0) AS diamond_recharge_user_count,",
            "  IFNULL(pcd.diamond_recharge_count, 0) AS diamond_recharge_count,",
            "  IFNULL(pcd.diamond_recharge_amount, 0) AS diamond_recharge_amount,",
            "  IFNULL(pcd.gold_recharge_user_count, 0) AS gold_recharge_user_count,",
            "  IFNULL(pcd.gold_recharge_count, 0) AS gold_recharge_count,",
            "  IFNULL(pcd.gold_recharge_amount, 0) AS gold_recharge_amount",
            "FROM crazy_poker.promotion_channels_daily pcd",
            "LEFT JOIN crazy_poker.promotion_channels pc ON pcd.channel_id = pc.id",
            "WHERE 1=1",
            "<if test='startCountDay != null and endCountDay != null'>",
            "  AND pcd.count_day BETWEEN #{startCountDay} AND #{endCountDay}",
            "</if>",
            "<if test='channelId != null'>",
            "  AND pcd.channel_id = #{channelId}",
            "</if>",
            "<if test='code != null and code != \"\"'>",
            "  AND pc.code LIKE CONCAT('%', #{code}, '%')",
            "</if>",
            "<if test='name != null and name != \"\"'>",
            "  AND pc.name LIKE CONCAT('%', #{name}, '%')",
            "</if>",
            "<if test='keyword != null and keyword != \"\"'>",
            "  AND (pc.name LIKE CONCAT('%', #{keyword}, '%') OR pc.code LIKE CONCAT('%', #{keyword}, '%'))",
            "</if>",
            "ORDER BY pcd.count_day DESC",
            "</script>"
    })
    List<PromotionChannelsDaily> findDailyAll(PromotionChannelsQuery query);

    /**
     * 获取推广渠道总数
     */
    @Select({
            "<script>",
            "select count(pcd.id) from promotion_channels_daily pcd ",
            "LEFT JOIN promotion_channels pc ON pcd.channel_id = pc.id ",
            "WHERE 1=1",
            "<if test='channelId != null'>",
            "AND pcd.channel_id = #{channelId}",
            "</if>",
            "<if test='code != null and code != \"\"'>",
            "AND pc.code LIKE CONCAT('%', #{code}, '%')",
            "</if>",
            "<if test='name != null and name != \"\"'>",
            "AND pc.name LIKE CONCAT('%', #{name}, '%')",
            "</if>",
            "<if test='startCountDay != null and endCountDay != null'>",
            "pcd.count_day BETWEEN #{startCountDay} AND #{endCountDay}",
            "</if>",
            "</script>"
    })
    long countDailyAll(PromotionChannelsQuery query);

    /**
     * 获取推广渠道列表
     */
    @Select({
            "<script>",
            "SELECT * FROM promotion_channels",
            "WHERE 1=1",
            "<if test='code != null and code != \"\"'>",
            "AND code = #{code}",
            "</if>",
            "<if test='name != null and name != \"\"'>",
            "AND name LIKE CONCAT('%', #{name}, '%')",
            "</if>",
            "<if test='status != null'>",
            "AND status = #{status}",
            "</if>",
            "<if test='valid != null'>",
            "AND valid = #{valid}",
            "</if>",
            "<if test='keyword != null and keyword != \"\"'>",
            "  AND (name LIKE CONCAT('%', #{keyword}, '%') OR code LIKE CONCAT('%', #{keyword}, '%'))",
            "</if>",
            "ORDER BY created_at DESC",
            "</script>"
    })
    List<PromotionChannels> findAll(PromotionChannelsQuery query);

    /**
     * 获取推广渠道总数
     */
    @Select({
            "<script>",
            "SELECT COUNT(id) FROM promotion_channels",
            "WHERE 1=1",
            "<if test='code != null and code != \"\"'>",
            "AND code = #{code}",
            "</if>",
            "<if test='name != null and name != \"\"'>",
            "AND name LIKE CONCAT('%', #{name}, '%')",
            "</if>",
            "<if test='status != null'>",
            "AND status = #{status}",
            "</if>",
            "<if test='valid != null'>",
            "AND valid = #{valid}",
            "</if>",
            "</script>"
    })
    long countAll(PromotionChannelsQuery query);

    /**
     * 获取单个渠道详情
     */
    @Select("SELECT * FROM promotion_channels WHERE id = #{id}")
    PromotionChannels findById(Long id);

    /**
     * 新增渠道
     */
    @Insert("INSERT INTO promotion_channels (code, name, download_url, token, created_by) " +
            "VALUES (#{code}, #{name}, #{downloadUrl}, #{token}, #{createdBy})")
    void add(PromotionChannels promotionChannels);

    /**
     * 更新渠道
     */
    @Update("UPDATE promotion_channels SET name = #{name}, download_url = #{downloadUrl} WHERE id = #{id}")
    void update(PromotionChannels promotionChannels);

    @Select("select concat(ppd.domain, (select ppp.uri from poker_pages_path ppp where ppp.code = 'PROMOTION_REGISTER')) from poker_pages_domain ppd where ppd.valid = 1 limit 1;")
    String findRegisterUrl();

    /**
     * 删除渠道
     */
    @Update("UPDATE promotion_channels SET valid = 0 WHERE id = #{id}")
    void delete(Long id);

    @Select({
            "<script>",
            "SELECT COUNT(id) FROM promotion_channels",
            "WHERE 1=1 AND valid = 1",
            "<if test='code != null and code != \"\"'>",
            "AND code = #{code}",
            "</if>",
            "<if test='token != null and token != \"\"'>",
            "AND token = #{token}",
            "</if>",
            "<if test='name != null and name != \"\"'>",
            "AND name = #{name}",
            "</if>",
            "<if test='ignoreId != null'>",
            "AND id != #{ignoreId}",
            "</if>",
            "</script>"
    })
    Long isExist(PromotionChannelsQuery query);


    @Select("select ppd.domain from poker_pages_domain ppd where ppd.valid = 1 limit 1;")
    String findValidDomain();

    @Select("select ppp.uri from poker_pages_path ppp where ppp.code = 'PROMOTION_REGISTER' limit 1;")
    String findRegisterPath();

    @Update("update poker_pages_domain set domain = #{domain} where valid = 1;")
    void saveDomain(@Param("domain") String domain);

    @Update("update poker_pages_path set uri = #{registerPath} where code = 'PROMOTION_REGISTER';")
    void saveRegisterPath(@Param("registerPath") String registerPath);

}
