package com.allinpokers.yunying.appmessage.send;

import com.allinpokers.yunying.appmessage.rabbitmq.sender.AppMessageSender;
import com.allinpokers.yunying.appmessage.rabbitmq.sender.bean.TemplateMessage;
import com.allinpokers.yunying.appmessage.send.bean.*;
import com.allinpokers.yunying.appmessage.utils.AppMessageConstants;
import com.allinpokers.yunying.appmessage.utils.MapBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;

@Component
public class AppBusinessMessageSender {

    @Resource
    AppMessageSender appMessageSender;

    /**
     * 系统发送者ID，0表示系统
     */
    private static final BigInteger SYSTEM_SENDER_ID = BigInteger.valueOf(0);

    /**
     * 发送俱乐部创建成功消息
     * @param clubCreation 俱乐部创建信息
     */

    public void notifyClubCreationSuccessful(ClubCreation clubCreation) {
        // 发送用户加入俱乐部成功消息
        appMessageSender.sendTemplateMessage(TemplateMessage.builder()
                .tplCode(AppMessageConstants.TplCode.CLUB0007)
                .params(MapBuilder.builder()
                        .put("clubName", clubCreation.getClubName())
                        .put("clubId", clubCreation.getClubId())
                        .put("userId", clubCreation.getUserId())
                        .build())
                .senderId(SYSTEM_SENDER_ID)
                .receiverUserId(BigInteger.valueOf(clubCreation.getUserId()))
                .build());

        // 判断是否有联盟ID，有联盟ID则给联盟主发送消息
        if (clubCreation.getTribeId() != null) {
            // 发送用户加入联盟成功消息
            notifyJoinTribeSuccessful(JoinTribe.builder()
                    .clubId(clubCreation.getClubId())
                    .clubName(clubCreation.getClubName())
                    .clubOwnerId(clubCreation.getUserId())
                    .tribeId(clubCreation.getTribeId())
                    .tribeName(clubCreation.getTribeName())
                    .tribeOwnerId(clubCreation.getTribeOwnerId())
                    .build());
        }
    }

    /**
     * 发送俱乐部创建失败消息
     * @param clubCreation 俱乐部创建信息
     */
    public void notifyClubCreationFailed(ClubCreation clubCreation) {
        // 发送用户加入俱乐部失败消息
        appMessageSender.sendTemplateMessage(TemplateMessage.builder()
                .tplCode(AppMessageConstants.TplCode.CLUB0008)
                .params(MapBuilder.builder()
                        .put("clubName", clubCreation.getClubName())
                        .put("userId", clubCreation.getUserId())
                        .build())
                .senderId(SYSTEM_SENDER_ID)
                .receiverUserId(BigInteger.valueOf(clubCreation.getUserId()))
                .build());
    }

    /**
     * 发送手动充值钻石成功消息
     * @param manualRechargeDiamonds 手动充值钻石信息
     */
    public void notifyManualRechargeDiamondsSuccessful(ManualRechargeDiamonds manualRechargeDiamonds) {
        // 发送手动充值钻石成功消息
        appMessageSender.sendTemplateMessage(TemplateMessage.builder()
                .tplCode(AppMessageConstants.TplCode.WALLET0001)
                .params(MapBuilder.builder()
                        .put("userId", manualRechargeDiamonds.getUserId())
                        // 由于大数额会变成科学计数法，这里需要转类型
                        .put("diamondAmount", BigDecimal.valueOf(manualRechargeDiamonds.getDiamondAmount()).toPlainString())
                        .build())
                .senderId(SYSTEM_SENDER_ID)
                .receiverUserId(BigInteger.valueOf(manualRechargeDiamonds.getUserId()))
                .build());
    }

    /**
     * 发送手动充值钻石失败消息
     * @param tribeCreation 创建联盟信息
     */
    public void notifyTribeCreationSuccessful(TribeCreation tribeCreation) {
        // 发送联盟创建成功消息
        appMessageSender.sendTemplateMessage(TemplateMessage.builder()
                .tplCode(AppMessageConstants.TplCode.TRIBE0001)
                .params(MapBuilder.builder()
                        .put("tribeName", tribeCreation.getTribeName())
                        .put("tribeId", tribeCreation.getTribeId())
                        .put("userId", tribeCreation.getUserId())
                        .build())
                .senderId(SYSTEM_SENDER_ID)
                .receiverUserId(BigInteger.valueOf(tribeCreation.getUserId()))
                .build());
    }

    /**
     * 发送联盟创建失败消息
     * @param tribeCreation 创建联盟信息
     */
    public void notifyTribeCreationFailed(TribeCreation tribeCreation) {
        // 发送联盟创建失败消息
        appMessageSender.sendTemplateMessage(TemplateMessage.builder()
                .tplCode(AppMessageConstants.TplCode.TRIBE0002)
                .params(MapBuilder.builder()
                        .put("tribeName", tribeCreation.getTribeName())
                        .put("userId", tribeCreation.getUserId())
                        .build())
                .senderId(SYSTEM_SENDER_ID)
                .receiverUserId(BigInteger.valueOf(tribeCreation.getUserId()))
                .build());
    }


    /**
     * 发送俱乐部加入联盟成功消息
     * @param joinTribe 加入联盟信息
     */
    public void notifyJoinTribeSuccessful(JoinTribe joinTribe) {
        // 发送加入联盟成功消息给俱乐部主
        appMessageSender.sendTemplateMessage(TemplateMessage.builder()
                .tplCode(AppMessageConstants.TplCode.TRIBE0005)
                .params(MapBuilder.builder()
                        .put("clubName", joinTribe.getClubName())
                        .put("clubId", joinTribe.getClubId())
                        .put("clubOwnerId", joinTribe.getClubOwnerId())
                        .put("tribeName", joinTribe.getTribeName())
                        .put("tribeId", joinTribe.getTribeId())
                        .put("tribeOwnerId", joinTribe.getTribeOwnerId())
                        .build())
                .senderId(SYSTEM_SENDER_ID)
                .receiverUserId(BigInteger.valueOf(joinTribe.getClubOwnerId()))
                .build());

        // 发送俱乐部加入联盟消息给联盟主
        appMessageSender.sendTemplateMessage(TemplateMessage.builder()
                .tplCode(AppMessageConstants.TplCode.TRIBE0003)
                .params(MapBuilder.builder()
                        .put("clubName", joinTribe.getClubName())
                        .put("clubId", joinTribe.getClubId())
                        .put("clubOwnerId", joinTribe.getClubOwnerId())
                        .put("tribeName", joinTribe.getTribeName())
                        .put("tribeId", joinTribe.getTribeId())
                        .put("tribeOwnerId", joinTribe.getTribeOwnerId())
                        .build())
                .senderId(SYSTEM_SENDER_ID)
                .receiverUserId(BigInteger.valueOf(joinTribe.getTribeOwnerId()))
                .build());

    }


    /**
     * 发送俱乐部加入联盟失败消息
     * @param joinTribe 加入联盟信息
     */
    public void notifyJoinTribeFailed(JoinTribe joinTribe) {
        // 发送加入联盟失败消息给俱乐部主
        appMessageSender.sendTemplateMessage(TemplateMessage.builder()
                .tplCode(AppMessageConstants.TplCode.TRIBE0007)
                .params(MapBuilder.builder()
                        .put("clubName", joinTribe.getClubName())
                        .put("clubId", joinTribe.getClubId())
                        .put("clubOwnerId", joinTribe.getClubOwnerId())
                        .put("tribeName", joinTribe.getTribeName())
                        .put("tribeId", joinTribe.getTribeId())
                        .put("tribeOwnerId", joinTribe.getTribeOwnerId())
                        .build())
                .senderId(SYSTEM_SENDER_ID)
                .receiverUserId(BigInteger.valueOf(joinTribe.getClubOwnerId()))
                .build());
    }


    public void sendDiamondRecharge(DiamondRecharge diamondRecharge) {
        // 发送加入联盟失败消息给俱乐部主
        appMessageSender.sendTemplateMessage(TemplateMessage.builder()
                .tplCode(AppMessageConstants.TplCode.WALLET0003)
                .params(MapBuilder.builder()
                        .put("orderId", diamondRecharge.getOrderId())
                        .put("userId", diamondRecharge.getUserId())
                        .put("clubId", diamondRecharge.getClubId())
                        .put("diamondAmount", BigDecimal.valueOf(diamondRecharge.getDiamondAmount() / 100))
                        .put("timestamp", diamondRecharge.getTimestamp())
                        .build())
                .senderId(SYSTEM_SENDER_ID)
                .receiverUserId(BigInteger.valueOf(diamondRecharge.getUserId()))
                .build());
    }

}
