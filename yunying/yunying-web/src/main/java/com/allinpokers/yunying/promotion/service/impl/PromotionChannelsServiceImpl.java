package com.allinpokers.yunying.promotion.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.allinpokers.yunying.promotion.bean.*;
import com.allinpokers.yunying.promotion.dao.PromotionChannelsDao;
import com.allinpokers.yunying.promotion.service.PromotionChannelsService;
import com.allinpokers.yunying.util.RandomBase64Utils;
import com.allinpokers.yunying.util.UUIDGenerator;
import com.allinpokers.yunying.util.UrlParamBuilder;
import com.allinpokers.yunying.util.UrlUtils;
import com.allinpokers.yunying.util.excel.ExcelModel;
import com.allinpokers.yunying.util.excel.ExcelRow;
import com.allinpokers.yunying.util.excel.ExcelSheet;
import com.allinpokers.yunying.util.excel.ExcelUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PromotionChannelsServiceImpl
 *
 * <AUTHOR>
 * @since 2025/8/8
 */
@Slf4j
@Service
public class PromotionChannelsServiceImpl implements PromotionChannelsService {

    @Resource
    PromotionChannelsDao promotionChannelsDao;

    @Override
    public JSONObject findRegisterDomain() {
        JSONObject json = new JSONObject();
        json.put("domain", promotionChannelsDao.findValidDomain());
        json.put("path", promotionChannelsDao.findRegisterPath());
        return json;
    }

    @Override
    public void saveRegisterDomain(PromotionChannelsQuery query) {
        if (query.getDomain() != null) {
            // 保存域名
            String domain = UrlUtils.fixUrl(query.getDomain(), UrlUtils.Protocol.HTTPS);
            promotionChannelsDao.saveDomain(domain);
        }

        if (query.getRegisterPath() != null) {
            // 保存注册路径
            String registerPath = query.getRegisterPath();
            if (!registerPath.startsWith("/")) {
                registerPath = "/" + registerPath;
            }
            promotionChannelsDao.saveRegisterPath(registerPath);
        }
    }

    @Override
    public PageInfo<PromotionChannels> list(PromotionChannelsQuery query) {
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }
        if (query.getPageSize() == null) {
            query.setPageSize(20);
        }
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<PromotionChannels> list = promotionChannelsDao.findAll(query);

        // 处理扩展数据
        if (!CollectionUtils.isEmpty(list)) {
            queryExtend(list);
        }

        return new PageInfo<>(list);
    }

    /**
     * 处理扩展数据
     * @param list
     */
    private void queryExtend(List<PromotionChannels> list) {
        // 查询注册地址
        String registerUrl = promotionChannelsDao.findRegisterUrl();
        if (StringUtils.isEmpty(registerUrl)) {
            return;
        }
        registerUrl = UrlUtils.fixUrl(registerUrl, UrlUtils.Protocol.HTTPS);
        for (PromotionChannels channels : list) {
            channels.setRegisterUrl(new UrlParamBuilder(registerUrl, 1024)
                    .addParam("t", channels.getToken())
                    .build());
        }
    }

    @Override
    public long count(PromotionChannelsQuery query) {
        return promotionChannelsDao.countAll(query);
    }

    @Override
    public PromotionChannels get(Long id) {
        return promotionChannelsDao.findById(id);
    }

    @Override
    public void add(PromotionChannels promotionChannels) {
        // 插入的时候，需要生成一个token值
        String token = nextToken();
        promotionChannels.setToken(token);
        promotionChannelsDao.add(promotionChannels);
    }

    @Override
    public void update(PromotionChannels promotionChannels) {
        promotionChannelsDao.update(promotionChannels);
    }

    @Override
    public void delete(Long id) {
        promotionChannelsDao.delete(id);
    }

    @Override
    public Boolean isExist(PromotionChannelsQuery query) {
        return promotionChannelsDao.isExist(query) > 0L;
    }

    @Override
    public List<PromotionChannelsDaily> countList(PromotionChannelsQuery query) {
        // 先按照关键字查询渠道
        List<PromotionChannels> channels = promotionChannelsDao.findAll(PromotionChannelsQuery.builder()
                .keyword(query.getKeyword())
                .build());

        // 查询现有数据
        List<PromotionChannelsDaily> dailies = promotionChannelsDao.findDailyAll(query);
        if (!CollectionUtils.isEmpty(dailies)) {
            for (PromotionChannelsDaily daily : dailies) {
                // 计算扩展数据
                countExtend(daily);
            }
        }
        log.info("dailies.size() = {}", dailies.size());

        // 根据开始时间与结束时间创建一堆0值的数据
        return createDefaultDailies(channels, dailies, query.getStartCountDay(), query.getEndCountDay());
    }

    /**
     * 创建默认的推广渠道统计数据
     * @param channels
     * @param dailies
     * @param startDay
     * @param endDay
     * @return
     */
    private List<PromotionChannelsDaily> createDefaultDailies(List<PromotionChannels> channels, List<PromotionChannelsDaily> dailies, Integer startDay, Integer endDay) {
        List<PromotionChannelsDaily> list = new ArrayList<>();

        Map<String, PromotionChannelsDaily> map = new HashMap<>();
        dailies.forEach(daily -> {
            String uniqueId = daily.getUniqueId();
            map.put(uniqueId, daily);
        });

        // 根据startDay 和 endDay创建日期范围
        List<Integer> dateRange = getDateRange(startDay, endDay);
        log.info("dateRange = {}", JSONObject.toJSONString(dateRange));
        dateRange.forEach(day -> {
            for (PromotionChannels channel : channels) {
                // YYYYMMDD:channel_id
                String uniqueId = day + ":" + channel.getId();
                log.info("uniqueId = {}", uniqueId);
                PromotionChannelsDaily daily = map.get(uniqueId);
                if (daily == null) {
                    // 没有找到对应的记录，创建一个新的，全为0的数据
                    daily = new PromotionChannelsDaily();
                    daily.setCountDay(day);
                    daily.setCountDayFmt(countDayFmt(day));
                    daily.setChannelId(channel.getId());
                    daily.setCode(channel.getCode());
                    daily.setName(channel.getName());
                    daily.setUniqueId(uniqueId);
                    daily.setRegisterUserCount(0);
                    daily.setLoginUserCount(0);
                    daily.setRegisterNextDayLoginCount(0);
                    daily.setRegisterDayThreeLoginCount(0);
                    daily.setRegisterDiamondRechargeCount(0);
                    daily.setRegisterChipRechargeCount(0);
                    daily.setIsChipRecharge(false);
                    daily.setIsDiamondRecharge(false);
                    list.add(daily);
                } else {
                    // 找到了对应的记录，直接添加到列表中
                    list.add(daily);
                }
            }
        });

        // 按照日期排序, 从大到小
        list.sort((a, b) -> b.getCountDay().compareTo(a.getCountDay()));

        return list;
    }

    private List<Integer> getDateRange(Integer startDay, Integer endDay) {
        List<Integer> days = new ArrayList<>();
        if (startDay == null || endDay == null) return days;
        String startStr = String.valueOf(startDay);
        String endStr = String.valueOf(endDay);
        if (startStr.length() != 8 || endStr.length() != 8) return days;

        java.time.LocalDate start = java.time.LocalDate.of(
                Integer.parseInt(startStr.substring(0, 4)),
                Integer.parseInt(startStr.substring(4, 6)),
                Integer.parseInt(startStr.substring(6, 8))
        );
        java.time.LocalDate end = java.time.LocalDate.of(
                Integer.parseInt(endStr.substring(0, 4)),
                Integer.parseInt(endStr.substring(4, 6)),
                Integer.parseInt(endStr.substring(6, 8))
        );

        for (java.time.LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
            days.add(Integer.parseInt(date.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"))));
        }
        return days;
    }

    /**
     * 生成一个新的唯一token
     * @return
     */
    public String nextToken() {
        int max = 5000;
        for (int i = 0; i < max; i++) {
            // 优先用短token
            String token = RandomBase64Utils.generateEncodedNumber();
            // 检查是否已存在
            PromotionChannelsQuery query = new PromotionChannelsQuery();
            query.setToken(token);
            if (!isExist(query)) {
                return token;
            }
        }
        return UUIDGenerator.next();
    }

    @Override
    public void countExport(PromotionChannelsQuery query, HttpServletResponse response) {

        List<PromotionChannelsDaily> list = countList(query);

        ExcelSheet sheet = new ExcelSheet("推广统计");
        List<ExcelModel> models = new ArrayList<>();
        models.add(ExcelModel.builder()
                .rows(Lists.newArrayList(new ExcelRow().add("记录总数：").add(list.size())))
                .afterBlankLine(1)
                .build());


        String[] diamondTitle = new String[]{"日期", "渠道号", "渠道名称", "注册人数", "登录人数", "次日存留", "三日存留", "购买钻石人数", "购买联盟币人数"};
        List<ExcelRow> clubRows = new ArrayList<>();
        for (PromotionChannelsDaily info : list) {
            ExcelRow row = new ExcelRow()
                    .add(countDayFmt(info.getCountDay()))
                    .add(info.getCode())
                    .add(info.getName())
                    .add(info.getRegisterUserCount())
                    .add(info.getLoginUserCount())
                    .add(info.getRegisterNextDayLoginCount())
                    .add(info.getRegisterDayThreeLoginCount())
                    .add(info.getRegisterDiamondRechargeCount())
                    .add(info.getRegisterChipRechargeCount());
            clubRows.add(row);
        }

        models.add(ExcelModel.builder()
                .titles(Lists.newArrayList(diamondTitle))
                .rows(clubRows)
                .afterBlankLine(1)
                .build());

        sheet.setModels(models);

        try {
            String fileName = URLEncoder.encode(sheet.getName(), "UTF-8");
            response.setHeader("Content-Type", "application/octet-stream");
            response.setHeader("Content-Disposition", String.format("attachment;fileName=\"%s.%s\"", fileName, "xlsx"));
            ExcelUtils.write(response.getOutputStream(), sheet);
        } catch (Exception e) {
            throw new RuntimeException("导出失败");
        }
    }


    /**
     * 格式化统计日期，yyyyMMdd => yyyy-MM-dd
     * @param countDay
     * @return
     */
    private String countDayFmt(Integer countDay) {
        if (countDay == null) {
            return "";
        }
        String s = String.valueOf(countDay);
        if (s.length() != 8) {
            return s;
        }
        return s.substring(0, 4) + "-" + s.substring(4, 6) + "-" + s.substring(6);
    }

    /**
     * 计算扩展数据
     * @param promotionChannelsDaily
     */
    public void countExtend(PromotionChannelsDaily promotionChannelsDaily) {

        // 格式化日期
        promotionChannelsDaily.setCountDayFmt(countDayFmt(promotionChannelsDaily.getCountDay()));

        // 查询注册的用户
        List<UserRegisterChannel> users = promotionChannelsDao.findUserRegisterChannelAll(PromotionChannelsQuery.builder()
                .channelId(promotionChannelsDaily.getChannelId())
                .registerDay(promotionChannelsDaily.getCountDay())
                .build());

        // 获取所有userId, 用于后续查询, string类型
        List<Long> userIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(users)) {
            for (UserRegisterChannel user : users) {
                if (user.getUserId() != null) {
                    userIds.add(user.getUserId());
                }
            }
        }
        if (CollectionUtils.isEmpty(userIds)) {
            // 没有注册用户，直接返回
            promotionChannelsDaily.setLoginUserCount(0);
            promotionChannelsDaily.setRegisterNextDayLoginCount(0);
            promotionChannelsDaily.setRegisterDayThreeLoginCount(0);
            promotionChannelsDaily.setRegisterDiamondRechargeCount(0);
            promotionChannelsDaily.setRegisterChipRechargeCount(0);
            promotionChannelsDaily.setIsChipRecharge(false);
            promotionChannelsDaily.setIsDiamondRecharge(false);
            return;
        }

        // 设置注册用户数量
        promotionChannelsDaily.setRegisterUserCount(userIds.size());

        // 需要统计的日子
        // 1. 注册当天
        Integer registerDay = promotionChannelsDaily.getCountDay();
        Long channelId = promotionChannelsDaily.getChannelId();

        // 获取注册当天的统计
        PromotionChannelsDailyCount registerDayCount = promotionChannelsDao.countUserStats(userIds, channelId, registerDay);

        // 统计当天的结果
        if (registerDayCount != null) {
            promotionChannelsDaily.setLoginUserCount(registerDayCount.getLoginUserCount());
            promotionChannelsDaily.setActiveUserCount(registerDayCount.getActiveUserCount());
        }

        // 2. 注册后第二天
        Integer nextDay = getDayOfNum(registerDay, 1);
        PromotionChannelsDailyCount nextDayCount = promotionChannelsDao.countUserStats(userIds, channelId, nextDay);
        if (nextDayCount != null) {
            promotionChannelsDaily.setRegisterNextDayLoginCount(nextDayCount.getLoginUserCount());
        }

        // 3. 注册后第三天
        Integer thirdDay = getDayOfNum(registerDay, 2);
        PromotionChannelsDailyCount thirdDayCount = promotionChannelsDao.countUserStats(userIds, channelId, thirdDay);
        if (thirdDayCount != null) {
            promotionChannelsDaily.setRegisterDayThreeLoginCount(thirdDayCount.getLoginUserCount());
        }

        // 计算三天内的充值用户
        PromotionChannelsDailyCount promotionChannelsDailyCount = promotionChannelsDao.countUserStatsBetween(userIds, channelId, registerDay, thirdDay);
        promotionChannelsDaily.setRegisterDiamondRechargeCount(promotionChannelsDailyCount.getDiamondRechargeUserCount());
        promotionChannelsDaily.setRegisterChipRechargeCount(promotionChannelsDailyCount.getChipRechargeUserCount());
        promotionChannelsDaily.setIsDiamondRecharge(promotionChannelsDailyCount.getDiamondRechargeUserCount() > 0);
        promotionChannelsDaily.setIsChipRecharge(promotionChannelsDailyCount.getChipRechargeUserCount() > 0);

    }

    /**
     * 获取某天的前后N天
     * @param day 20230808
     * @param num 正数表示后N天，负数表示前N天
     * @return
     */
    public int getDayOfNum(Integer day, Integer num) {
        if (day == null || num == null) return 0;
        String dayStr = String.valueOf(day);
        if (dayStr.length() != 8) return 0;
        int year = Integer.parseInt(dayStr.substring(0, 4));
        int month = Integer.parseInt(dayStr.substring(4, 6));
        int dayOfMonth = Integer.parseInt(dayStr.substring(6, 8));
        java.time.LocalDate localDate = java.time.LocalDate.of(year, month, dayOfMonth);
        localDate = localDate.plusDays(num);
        return Integer.parseInt(localDate.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd")));
    }

}
