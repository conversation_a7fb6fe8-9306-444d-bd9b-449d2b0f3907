package com.allinpokers.yunying.promotion.service;


import com.alibaba.fastjson.JSONObject;
import com.allinpokers.yunying.promotion.bean.PromotionChannels;
import com.allinpokers.yunying.promotion.bean.PromotionChannelsDaily;
import com.allinpokers.yunying.promotion.bean.PromotionChannelsQuery;
import com.github.pagehelper.PageInfo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * PromotionChannelsService
 *
 * <AUTHOR>
 * @since 2025/8/8
 */
public interface PromotionChannelsService {

    /**
     * 查询注册域名
     * @return
     */
    JSONObject findRegisterDomain();

    /**
     * 保存注册域名
     * @param query
     */
    void saveRegisterDomain(PromotionChannelsQuery query);

    /**
     * 查询
     */
    PageInfo<PromotionChannels> list(PromotionChannelsQuery query);

    /**
     * 统计
     */
    long count(PromotionChannelsQuery query);

    /**
     * 查询单个
     */
    PromotionChannels get(Long id);

    /**
     * 新增
     */
    void add(PromotionChannels promotionChannels);

    /**
     * 更新
     */
    void update(PromotionChannels promotionChannels);

    /**
     * 删除
     */
    void delete(Long id);

    /**
     * 检查渠道代码是否存在
     */
    Boolean isExist(PromotionChannelsQuery query);

    /**
     * 统计推广渠道数据
     * @param query
     * @return
     */
    List<PromotionChannelsDaily> countList(PromotionChannelsQuery query);

    /**
     * 导出推广渠道统计数据
     * @param query
     * @param response
     */
    void countExport(PromotionChannelsQuery query, HttpServletResponse response);

}
