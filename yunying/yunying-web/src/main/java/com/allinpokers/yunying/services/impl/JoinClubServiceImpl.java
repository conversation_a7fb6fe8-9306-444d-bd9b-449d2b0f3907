package com.allinpokers.yunying.services.impl;

import com.allinpokers.yunying.dao.crazypoker.ClubMembersDao;
import com.allinpokers.yunying.dao.crazypoker.ClubRecordDao;
import com.allinpokers.yunying.dao.crazypoker.JoinClubDao;
import com.allinpokers.yunying.dao.crazypoker.UserDetailsInfoDao;
import com.allinpokers.yunying.dao.crazypoker.ClubBlacklistDao;
import com.allinpokers.yunying.entity.crazypoker.ClubRecord;
import com.allinpokers.yunying.entity.crazypoker.UserDetailsInfo;
import com.allinpokers.yunying.entity.crazypoker.ClubBlacklist;
import com.allinpokers.yunying.entity.crazypoker.example.ClubBlacklistExample;
import com.allinpokers.yunying.entity.crazypoker.example.UserDetailsInfoExample;
import com.allinpokers.yunying.dao.crazypoker.TribeMembersDao;
import com.allinpokers.yunying.entity.crazypoker.TribeMembers;
import com.allinpokers.yunying.entity.crazypoker.example.TribeMembersExample;
import com.allinpokers.yunying.enu.ResponseCodeEnum;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.model.response.joinclub.BathJoinClubResp;
import com.allinpokers.yunying.model.response.joinclub.PromotorInfo;
import com.allinpokers.yunying.model.response.joinclub.PromotorsList;
import com.allinpokers.yunying.rabbitmq.client.bean.ClubMessage;
import com.allinpokers.yunying.rabbitmq.client.bean.MessageUnreadDetailBo;
import com.allinpokers.yunying.rabbitmq.constant.EMessageChannelCode;
import com.allinpokers.yunying.rabbitmq.constant.EMessageCode;
import com.allinpokers.yunying.services.ClubMembersService;
import com.allinpokers.yunying.services.ClubService;
import com.allinpokers.yunying.services.JoinClubService;
import com.allinpokers.yunying.services.JoinClubTransactionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.TextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
public class JoinClubServiceImpl implements JoinClubService {

    @Autowired
    private ClubService clubService;

    @Autowired
    private ClubMembersService clubMembersService;

    @Autowired
    private JoinClubTransactionService joinClubTransactionService;

    @Autowired
    private JoinClubDao joinClubDao;

    @Autowired
    private ClubRecordDao clubRecordDao;

    @Autowired
    private UserDetailsInfoDao userDetailsInfoDao;

    @Autowired
    private ClubBlacklistDao clubBlacklistDao;

    @Autowired
    private TribeMembersDao tribeMembersDao;

    @Override
    public CommonRespon<PromotorsList> queryPromotors(Long clubId, String search, Integer page, Integer size) {
        PromotorsList promotorsList = new PromotorsList();
        // 查询俱乐部名称
        Integer clubIdInt = Integer.parseInt(Long.toString(clubId));
        ClubRecord clubRecord = clubService.findByRandomId(clubIdInt);
        if(clubRecord == null) {
            return CommonRespon.failure(ResponseCodeEnum.CLUB_NOT_EXIST);
        }

        promotorsList.setClubId(clubId);
        promotorsList.setClubName(clubRecord.getName());

        // 查询俱乐部的推广员列表
        PageBean<PromotorInfo> promotorInfoPageBean = clubMembersService.queryPromotorsByClubId(Long.parseLong(clubRecord.getId() + ""), search, page, size);
        promotorsList.setPromotorInfos(promotorInfoPageBean);
        return CommonRespon.success(promotorsList);
    }

    /**
     * 批量加入俱乐部
     *
     * @param clubIdShow
     * @param promotorIdShow
     * @param userIds
     * @return
     */
    @Override
    public CommonRespon<BathJoinClubResp> batchJoinClub(Long clubIdShow, Long promotorIdShow, String userIds) {
        // 查询俱乐部
        ClubRecord clubRecord = clubRecordDao.queryClubRecordByRandomId(clubIdShow);
        if(clubRecord == null) {
            return CommonRespon.failure(ResponseCodeEnum.PROMOTER_ERROR);
        }

        // 判断promotorId是否正确
        UserDetailsInfo userDetailsInfo = joinClubDao.queryPromoterByRandomId(promotorIdShow);
        if(userDetailsInfo == null) {
            return CommonRespon.failure(ResponseCodeEnum.PROMOTER_ERROR);
        }
        Integer promoterId = userDetailsInfo.getUserId();

        // 获取用户id
        if(userIds == null || userIds.equals("")) {
            // 加入俱乐部玩家id为空
            return CommonRespon.failure(ResponseCodeEnum.USER_ERROR);
        }
        // 替换中文，为英文,
        userIds = userIds.trim();
        String split = ",";
        userIds.replaceAll("，", split);
        String[] userIdsArray = userIds.split(split);

        // 判断用户输入的用户id是否正确
        List<Long> userIdsList = new ArrayList<>();
        for(String userStr : userIdsArray) {
            try {
                userIdsList.add(Long.parseLong(userStr));
            } catch (NumberFormatException e) {
                log.error("parse userid error. userid:{}.", userStr, e);
                // 提示输入错误
                return CommonRespon.failure(ResponseCodeEnum.USER_ERROR);
            }
        }

        // 输入不能超过100个用户
        if(userIdsList.size() > 100) {
            return CommonRespon.failure(ResponseCodeEnum.JOIN_CLUB_NUM_LIMIT);
        }

        // 加入俱乐部
        List<Long> sucess = new ArrayList<>();
        List<Long> fail = new ArrayList<>();
        for(Long userIdShow : userIdsList) {
            Integer userId = checkUser(userIdShow, clubRecord);
            if(userId == null) {
                // 不可以加入俱乐部
                fail.add(userIdShow);
            } else {
                //判断用户是否加入黑名單
                TribeMembersExample tribeMembersExample = new TribeMembersExample();
                tribeMembersExample.or().andClubIdEqualTo(clubRecord.getId());
                List<TribeMembers> tribeMembers = tribeMembersDao.selectByExample(tribeMembersExample);
                List<ClubBlacklist> clubBlacklistTribe = new ArrayList<>();
                if (tribeMembers != null && !tribeMembers.isEmpty()) {
                    List<Integer> tribeIds = tribeMembers.stream().map(TribeMembers::getTribeId).collect(Collectors.toList());
                    ClubBlacklistExample clubBlacklistExampleTribe = new ClubBlacklistExample();
                    clubBlacklistExampleTribe.or().andTribeIdIn(tribeIds)
                                .andTypeEqualTo((byte)1)
                                .andContenttypeEqualTo((byte)0)
                                .andUserIdEqualTo(userId);
                    clubBlacklistTribe = clubBlacklistDao.selectByExample(clubBlacklistExampleTribe);
                }
                ClubBlacklistExample clubBlacklistExample = new ClubBlacklistExample();
                clubBlacklistExample.or().andClubIdEqualTo(clubRecord.getId())
                            .andTypeEqualTo((byte)0)
                            .andContenttypeEqualTo((byte)0)
                            .andUserIdEqualTo(userId);
                List<ClubBlacklist> clubBlacklist = clubBlacklistDao.selectByExample(clubBlacklistExample);

                if((clubBlacklist != null && !clubBlacklist.isEmpty()) || (clubBlacklistTribe != null && !clubBlacklistTribe.isEmpty())){
                    log.error("join club error . user is in the blacklist, userid:{}.", userIdShow);
                    fail.add(userIdShow);
                }else{
                    // 可以加入俱乐部
                    try {
                        joinClubTransactionService.joinClub(clubRecord.getId(), userId, promoterId);
                        sucess.add(userIdShow);
                    } catch (Exception e) {
                        log.error("Join club error.userid:{}.",userIdShow, e);
                        fail.add(userIdShow);
                    }
                }
            }
        }

        BathJoinClubResp bathJoinClubResp = new BathJoinClubResp();
        bathJoinClubResp.setSuccessUsers(sucess);
        bathJoinClubResp.setFailUsers(fail);
        return CommonRespon.success(bathJoinClubResp);
    }

    /**
     * 是否可以加入俱乐部。可以加入则返回用户id，否则返回null
     * @param userIdShow
     * @return
     */
    private Integer checkUser(Long userIdShow, ClubRecord clubRecord) {

        // 查询用户是否存在
        UserDetailsInfoExample example = new UserDetailsInfoExample();
        example.createCriteria().andRandomNumEqualTo(userIdShow + "");
        List<UserDetailsInfo> userDetailsInfos = userDetailsInfoDao.selectByExample(example);
        if(userDetailsInfos == null || userDetailsInfos.size() == 0) {
            log.error("join club error . user not exist, userid:{}.", userIdShow);
            return null;
        }

        Integer userId = userDetailsInfos.get(0).getUserId();
        if (joinClubDao.countByUserIdAndClubId(userId,clubRecord.getId()) > 0) {//大于0 就是已经加入此俱樂部，则不能再加
            log.error("join club error . user has been joined this club, userid:{}.", userIdShow);
            return null;
        }

        // 查询俱乐部是否满员
        if(clubRecord.getUpperLimit() <= clubRecord.getClubMembers()) {
            // 满员处理
            joinClubTransactionService.clubMembersLimitOperation(clubRecord);
            log.error("join club error . club memebers limit, userid:{}.", userIdShow);
            return null;
        }

        return userId;
    }

}
