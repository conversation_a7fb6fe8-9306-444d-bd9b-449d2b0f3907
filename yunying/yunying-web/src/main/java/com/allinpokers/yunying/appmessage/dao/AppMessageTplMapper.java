package com.allinpokers.yunying.appmessage.dao;


import com.allinpokers.yunying.appmessage.bean.AppMessageTpl;
import com.allinpokers.yunying.appmessage.bean.AppMessageTplQuery;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface AppMessageTplMapper {


    /**
     * AppMessageTplQuery query);
     * @param query 查询条件
     * @return 消息模板列表
     */
    List<AppMessageTpl> getMessageTplList(AppMessageTplQuery query);


    /**
     * 获取消息模板的总数
     * @param query 查询条件
     * @return 消息模板总数
     */
    long countMessageTpl(AppMessageTplQuery query);

    /**
     * 获取单个模板详情
     */
    AppMessageTpl getMessageTplById(Long tplId);

    /**
     * 新增模板
     */
    void addMessageTpl(AppMessageTpl messageTpl);

    /**
     * 更新模板
     */
    void updateMessageTpl(AppMessageTpl messageTpl);

    /**
     * 删除模板
     */
    void deleteMessageTpl(Long tplId);


    /**
     * 查询UserID是否存在
     * @param userIds
     * @return
     */
    @Select({
            "<script>",
            "SELECT COUNT(USER_ID) FROM user_basic_info WHERE USER_ID IN",
            "<foreach item='userId' collection='userIds' open='(' separator=',' close=')'>",
            "#{userId}",
            "</foreach>",
            "</script>"
    })
    Long countUserIdsInList(@Param("userIds") List<String> userIds);

}
