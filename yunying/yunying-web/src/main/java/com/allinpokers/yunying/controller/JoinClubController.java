package com.allinpokers.yunying.controller;

import com.allinpokers.yunying.model.request.joinclub.JoinClubReq;
import com.allinpokers.yunying.model.request.joinclub.QueryPromotorsReq;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.joinclub.BathJoinClubResp;
import com.allinpokers.yunying.model.response.joinclub.PromotorsList;
import com.allinpokers.yunying.services.JoinClubService;
import com.allinpokers.yunying.util.JsonUtils;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
@Api(value = "/club/join", description = "批量加入俱乐部模块", tags = {"批量加入俱乐部模块"})
@RestController
@RequestMapping("/club/join")
@Slf4j
public class JoinClubController extends BaseController {

    @Autowired
    private JoinClubService joinClubService;

    @ApiOperation(value = "根据俱乐部id查询俱乐部推广员列表", produces = "application/json", httpMethod = "POST")
    @PostMapping("/search/promotors")
    public CommonRespon<PromotorsList> searchClubInfo(@RequestBody(required = false) QueryPromotorsReq queryPromotorsReq) {
        log.info("查询俱乐部推广员请求  req={}", JsonUtils.write(queryPromotorsReq));
        return joinClubService.queryPromotors(queryPromotorsReq.getClubId(), queryPromotorsReq.getSearch(), queryPromotorsReq.getPage(), queryPromotorsReq.getSize());
    }

    @ApiOperation(value = "批量加入俱乐部", produces = "application/json", httpMethod = "POST")
    @PostMapping("/batch/join/club")
    public CommonRespon<BathJoinClubResp> batchJoinClub(@RequestBody(required = false) JoinClubReq joinClubReq) {
        log.info("加入俱乐部请求  req={}", JsonUtils.write(joinClubReq));
        return joinClubService.batchJoinClub(joinClubReq.getClubId(), joinClubReq.getPromotorId(), joinClubReq.getUserIds());
    }
}
