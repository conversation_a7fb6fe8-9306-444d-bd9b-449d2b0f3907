package com.allinpokers.yunying.controller;

import com.allinpokers.yunying.model.request.paychannel.BankSaveReq;
import com.allinpokers.yunying.model.request.paychannel.QueryChannelBankReq;
import com.allinpokers.yunying.util.JsonUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

@RunWith(SpringRunner.class)
@SpringBootTest()
public class PayChannelControllerTest {

    @Autowired
    PayChannelController payChannelController;

    @Test
    public void queryBank() {
        QueryChannelBankReq req = new QueryChannelBankReq();

        System.out.println(JsonUtils.write(payChannelController.queryBank(req)));

        //req.setId(20);

        System.out.println(JsonUtils.write(payChannelController.queryBank(req)));


    }

    @Test
    public void bankSave() {
        List<String> ll = new ArrayList<>(0);
        Set<String> str = ll.stream().collect(Collectors.toSet());

        System.out.println(str.size());

/*        BankSaveReq bankSaveReq = new BankSaveReq();
        bankSaveReq.setBankCodes(Arrays.asList("ABC","CMBC","BOC"));
        bankSaveReq.setChannelId(20);
        System.out.println(JsonUtils.write(payChannelController.bankSave(bankSaveReq)));*/
    }
}