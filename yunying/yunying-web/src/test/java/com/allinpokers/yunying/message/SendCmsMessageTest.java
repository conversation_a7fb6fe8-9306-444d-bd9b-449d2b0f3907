package com.allinpokers.yunying.message;

import com.allinpokers.yunying.rabbitmq.client.MessageSender;
import com.allinpokers.yunying.rabbitmq.client.bean.CmsMessage;
import com.allinpokers.yunying.rabbitmq.client.bean.OsmMessage;
import com.allinpokers.yunying.rabbitmq.client.bean.cms.TribeJoinApplyParams;
import com.allinpokers.yunying.rabbitmq.client.bean.osm.ClubCreateApplyParams;
import com.allinpokers.yunying.util.JsonUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest()
public class SendCmsMessageTest {
    @Resource
    private MessageSender sender;

    @Test
    public void send() {
        int userId = 1002441;
        TribeJoinApplyParams params = TribeJoinApplyParams.builder()
                .clubId(900047)
                .clubRandomId("726498")
                .clubName("六六")
                .tribeId(123456)
                .tribeName("联盟名称")
                .build();

        CmsMessage message = CmsMessage.builder()
                .senderId(userId + "")
                .type(1)
                .params(JsonUtils.write(params))
                .build();

        sender.sendCmsMessage(message);


    }
}
