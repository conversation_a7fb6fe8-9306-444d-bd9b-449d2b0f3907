package com.allinpokers.yunying.message;

import com.allinpokers.yunying.rabbitmq.client.MessageSender;
import com.allinpokers.yunying.rabbitmq.client.bean.OsmMessage;
import com.allinpokers.yunying.rabbitmq.client.bean.osm.ClubCreateApplyParams;
import com.allinpokers.yunying.util.JsonUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest()
public class SendOsmMessageTest {
    @Resource
    private MessageSender sender;

    @Test
    public void send() {
        int userId = 1002441;
        ClubCreateApplyParams params = ClubCreateApplyParams.builder()
                .userId(userId)
                .userNickname("nickname")
                .relateMsgId(UUID.randomUUID().toString())
                .build();

        OsmMessage osmMessage = OsmMessage.builder()
                .senderId(userId + "")
                .type(1)
                .params(JsonUtils.write(params))
                .build();

        sender.sendOsmMessage(osmMessage);
    }
}
