package com.allinpokers.yunying;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.junit.Test;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.stereotype.Controller;
import org.springframework.util.ClassUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 扫描 @Controller @RestController 注解，并且和 yunying-permission.sql 文件对比
 * 如果在 yunying-permission.sql 中不包含对应的接口路径，则提示
 *
 * <AUTHOR>
 */
public class PermissionCheck {

    @Test
    public void check() throws Exception {
        //所有人都有的权限
        List<String> ignoreUrls = Lists.newArrayList(
                "/authUsers/menusAndDataCodes",
                "/authUsers/login",
                "/authUsers/sendSmsForRoot",
                "/authUsers/logout",
                "/authUsers/updatePassword",
                "/cmsMessages/readAll",
                "/osmMessages/readAll"
        );

        //扫包
        List<Api> apis = new ArrayList<>();
        List<Class> controllerClassList = scanControllers("com.allinpokers");
        for (Class<?> controllerClass : controllerClassList) {
            RequestMapping typeMapping = controllerClass.getAnnotation(RequestMapping.class);
            io.swagger.annotations.Api apiAnno = controllerClass.getAnnotation(io.swagger.annotations.Api.class);
            String controllerTag = apiAnno == null || apiAnno.tags().length == 0 ? "" : apiAnno.tags()[0];

            List<String> baseUrls = typeMapping == null ? new ArrayList<>() : Lists.newArrayList(typeMapping.value());
            for (Method method : controllerClass.getMethods()) {
                List<Api> list = parseMethod(method, controllerClass.getSimpleName(), controllerTag, baseUrls);
                apis.addAll(list);
            }
        }

        //读取 yunying-permission.sql
        List<String> configUrls = new ArrayList<>();
        String path = PermissionCheck.class.getResource("/").getPath();
        path = path.substring(0, path.indexOf("yunying-web")) + "documents/yunying-permission.sql";
        File sqlFile = new File(path);
        try (BufferedReader reader = new BufferedReader(new FileReader(sqlFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.contains("insert into auth_resource") || !line.contains("method")) {
                    continue;
                }
                line = line.split(", ")[11];
                line = line.substring(1, line.length() - 1);
                configUrls.add(line);
            }
        }

        System.out.println(String.format("----------------读取到已配置的接口url-------------共%s个", configUrls.size()));
        for (String configUrl : configUrls) {
            System.out.println(configUrl);
        }

        //打印没有配置接口权限的url
        apis = apis.stream()
                .filter(api -> !configUrls.contains(api.getUrl()) && !ignoreUrls.contains(api.getUrl()))
                .collect(Collectors.toList());
        System.out.println(String.format("----------------开始打印打印没有配置接口权限的url-------------共%s个", apis.size()));
        for (Api api : apis) {
            System.out.println(api.getControllerName() + "\t\t" + api.getUrl());
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @ToString
    static class Api {
        private String controllerName;
        private String method;
        private String url;
        private String desc;
    }

    private List<Class> scanControllers(String... basePackages) {
        String resourcePattern = "/**/*.class";
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        List<Class> classList = new ArrayList<>();

        for (String basePackage : basePackages) {
            String pattern = ResourcePatternResolver.CLASSPATH_ALL_URL_PREFIX + ClassUtils.convertClassNameToResourcePath(basePackage) + resourcePattern;
            try {
                Resource[] resources = resolver.getResources(pattern);
                MetadataReaderFactory readerFactory = new CachingMetadataReaderFactory(resolver);
                for (Resource resource : resources) {
                    if (resource.isReadable()) {
                        MetadataReader metadataReader = readerFactory.getMetadataReader(resource);
                        String className = metadataReader.getClassMetadata().getClassName();
                        Class<?> clazz = Class.forName(className);
                        Controller controller = clazz.getAnnotation(Controller.class);
                        RestController restController = clazz.getAnnotation(RestController.class);
                        if (controller != null || restController != null) {
                            classList.add(clazz);
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return classList;
    }

    private List<Api> parseMethod(Method method, String controllerName, String controllerTag, List<String> baseUrls) {
        List<Api> apis = new ArrayList<>();

        ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
        String desc = controllerName + "：" + controllerTag + (apiOperation == null ? "" : apiOperation.value());




//        Consumer<String[], String> consumer = (urls, method) -> {
//            if (baseUrls.isEmpty()) {
//                for (String url : urls) {
//                    url = url.startsWith("/") ? url : "/" + url;
//                    apis.add(new Api(controllerName, method, url, desc));
//                }
//                return;
//            }
//            for (String baseUrl : baseUrls) {
//                baseUrl = !baseUrl.endsWith("/") ? baseUrl : baseUrl.substring(0, baseUrl.length() - 1);
//                for (String url : urls) {
//                    url = url.startsWith("/") ? url : "/" + url;
//                    String all = baseUrl + url;
//                    apis.add(new Api(controllerName, method, all, desc));
//                }
//            }
//        };
//
//        RequestMapping requestMapping = method.getAnnotation(RequestMapping.class);
//        if (requestMapping != null) {
//            consumer.accept(requestMapping.value());
//        }
//        PostMapping postMapping = method.getAnnotation(PostMapping.class);
//        if (postMapping != null) {
//            consumer.accept(postMapping.value());
//        }
//        GetMapping getMapping = method.getAnnotation(GetMapping.class);
//        if (getMapping != null) {
//            consumer.accept(getMapping.value());
//        }
//        DeleteMapping deleteMapping = method.getAnnotation(DeleteMapping.class);
//        if (deleteMapping != null) {
//            consumer.accept(deleteMapping.value(), "DELETE");
//        }

        return apis;
    }

}
