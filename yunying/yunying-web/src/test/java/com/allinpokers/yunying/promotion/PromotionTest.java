package com.allinpokers.yunying.promotion;

import com.allinpokers.yunying.dao.crazypoker.JPContributionDao;
import com.allinpokers.yunying.model.request.userdataedit.UserDataEditReq;
import com.allinpokers.yunying.mongodb.dao.AddRequestInfoDao;
import com.allinpokers.yunying.mongodb.dao.GameRequestRecordDao;
import com.allinpokers.yunying.mongodb.dao.SelectClubDao;
import com.allinpokers.yunying.mongodb.model.JPContribution;
import com.allinpokers.yunying.services.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class PromotionTest {

    @Autowired
    JpbAccountService jpbAccountService;

    @Autowired
    MaintainMessageService maintainMessageService;

    @Autowired
    DataManagerService dataManagerService;

    @Autowired
    PayChannelService payChannelService;

    @Autowired
    CmsAccountService cmsAccountService;

    @Autowired
    private AddRequestInfoDao addRequestInfoDao;

    @Autowired
    private SelectClubDao selectClubDao;

    @Autowired
    private GameRequestRecordDao gameRequestRecordDao;

    @Autowired
    private IUserDataEditService userDataEditService;


    @Test
    public void toBePromoterForOldData() throws ParseException {
//        PageBean<JackpotIntoRecord> result = jpbAccountService.queryIntoRecord(new Date(), new Date(), 0, 20);
//        PageBean<JPBAccountRakebackResp> result = jpbAccountService.queryJPBAccountRakebackList(0, null, null);
//        maintainMessageService.config(111, "content", 1, 0);
//        List<AddRequestInfo> addRequestInfos = addRequestInfoDao.findByTimeBetween(0, new Date().getTime());
//        List<GameRequestRecord> selectClubs = gameRequestRecordDao.findByUserIdAndTimeBetween(305789, 0, new Date().getTime());
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        List<SelectClubInfo> selectClubs = dataManagerService.querySelectClub("Bing", sdf.parse("2019-01-01"), new Date());
//        System.out.println("aa");
//        payChannelService.querySelectClubList(14,"六",  null, null);
//        payChannelService.queryTribeClubInfos(14,null,"六",  null, null);
        cmsAccountService.queryCmsAccountFlow(1,null,0,null, null,0 , 20);
    }

    @Test
    public void testUserDataEdit() {
        UserDataEditReq req = new UserDataEditReq();
        req.setUserIds("**********");
        req.setWeekAfRateMin(10);
        req.setWeekAfRateMax(100);
        req.setWeekTotalHandMin(10);
        req.setWeekTotalHandMax(100);
        req.setWeekPoolWinRateMin(10);
        req.setWeekPoolWinRateMax(100);
        req.setWeekAllinWinRateMin(10);
        req.setWeekAllinWinRateMax(99);
        req.setWeekBet3RateMin(10);
        req.setWeekBet3RateMax(100);
        req.setWeekCbetRateMin(10);
        req.setWeekCbetRateMax(100);
        req.setWeekGameCntMin(10);
        req.setWeekGameCntMax(100);
        req.setWeekPoolRateMin(10);
        req.setWeekPoolRateMax(100);
        req.setWeekPrfRateMin(10);
        req.setWeekPrfRateMax(100);
        req.setWeekTanpaiRateMin(10);
        req.setWeekTanpaiRateMax(100);
        userDataEditService.editUserData(req);
    }
}