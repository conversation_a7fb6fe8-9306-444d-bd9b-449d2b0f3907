spring:
  jpa:
    show-sql: true
  data:
    mongodb:
      uri: ***************************************************
      database: crazy_poker
    writemongo:
      uri: ***************************************************
      database: crazy_poker
  datasource:
    crazy-poker:
      jdbcUrl: ***************************************************************************************************************************************************************************
      username: work
      password: SqL0301myT2016est
    yunying:
      jdbcUrl: ***********************************************************************************************************************************************************************
      username: work
      password: SqL0301myT2016est
    vest:
      jdbcUrl: **************************************************************************************************************************************************************************
      username: work
      password: SqL0301myT2016est
  redis:
    host: ************
    password: YUjV9YbA%qwuJJT
    port: 5733
      #rabbit mq设置
  rabbitmq:
    host: ************
    port: 5670
    username: work
    password: YUjV9YbAqwuJJT
    #发布确认
    publisher-confirms: true
    #退回确认
    publisher-returns: true
    virtual-host: /
    cache:
      channel:
        size: 100


yunying:
  security:
    # 登录接口地址
    login-processing-url: /authUsers/login
    # 退出登录接口地址
    logout-processing-url: /authUsers/logout
    # 忽略不需要登录的地址
    ignore-urls:
      - /favicon.ico
      - /usermemger/agent/login
      - /swagger-ui.html**
      - /webjars/springfox-swagger-ui/**
      - /swagger-resources/**
      - /v2/api-docs
      - /report/roomhand
      - /report/handdetail
      - /channel_pay/web/withdraw/apply
      - /authUsers/sendSmsForRoot
      - /club/insertClubPromoteLog
    # jwt token 签名密钥
    token-secret: 2zzKZCiGA!wKk0PxJ@VEcJCTFGJ*YY56$7lQ6P8z2S7f3%TN4
    # token 过期时间
    token-expire-minutes: 30
  web:
    # 代付渠道接口请求基本路径
    pay-channel-base-url: https://************:8057/api/pay
    # 代付渠道Jwt的密钥
    pay-channel-jwt-secret: wktZ8iR7SlLp1q9F5qUlHPrxkohC33Q1
    # 高德地图KEY
    amap-key: 密钥
    # ip离线数据库的文件地址
    ipdb-path: config/ipip.ipdb
  payment:
    product:
      configPath: /mnt/yunying/product/
server:
  port: 8663
  servlet:
    context-path: /api/yunying

# 热配置文件
config:
  dirPath: config/op
  network:
    fileName: network-config.json
  root-account-login:
    fileName: root-account-login-config.json
  at:
    fileName: at-config.json
  audit:
    fileName: audit-config.json
  room-info-whitelist:
    fileName: room-info-whitelist-config.json

charsetName:
# zookeeper 配置文件
zk:
  #是否启动
  enable: true
  ##
  # Zookeeper服务器的地址列表
  # 格式: ip(hostname):port[,ip(hostname):port]*
  # 必须提供
  ##
  connectString: ************:2181,************:3181,************:4181
  ##
  # 可选
  # 必须大于0，否则忽略此设置
  # 默认值：60 秒
  ##
  sessionTimeoutMs: 60000
  ##
  # 可选
  # 必须大于0，否则忽略此设置
  # 默认值：15 秒
  ##
  connectionTimeoutMs: 15000
  ##
  # 可选
  # 必须大于0，否则忽略此设置
  # 默认值：1 秒
  ##
  maxCloseWaitMs: 1000
  ##
  # 创建CuratorTempFramework时使用
  # 可选
  # 必须大于0，否则忽略此设置
  # 默认值：3 分
  ##
  inactiveThresholdMs: 180000
  ##
  # 设置当前这个Zookeeper访问的命名空间
  # 如果设置了，通过此实例访问的路径都将自动附加
  # 上此设置作为路径的前缀。
  # null或mepty，忽略此参数
  ##
  namespace:
  ##
  # 1 :  true
  # otherwise false
  ##
  canBeReadOnly:
  useContainerParentsIfAvailable:

# 国内短信配置
sms:
  template: 559670
  templateInternation: 6000005
  uid: qk123456
  pwd: a5202d304211956e86d34b8a32817a47
  smsUrl: http://api.sms.cn/sms/?



# 253短信平台(国内)
smscountry:
  # 用户名
  userName: N4320736
  # 密码
  passWord: nLW2DZ47qh116a
  # api地址
  smsUrl:  http://smssh1.253.com/msg/send/json

# 253短信平台(国际)
smsinternational:
  # 用户名
  userName: I7416707
  # 密码
  passWord: pgcXnAMYU644af
  # api地址
  smsUrl: http://intapi.253.com/send/json


logging:
  file: /mnt/logs/yunying/yunying.log

# 消息栏多语言配置
messages:
  osm:
    basename: i18n/OsmMessages
    encoding: UTF-8
  cms:
    basename: i18n/CmsMessages
    encoding: UTF-8

# 维护觸發強制結算
maintenance:
  settlement:
    checkInterval: 60
    minLeadTime: 30

#阿里云oss 配置
aliyun:
  oss:
    endpoint: oss-cn-shenzhen.aliyuncs.com
    access-key-id: LTAI5t6UYvAeqQZb6Zb3LRdw
    access-key-secret: ******************************
    bucket-name: qqpoker-icon

playback:
  url: https://share-dev.qqpoker.fun/
