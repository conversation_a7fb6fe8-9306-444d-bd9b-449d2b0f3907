package com.allinpokers.yunying.enu;

import lombok.Getter;

@Getter
public enum ResponseCodeEnum {
    /**
     *
     */
    SUCCESS(0,"请求成功"),
    ERROR(-1,"请求失败"),
    PARAM_ERROR(2,"参数错误"),
    RECORD_NOT_EXIST(3,"记录不存在"),

    //权限相关及外部服务
    PARAM_VALID_FAILED(10001, "参数校验失败"),
    TOKEN_OUT(10002,"登录凭证无效"),
    USERNAME_OR_PASSWORD_ERROR(10003, "用户名或密码错误"),
    PERMISSION_ACCESS_DENIED(10004, "权限不足，请联系管理员"),
    LOGIN_FAILURE(10005, "登录失败"),
    OP_NOT_EXIST(10006,"指令不存在"),
    USERNAME_RULE_NOT_MATCH(10007,"用户账号必须为6-16位数字+字母"),
    TWO_PASSWORD_NOT_EQUALS(10008,"两次密码不一致"),
    PASSWORD_RULE_NOT_MATCH(10009,"密码由数字、字母和!@#$%^&*组成，长度6-16"),
    EXIST_SAME_USERNAME(10010,"已存在相同的用户账号"),
    OUT_OF_ALLOW_PERMISSION_RANGE(10011,"超出可配置的权限范围"),
    AUTH_USER_DISABLE(10012,"账号已停用，请联系相关人员"),
    NEED_ENABLE_ANCESTOR_USER(10013,"父账号未启用"),
    CANT_ADD_ROOT_USER(10014,"请联系相关人员新增超级账号"),
    ROOT_ACCOUNT_NOT_ALLOW_THIS_IP(10015,"超级账号不允许在该IP登录"),
    ROOT_ACCOUNT_NEED_SMS_CODE(10016,"超级账号登录需要使用验证码"),
    CANT_ADD_PLATFORM_USER(10017,"不能创建平台账号"),
    PARAM_IP_FAILURE(10018,"IP校验失败"),
    LOGIN_IP_NOT_IN_WHITE_LIST(10019,"登录失败，因IP不在白名单内"),
    TOTP_CODE_REQUIRED(10020,"需要输入谷歌二次验证码"),
    TOTP_CODE_INVALID(10021,"谷歌二次验证码错误"),

    //用户相关
    USER_NOT_EXIST(20001, "用户不存在"),
    USER_ALREADY_EXIST(20002, "用户已存在"),
    USER_ALREADY_WITHDRAW(20003, "用户已注销账号"),
    USER_STATUS_EXCEPTION(20004, "用户状态异常"),
    USER_BIND_PHONE_FAILED(20005, "用户绑定手机号失败"),
    MARKETING_ACCOUNT_NOT_EXIST(2006,"营销账户暂无"),
    USER_CHIP_NOT_ENOUGH(2007,"账户金豆不足"),
    USER_CANNOT_CREATE_ROOM(2008,"用户没有创建牌局权限"),
    USER_HAS_CREATE_ROOM_OP(2009,"用户已经有创建牌局权限"),
    USER_HAS_NO_PHONE(2010,"用户暂无绑定手机"),
    USER_PWD_ERROR(2011,"用户密码错误"),


    //数据
    DATA_EMPT(3001,"没有数据"),
    ACCOUNT_NOT_EXIST(3002,"账户不存在"),
    JSON_FAIL(3003,"json转换失败"),
    DATA_OUT_OF_TIME_LIMIT_RANG(3004,"超出可查看的时间范围，有疑问请联系管理员"),
    DATA_OUT_OF_TIME_LIMIT_RANG_7(3005,"只能查看最近7天内数据，有疑问请联系管理员"),
    DATA_OUT_OF_TIME_LIMIT_RANG_3(3006,"只能查看最近3天内数据，有疑问请联系管理员"),
    DATA_OUT_OF_TIME_LIMIT_RANG_35(3007,"只能查看最近35天内数据，有疑问请联系管理员"),
    DATA_UN_FINISH(3008,"数据还没有生成"),
    DATA_HAS_EXIST(3009,"记录已经存在"),
    DATA_OUT_OF_TIME_LIMIT_RANG_60(3010,"只能查看最近60天内数据，有疑问请联系管理员"),



    //消息相关
    MESSAGE_CANT_JOIN_OTHER_TRIBE(4001, "该俱乐部已加入其他联盟，通过失败"),
    MESSAGE_IS_A_TRIBE_CANT_CREATE(4002, "该用户已经创建了一个联盟，不能再次创建"),
    MESSAGE_JOIN_TRIBE_CANT_CREATE(4003, "该用户已经加入了一个联盟，不能再次创建"),
    MESSAGE_EXIST_SAME_TRIBE_NAME_CANT_CREATE(4004, "已存在相同的联盟名称，不能创建"),
    MESSAGE_TRIBE_PROFIT_OUT_OF_RANGE(4005, "联盟的占成比率超出范围（0-99）"),
    MESSAGE_TRIBE_CLUB_UPPER_LIMIT_OUT_OF_RANGE(4006, "联盟的俱乐部上限超出范围（1-20）"),
    MESSAGE_CANT_JOIN_TRIBE_CLUB_UPPER_LIMIT(4007, "该联盟的俱乐部数量已达上限"),
    MESSAGE_CLUB_IS_BEYOND_TRIBE_LIMIT(4008, "该联盟俱乐部数量已达上限，请重新选择"),
    MESSAGE_EXIST_SAME_CLUB_NAME_CANT_CREATE(4009, "已存在相同的俱乐部名称，不能创建"),
    MESSAGE_HAS_CLUB_CANT_CREATE(4010, "该用户已经有俱乐部，不能创建"),
    MESSAGE_CANT_SUPPORT_MESSAGE_TYPE(4011, "不支持的消息类型"),
    MESSAGE_CLUB_UPPER_LIMIT_OUT_OF_RANGE(4012, "俱乐部的人数上限超出范围（1-5000）"),
    MESSAGE_JOIN_TRIBE_CLUB_OPERATOR_NOT_CLUB(4013, "当前用户没有配置对应的俱乐部ID"),
    MESSAGE_JOIN_TRIBE_CLUB_IS_NOT_OWNER_CLUB(4014, "当前用户的俱乐部没有创建联盟"),
    MESSAGE_JOIN_CLUB_PAY_CHANNEL_NOT_LEGAL(4015,"指定代充渠道非法"),

    //联盟相关
    TRIBE_CLUB_SMALLER_NOW_CLUBMENBERS(5001,"联盟现有俱乐部数量超过调整上限值"),
    TRIBE_CREATOR_CLUB_CAN_NOT_DELETE(5002,"联盟直属俱乐部不可以转移|踢出"),
    TRIBE_NOT_EXIST(5003,"联盟不存在"),
    TRIBE_SUB_TRIBE_IS_NOT_EMPTY(5004, "子联盟不为空"),
    TRIBE_CHANGE_EXIST_MAIN_TRIBE(5005,"转移联盟的列表存在主联盟身份的联盟"),
    TRIBE_MAIN_NOT_LEGAL(5006,"主联盟不合法"),
    TRIBE_RANDOM_ID_EXIST(5007,"联盟显性id 存在"),
    TRIBE_STATUS_CLOSE_FAIL_1(5008,"这个的俱乐部是联盟的主俱乐部，请先移除联盟内其他俱乐部"),
    TRIBE_ID_FAIL_1(5009,"联盟ID不能为空"),
    TRIBE_ID_FAIL_2(5010,"联盟ID格式错误，请输入4-7位数字"),
    TRIBE_ID_FAIL_3(5011,"联盟ID已重复，请检查"),

    //俱乐部相关
    CLUB_PROFIT_LIMIT_FALT(6001,"俱乐部分成比例不得超过联盟比例"),
    CLUB_SMALLER_NOW_CLUBMENBERS(6002,"俱乐部现有成员数量超过调整上限值"),
    CLUB_IS_OUT_TO_TRIVER(6003,"该俱乐部踢出或者转移中"),
    CLUB_HOST_OVER_LIMIT_NUMBERS(6004,"超出推荐俱乐部数量限制"),
    CLUB_NOT_EXIST(6005,"俱乐部不存在"),
    CLUB_HOST_RECORD_EXIST(6006,"推荐俱乐部已经存在"),
    CLUB_IS_PAY_CHANNEL(6007,"俱乐部已经是代充渠道"),
    CLUB_CANT_BE_PAY_CHANNEL(6008,"该俱乐部不能被设置成代充渠道"),
    CLUB_TRANS_TYPE_CAN_NOT_OPEN(6009,"非俱乐部充值模式不可设置返佣开关"),
    CLUB_RANDOM_ID_EXIST(6010,"俱乐部显性id 存在"),
    CLUB_STATUS_CLOSE_FAIL_1(6011,"删除俱乐部前请先移除所有俱乐部成员"),
    CLUB_STATUS_CLOSE_FAIL_2(6012,"这个的俱乐部是联盟的主俱乐部，请先删除联盟"),
    CLUB_ID_FAIL_1(6013,"俱乐部ID不能为空"),
    CLUB_ID_FAIL_2(6014,"俱乐部ID格式错误，请输入4-6位数字"),
    CLUB_ID_FAIL_3(6015,"俱乐部ID已重复，请检查"),

    //    短信
    SMS_CAPTCHA_CODE_ERROR(7000, "验证码校验错误"),
    SMS_CODE_EMPTY(7001,"验证码失败"),



    //游戏配置相关
    BANNER_IMG_MORE_THAN_CONFIG(8001, "上架图片数量超出配置的最大值"),
    MAINTAIN_MESSAGE_CONFIG(8002, "不能将过去的时间设为结束时间"),

    // 商城-商品配置
    PAYMENT_PRODUCT_CHANNEL_INVALID(9001,"指定的渠道ID无效"),
    PAYMENT_PRODUCT_CODE_EXISTING(9002,"产品编码已经存在"),

    //营销账户
    MARK_ACCOUNT_MINUS(10001,"营销账户不可负，金豆不足"),

    // 网络切换
    NETWORK_SYNCFILE_NOT_CONFIGURED(11001,"网络切换同步文件未配置"),
    EXPLOSIONTABLE_SYNCFILE_NOT_CONFIGURED(11002,"爆牌模式同步文件未配置"),

    //提豆相关
    ACCOUNT_PAYEE_CHECK_AUDITED(12001, "该提豆信息修改已被审核"),
    WITHDRAW_CHIP_IS_PROCESSED(12002, "该提豆申请已被审核"),
    USER_ACCOUNT_NOT_EXIST(12003, "用户账户不存在"),
    WITHDRAW_CHIP_AGREE_PARTIAL_FAIL(12004, "部分玩家提豆审核失败"),
    PLEASE_PROCESS_BY_SDK(12005, "请点击[同意]按钮使用SDK方式"),
    PLEASE_PROCESS_BY_ARTIFICIAL(12006, "请点击[已处理]按钮使用人工处理方式"),
    ACCOUNT_PAYEE_LIMIT_MORE_NEQ_TWO(12007, "该的银行卡已经绑定过2个ID号码，无法再次绑定。"),
    NOT_SUPPORT_BANK(12008, "不支持的银行"),
    WITHDRAW_CHIP_OPERATION_SUCCESS(12009, "操作成功"),
    WITHDRAW_CHIP_OPERATION_FAIL(12010, "操作失败"),
    JUMP_BANK(12011, "请转换人工操作"),
    //系统维护模块
    ROOM_PATH_NOT_EXIST(13001,"牌局类型不存在"),

    // 支付渠道配置
    CMS_ACCOUNT_NOT_EXIST(14001, "cms账号不存在"),
    CMS_ACCOUNT_BINDED(14002, "cms账号已绑定"),
    PAYMENT_CODE_NOT_EXIST(14003, "支付商编码不存在"),
    CMS_ACCOUNT_NOT_MATCH(14004, "cms账号不匹配"),
    CLUB_BIND_ANOTHER_CHANNEL(14005, "俱乐部已配置到其他充值渠道"),
    CHANNEL_CAN_NOT_DELETED(14006, "已配置俱乐部，不能删除"),
    CAN_NOT_SET_DEFAULT(14007, "没有使用中的配置，不能设置为默认"),
    PAYMENT_INFO_NOT_EXIST(14008, "支付配置信息不存在"),
    PAYCHANNEL_NOT_EXIST(14009, "支付渠道不存在"),


    // 批量加入俱乐部
    USER_ERROR(15001, "输入用户错误"),
    PROMOTER_ERROR(15002, "推广员id错误"),
    JOIN_CLUB_NUM_LIMIT(15003, "添加用户数量超出限制"),

    // AT分发
    USER_NOT_BOUND_PHONE(16001,"此号码未绑定任何用户"),
    USER_CLUB_NOTMATCH(16002,"用户归属俱乐部不匹配当前编辑俱乐部"),
    USER_BEEN_ATUSER(16003,"此用户已添加，不能重复添加"),
    USER_MT_NOTEXISTING(16004,"此用户不存在！"),
    USER_NOT_UPDATING(16005,"更新数据不存在！"),
    AT_AUTO_MODE_ON(16006,"自动模式的全局开关必须关闭！"),
    AT_IS_DISPATCHED(16007,"此用户正在派遣中！"),

    //手工充值仓 & 手工充豆
    MANUAL_RECHARGE_BALANCE_LACK(17001,"用户余额不足，手工充豆失败"),
    MANUAL_RECHARGE_MINUS(17002,"手工充值仓金豆不足，充值失败"),
    MANUAL_RECHARGE_NEED_LARGE_THAN_ZERO(17003,"增加/减少的金豆必须大于0"),
    MANUAL_RECHARGE_CANT_BE_NEGATIVE(17004,"手工充值仓不能为负数"),
    MANUAL_RECHARGE_NOT_ALLOW_RECHARGE_TYPE(17005,"不支持的充豆类型"),

    //充值
    ORDER_NOT_EXITS(18001,"订单不存在"),
    ORDER_ALREADY_PROCESSED(18002,"订单已经被处理"),
    ORDER_STATUS_ERROR(18003,"订单状态不正确"),
    ORDER_CMSACC_INSUFFICIENT(18004,"cms账户余额不足"),

    //活动相关
    ACTIVITY_FIRST_CHARGE_RED_PACK_CLUB_NOT_CONFIG(19001, "该俱乐部没有配置到首充红包活动中"),

    //修改个人数据
    USER_DATA_EDIT_LACK_ERROR(20001, "请补充完整所有数据"),
    USER_DATA_EDIT_ERROR(20002, "个人数据编辑错误"),
    USER_DELTA_EDIT_LACK_ERROR(20003, "请补充完整所有数据"),

    // 跑马灯奖池
    HORSE_RACE_LAMP_ACCOUNT_MINUS(21001,"跑马灯奖池金豆不足"),

    // 加权
    WEIGHTING_USER_MANUAL_NOTSATISFIED(22001,"用户不满足设置为手工加权的条件"),
    WEIGHTING_CANCEL_NOTSATISFIED(22002,"用户不满足取消加权的条件"),

    //mtt相关
    MTT_START_TIME_ILLIGAL_ERROR(23001, "开局时间必须比当前时间大5分钟"),
    MTT_CREATE_ERROR(23002, "创建比赛失败"),
    MTT_NOT_FIND_ERROR(23003, "该比赛不存在"),
    MTT_IS_ENDED_ERROR(23004, "该比赛已经结束"),
    MTT_CANNEL_GAME_ERROR(23005, "未开始,开赛前10分钟内不能解散"),
    MTT_NOT_FIND_RECORD_ERROR(23006, "未找到mtt的战绩"),
    MTT_NO_AVAIABLE_SERVER_ERROR(23007, "未找到可用的mtt服务器"),
    //服务费相关
    PUMP_DATA_EDIT_ERROR(24001, "服务费数据编辑错误"),

    //房間列表
    ROOM_PLAYER_LIST_ACTION_ERROR(25001,"行動不能为空"),
    ROOM_PLAYER_LIST_USERID_ERROR(25002,"玩家ID不能为空"),
    ROOM_PLAYER_LIST_ROOM_ERROR(25003,"房間不存在"),
    ROOM_PLAYER_LIST_USER_ERROR(25004,"玩家不在房間"),
    ROOM_PLAYER_LIST_STANDUP_ERROR(25005,"玩家已經站起")
    ;
    private final int code;
    private final String msg;

    ResponseCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
