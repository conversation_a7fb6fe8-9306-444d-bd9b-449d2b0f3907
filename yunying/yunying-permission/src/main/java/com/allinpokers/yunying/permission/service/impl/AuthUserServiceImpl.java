package com.allinpokers.yunying.permission.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.allinpokers.yunying.enu.ResponseCodeEnum;
import com.allinpokers.yunying.model.response.CommonRespon;
import com.allinpokers.yunying.model.response.PageBean;
import com.allinpokers.yunying.permission.config.SecurityConfig;
import com.allinpokers.yunying.permission.config.cache.IHotConfigCache;
import com.allinpokers.yunying.permission.constants.AuthSystemCode;
import com.allinpokers.yunying.permission.constants.AuthTypeCode;
import com.allinpokers.yunying.permission.constants.AuthUserStatus;
import com.allinpokers.yunying.permission.constants.EPermissionCode;
import com.allinpokers.yunying.permission.crazypoker.dao.ClubRecordDao;
import com.allinpokers.yunying.permission.crazypoker.dao.LoginWhiteIpDao;
import com.allinpokers.yunying.permission.crazypoker.entity.ClubRecord;
import com.allinpokers.yunying.permission.crazypoker.entity.example.ClubRecordExample;
import com.allinpokers.yunying.permission.crazypoker.entity.loginWhiteIp.LoginWhiteIp;
import com.allinpokers.yunying.permission.dao.AuthSystemDao;
import com.allinpokers.yunying.permission.dao.AuthUserDao;
import com.allinpokers.yunying.permission.entity.AuthPermission;
import com.allinpokers.yunying.permission.entity.AuthSystem;
import com.allinpokers.yunying.permission.entity.AuthUser;
import com.allinpokers.yunying.permission.entity.AuthUserPermission;
import com.allinpokers.yunying.permission.entity.example.AuthUserExample;
import com.allinpokers.yunying.permission.model.request.*;
import com.allinpokers.yunying.permission.model.response.*;
import com.allinpokers.yunying.permission.security.TokenUtils;
import com.allinpokers.yunying.permission.security.UserInfo;
import com.allinpokers.yunying.permission.security.UserInfoService;
import com.allinpokers.yunying.permission.service.*;
import com.allinpokers.yunying.permission.service.model.RadioGroupPermission;
import com.allinpokers.yunying.permission.util.IpLocationUtils;
import com.allinpokers.yunying.permission.util.TotpUtils;
import com.allinpokers.yunying.sms.constant.ESmsType;
import com.allinpokers.yunying.sms.service.ISmsService;
import com.allinpokers.yunying.util.IpUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AuthUserServiceImpl implements AuthUserService {
    @Resource
    private AuthUserDao authUserDao;
    @Resource
    private AuthUserPermissionService authUserPermissionService;
    @Resource
    private AuthPermissionStandByService authPermissionStandByService;
    @Resource
    private AuthPermissionService authPermissionService;
    @Resource
    private AuthPermissionGroupService authPermissionGroupService;
    @Resource
    private AuthSystemDao authSystemDao;
    @Resource
    private AuthSystemService authSystemService;
    @Resource
    private UserInfoService userInfoService;
    @Resource(name = "securityConfig")
    private SecurityConfig config;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource(name = "smsServiceImpl")
    private ISmsService smsService;
    @Resource(name = "hotConfigCache")
    private IHotConfigCache configCache;
    @Resource
    private IpLocationUtils ipLocationUtils;
    @Resource(name = "permissionLoginWhiteIpDao")
    private LoginWhiteIpDao loginWhiteIpDao;
    @Resource(name = "permissionClubRecordDao")
    private ClubRecordDao clubRecordDao;

    @Override
    public AuthUser findByUsername(String username) {
        AuthUserExample example = new AuthUserExample();
        example.or().andUsernameEqualTo(username).andDeletedEqualTo(false);
        List<AuthUser> authUsers = authUserDao.selectByExample(example);
        return authUsers.isEmpty() ? null : authUsers.get(0);
    }

    @Override
    public AuthUser findByUserId(Integer userId) {
        return authUserDao.selectByPrimaryKey(userId);
    }

    @Override
    public Map<Integer, AuthUser> authUserMap() {
        Map<Integer, AuthUser> map = new HashMap<>();
        List<AuthUser> list = this.selectAllAuthUser();
        if (!list.isEmpty()) {
            map = list.stream().collect(Collectors.toMap(AuthUser::getId, v -> v));
        }
        return map;
    }

    @Override
    public List<AuthUser> selectAllAuthUser() {
        AuthUserExample example = new AuthUserExample();
        return authUserDao.selectByExample(example);
    }

    public static void main(String[] args) {
        String hashed = BCrypt.hashpw("root12345", BCrypt.gensalt());
        System.out.println(hashed);
    }
    @Transactional(transactionManager = "yunyingTransactionManager", rollbackFor = RuntimeException.class)
    @Override
    public CommonRespon create(CreateUserReq req) {
        UserInfo operationUser = req.getOperationUser();
        boolean isRoot = operationUser.isRoot();
        boolean isOperation = operationUser.isOperation();

        boolean needAuthType = (isRoot && req.getAuthTypeCode() == null)
                || (isOperation && req.getAuthTypeCode() == null);
        if (needAuthType) {
            //超级账号新增用户时必须指定新增哪个类型的账号
            return CommonRespon.failure(ResponseCodeEnum.PARAM_VALID_FAILED);
        }
        if (req.getAuthTypeCode().equals(AuthTypeCode.ROOT)) {
            return CommonRespon.failure(ResponseCodeEnum.CANT_ADD_ROOT_USER);
        }
        if (isOperation && req.getAuthTypeCode().equals(AuthTypeCode.PLATFORM)) {
            return CommonRespon.failure(ResponseCodeEnum.CANT_ADD_PLATFORM_USER);
        }

        //登录账号校验
        String regex = "[\\w]{6,16}";
        if (!req.getUsername().matches(regex)) {
            return CommonRespon.failure(ResponseCodeEnum.USERNAME_RULE_NOT_MATCH);
        }
        //验证密码校验
        CommonRespon response = req.validatePassword();
        if (!response.isSuccess()) {
            return response;
        }

        //username不能重复
        AuthUser dbUser = this.findByUsername(req.getUsername());
        if (dbUser != null) {
            return CommonRespon.failure(ResponseCodeEnum.EXIST_SAME_USERNAME);
        }
        Integer parentId = isRoot ? null : operationUser.getId();
        String hashed = BCrypt.hashpw(req.decodePassword(), BCrypt.gensalt());
        //只有超级账号分组和运营分组的账号再创建用户时才可以指定分组
        AuthTypeCode authTypeCode = (isRoot || isOperation) ? req.getAuthTypeCode() : operationUser.getAuthTypeCode();
        String clubRandomId = req.getClubRandomId() == null ? null : req.getClubRandomId().trim();
        AuthUser authUser = AuthUser.builder()
                .parentId(parentId)
                .authTypeCode(authTypeCode.name())
                .nickname(req.getUsername())
                .username(req.getUsername())
                .password(hashed)
                .status(1)
                .mobileAreaCode(req.getMobileAreaCode())
                .mobileNo(req.getMobileNo())
                .remark(req.getRemark())
                .clubRandomId(clubRandomId)
                .tribeOwner(req.getTribeOwner())
                .createdBy(operationUser.getId())
                .updatedBy(operationUser.getId())
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .deleted(false)
                .build();

        authUserDao.insert(authUser);

        return CommonRespon.success();
    }

    @Override
    public PageBean<UserResp> listUsers(ListUserReq req) {
        UserInfo operationUser = req.getOperationUser();
        AuthTypeCode authTypeCode = (operationUser.isRoot() || operationUser.isOperation()) ? req.getAuthTypeCode() : null;

        AuthUserExample example = new AuthUserExample();
        AuthUserExample.Criteria criteria = example.or();
        if (authTypeCode != null) {
            //超级账号分组查询
            criteria.andAuthTypeCodeEqualTo(authTypeCode.name());
        }
        //超级账号可以查看所有账号，其他账号只能查看自己创建的账号
        if (!operationUser.isRoot()) {
            criteria.andParentIdEqualTo(operationUser.getId());
        }
        criteria.andDeletedNotEqualTo(true);
        //分页
        PageHelper.startPage(req.getPage(), req.getSize());
        Page<AuthUser> page = new Page<>();
        if (authTypeCode != null && authTypeCode.name().equals("CLUB")) {
            page = authUserDao.getAuthUserPage(!operationUser.isRoot()?operationUser.getId():null, authTypeCode.name());
        } else {
            page = (Page<AuthUser>) authUserDao.selectByExample(example);
        }
        List<ClubRecord> clubRecordList = new ArrayList<>();
        if (authTypeCode != null && authTypeCode.name().equals("CLUB") && page.size() > 0) {
            List<Integer> randomIdList = page.stream().filter((r) -> !StringUtils.isEmpty(r.getClubRandomId()) || !StringUtils.isEmpty(r.getPClubRandomId()))
                    .map((r) -> {
                        if (!StringUtils.isEmpty(r.getClubRandomId())) {
                            return Integer.parseInt(r.getClubRandomId());
                        } else if (!StringUtils.isEmpty(r.getPClubRandomId())){
                            return Integer.parseInt(r.getPClubRandomId());
                        }
                        return 0;
                    }).collect(Collectors.toList());
            if (randomIdList.size() > 0) {
                ClubRecordExample clubRecordExample = new ClubRecordExample();
                ClubRecordExample.Criteria clubCriteria = clubRecordExample.or();
                clubCriteria.andRandomIdIn(randomIdList);
                clubRecordList = clubRecordDao.selectByExample(clubRecordExample);
            }
        }
        Set<Integer> authUserIds = page.stream().map(AuthUser::getId).collect(Collectors.toSet());
        //查询用户拥有权限的系统
        Map<Integer, Set<AuthSystemCode>> systemCodeMap = authUserPermissionService.findUserAuthSystemCode(authUserIds);
        Map<AuthSystemCode, AuthSystem> allSystemMap = authSystemService.findAllMap();

        List<ClubRecord> finalClubRecordList = clubRecordList;
        List<UserResp> list = page.stream().map(authUser -> {
            //获取拥有权限的系统代码及名称
            List<AuthSystemResp> authSystemResps = systemCodeMap.getOrDefault(authUser.getId(), new HashSet<>())
                    .stream().map(systemCode -> {
                        AuthSystem authSystem = allSystemMap.get(systemCode);
                        return new AuthSystemResp(authSystem.getCode(), authSystem.getName());
                    }).collect(Collectors.toList());
            AtomicReference<String> clubName = new AtomicReference<>("");
            if (finalClubRecordList != null && finalClubRecordList.size() > 0) {
                finalClubRecordList.forEach(clubRecord -> {
                    if (!StringUtils.isEmpty(authUser.getClubRandomId())) {
                        if (clubRecord.getRandomId().toString().equals(authUser.getClubRandomId())) {
                            clubName.set(clubRecord.getName());
                        }
                    } else if (!StringUtils.isEmpty(authUser.getPClubRandomId())) {
                        if (authUser.getIsClubChild() == 1 && clubRecord.getRandomId().toString().equals(authUser.getPClubRandomId())) {
                            clubName.set(clubRecord.getName());
                        }
                    }
                });
            }
            return UserResp.builder()
                    .id(authUser.getId())
                    .authTypeCode(AuthTypeCode.valueOf(authUser.getAuthTypeCode()))
                    .username(authUser.getUsername())
                    .status(authUser.getStatus())
                    .mobileAreaCode(authUser.getMobileAreaCode())
                    .mobileNo(authUser.getMobileNo())
                    .remark(authUser.getRemark())
                    .clubRandomId(authUser.getClubRandomId())
                    .tribeOwner(authUser.getTribeOwner())
                    .clubName(clubName.get())
                    .pClubRandomId(authUser.getPClubRandomId())
                    .isClubChild(authUser.getIsClubChild())
                    .authSystems(authSystemResps)
                    .totpEnable(authUser.getTotpEnable())
                    .build();
        }).collect(Collectors.toList());


        return PageBean.of(page.getTotal(), req.getPage(), req.getSize(), list);
    }

    @Override
    public CommonRespon updateBaseInfo(UpdateUserReq req) {
        UserInfo operationUser = req.getOperationUser();
        boolean isRoot = AuthTypeCode.ROOT.equals(operationUser.getAuthTypeCode());
        AuthUser authUser = authUserDao.selectByPrimaryKey(req.getUserId());
        if (authUser == null) {
            return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
        }
        if (!isRoot && !isDescendant(operationUser.getId(), req.getUserId())) {
            //不是超级管理员并且修改的不是子/孙级账号
            return CommonRespon.failure(ResponseCodeEnum.PERMISSION_ACCESS_DENIED);
        }
        boolean hasPassword = Strings.isNotBlank(req.getPassword());
        if (hasPassword) {
            //校验password
            CommonRespon response = req.validatePassword();
            if (!response.isSuccess()) {
                return response;
            }
        }
        String clubRandomId = req.getClubRandomId() == null ? null : req.getClubRandomId().trim();
        authUser.setClubRandomId(clubRandomId);
        authUser.setTribeOwner(req.getTribeOwner());
        authUser.setMobileAreaCode(req.getMobileAreaCode());
        authUser.setMobileNo(req.getMobileNo());
        authUser.setRemark(req.getRemark());
        if (hasPassword) {
            String hashed = BCrypt.hashpw(req.decodePassword(), BCrypt.gensalt());
            authUser.setPassword(hashed);
        }

        if (req.getTotpEnable() != null && req.getTotpEnable() == 0) {
                authUser.setTotpSecret(null);
                authUser.setTotpEnable(0);
        }

        authUserDao.updateByPrimaryKey(authUser);
        //修改用户信息，重新刷新token缓存的用户数据
        userInfoService.refreshPermissions(Collections.singleton(authUser.getId()));
        return CommonRespon.success();
    }

    @Override
    public CommonRespon<List<SystemPermissionResp>> listSystemPermissions(ListSystemPermissionReq req) {
        //校验能否查看
        UserInfo operationUser = req.getOperationUser();
        boolean canNotView = !operationUser.isRoot() && !operationUser.getId().equals(req.getUserId()) && !isAncestor(operationUser.getId(), req.getUserId());
        if (canNotView) {
            //不是超级账号，查看的也不是自己或自己的后代的权限列表，无权查看
            return CommonRespon.failure(ResponseCodeEnum.PERMISSION_ACCESS_DENIED);
        }

        AuthUser authUser = authUserDao.selectByPrimaryKey(req.getUserId());
        if (authUser == null) {
            return CommonRespon.failure(ResponseCodeEnum.DATA_EMPT);
        }

        List<SystemPermissionResp> systemPermissions = new ArrayList<>();
        for (AuthSystemCode systemCode : AuthSystemCode.values()) {
            SystemPermissionResp systemPermission = getSystemPermission(authUser, systemCode);
            systemPermissions.add(systemPermission);
        }

        //移除没有可分配权限的系统
        systemPermissions.removeIf(systemPermission -> systemPermission.getPermissionGroups().isEmpty());

        return CommonRespon.success(systemPermissions);
    }

    @Transactional(transactionManager = "yunyingTransactionManager", rollbackFor = RuntimeException.class)
    @Override
    public CommonRespon updatePermissions(EditPermissionsReq req) {
        UserInfo operationUser = req.getOperationUser();
        AuthUser authUser = authUserDao.selectByPrimaryKey(req.getUserId());
        boolean notPermission = !operationUser.isRoot() && !operationUser.getId().equals(authUser.getParentId());
        if (notPermission) {
            //不是超级账号 或者 修改的不是直接的子账号
            return CommonRespon.failure(ResponseCodeEnum.PERMISSION_ACCESS_DENIED);
        }
        //查询该用户最大能配置的权限
        List<Integer> maxAllowPermissionId = getMaxAllowUserPermissions(authUser, null)
                .stream().map(AuthPermission::getId).collect(Collectors.toList());
        if (!maxAllowPermissionId.containsAll(req.getPermissionIds())) {
            return CommonRespon.failure(ResponseCodeEnum.OUT_OF_ALLOW_PERMISSION_RANGE);
        }

        //查询所有旧权限
        Set<Integer> oldPermissionIds = authPermissionService.findUserPermissionIds(authUser.getId());
        //隐藏的权限只能通过代码方式删除
        Set<Integer> hiddenPermissionIds = authPermissionService.findUserHiddenPermissionIds(authUser.getId());
        oldPermissionIds.removeAll(hiddenPermissionIds);

        //交集，保持原样
        Sets.SetView<Integer> intersection = Sets.intersection(req.getPermissionIds(), oldPermissionIds);
        //需要删除的权限ID集合
        Set<Integer> needDeletePermissionIds = new HashSet<>(oldPermissionIds);
        needDeletePermissionIds.removeAll(intersection);
        //需要新增的权限ID集合
        Set<Integer> needInsertPermissionIds = new HashSet<>(req.getPermissionIds());
        needInsertPermissionIds.removeAll(intersection);

        //删除 用户权限 以及 后代账号的权限
        Set<Integer> descendantUserIds = this.findDescendantUsers(authUser.getId()).stream()
                .map(AuthUser::getId).collect(Collectors.toSet());
        //删除父账号的权限
        authUserPermissionService.delete(operationUser, Collections.singleton(authUser.getId()), needDeletePermissionIds);
        //新增 用户权限
        authUserPermissionService.insert(operationUser.getId(), authUser.getId(), needInsertPermissionIds);
        //更新所有单选分组的权限：OSM时间权限/手工充豆权限/CMS时间权限
        updateAllRadioGroupPermissions(operationUser, req.getUserId(), descendantUserIds);
        //删除后代账号的权限
        Set<Integer> notTimePermissionIds = removeRadioGroupPermissionIds(needDeletePermissionIds);
        authUserPermissionService.delete(operationUser, descendantUserIds, notTimePermissionIds);

        //更新权限
        Set<Integer> allChangeUserIds = new HashSet<>(descendantUserIds);
        allChangeUserIds.add(authUser.getId());
        userInfoService.refreshPermissions(allChangeUserIds);

        return CommonRespon.success();
    }

    /**
     * 更新所有单选分组权限
     *
     * @param operationUser 操作者
     * @param userId 当前操作的用户ID
     * @param descendantUserIds 后代账号的ID
     */
    private void updateAllRadioGroupPermissions(UserInfo operationUser, Integer userId, Set<Integer> descendantUserIds) {
        List<RadioGroupPermission> radioGroupPermissions = allRadioGroupPermissionCodes();
        for (RadioGroupPermission radioGroupPermission : radioGroupPermissions) {
            updateRadioGroupPermission(operationUser, radioGroupPermission, userId, descendantUserIds);
        }
    }

    private Set<Integer> removeRadioGroupPermissionIds(Set<Integer> permissionIds) {
        permissionIds = new HashSet<>(permissionIds);
        //删除单选分组相关的权限ID，避免后面被删除
        List<EPermissionCode> permissionCodes = allRadioGroupPermissionCodes().stream().flatMap(v -> v.getPermissionCodes().stream()).collect(Collectors.toList());

        List<AuthPermission> permissions = authPermissionService.findByPermissionCodes(permissionCodes);
        Set<Integer> radioGroupPermissionIds = permissions.stream().map(AuthPermission::getId).collect(Collectors.toSet());
        permissionIds.removeAll(radioGroupPermissionIds);
        return permissionIds;
    }

    /**
     * 更新单选分组的权限
     *
     * @param operationUser 操作者
     * @param radioGroupPermission 同一个权限分组的code
     * @param userId 当前操作的用户
     * @param descendantUserIds 后台账号的ID
     */
    private void updateRadioGroupPermission(UserInfo operationUser, RadioGroupPermission radioGroupPermission, Integer userId, Set<Integer> descendantUserIds) {
        List<AuthPermission> permissions = authPermissionService.findUserPermissions(userId, radioGroupPermission.getSystemCode());
        //子账号可选权限
        List<EPermissionCode> allowPermissionCodes = childsRadioPermissionCodes(permissions, radioGroupPermission.getPermissionCodes());
        //子账号不允许的时间权限
        List<EPermissionCode> notAllowPermissionCodes = new ArrayList<>(radioGroupPermission.getPermissionCodes());
        notAllowPermissionCodes.removeAll(allowPermissionCodes);
        log.info("updateRadioPermission => descendant allow permissions：{}，not allow permissions：{}, descendantUserIds={}", allowPermissionCodes, notAllowPermissionCodes, descendantUserIds);
        //获取后代账号不允许的时间权限
        List<AuthUserPermission> authUserPermissions = authUserPermissionService.findDescendantUserPermissions(descendantUserIds, notAllowPermissionCodes);
        if (authUserPermissions.isEmpty()) {
            return;
        }
        if (allowPermissionCodes.isEmpty()) {
            //子账号没有可选的时间权限，直接删除已有的时间权限
            Set<Integer> permissionIds = authUserPermissions.stream().map(AuthUserPermission::getAuthPermissionId).collect(Collectors.toSet());
            log.info("updateRadioPermission => descendant delete permissions: descendantUserIds={}, permissionIds={}", descendantUserIds, permissionIds);
            authUserPermissionService.delete(operationUser, descendantUserIds, permissionIds);
            return;
        }
        //子账号还可以选择其他允许的时间权限，超出范围的权限修改为允许的时间权限
        AuthPermission maxAllowPermission = authPermissionService.findByPermissionCode(allowPermissionCodes.get(0));
        Set<Integer> authUserPermissionIds = authUserPermissions.stream().map(AuthUserPermission::getId).collect(Collectors.toSet());
        log.info("updateRadioPermission => change descendantUser permissions: " +
                "descendantUserIds={}, authUserPermissionIds={}, maxAllowPermissionId={}", descendantUserIds, authUserPermissionIds, maxAllowPermission.getId());
        authUserPermissionService.updatePermissionId(authUserPermissionIds, maxAllowPermission.getId());
    }

    private List<RadioGroupPermission> allRadioGroupPermissionCodes() {
        //所有权限都应该从大到小
        List<RadioGroupPermission> lists = new ArrayList<>();

        //OSM时间权限：时间范围从大到小
        lists.add(new RadioGroupPermission(AuthSystemCode.OSM, Lists.newArrayList(
                EPermissionCode.osm_time_limit_no,
                EPermissionCode.osm_time_limit_35,
                EPermissionCode.osm_time_limit_7,
                EPermissionCode.osm_time_limit_3,
                EPermissionCode.osm_time_limit_0
        )));

        //OSM手工充豆权限；权限由高到底
        lists.add(new RadioGroupPermission(AuthSystemCode.OSM, Lists.newArrayList(
                EPermissionCode.osm_manual_recharge_chip_supper_permission,
                EPermissionCode.osm_manual_recharge_chip_platform_permission,
                EPermissionCode.osm_manual_recharge_chip_operation_permission,
                EPermissionCode.osm_manual_recharge_chip_customer_service_permission,
                EPermissionCode.osm_manual_recharge_chip_no_permission
        )));

        //CMS时间权限：时间范围由大到小
        lists.add(new RadioGroupPermission(AuthSystemCode.CMS, Lists.newArrayList(
                EPermissionCode.cms_time_limit_no,
                EPermissionCode.cms_time_limit_35,
                EPermissionCode.cms_time_limit_7,
                EPermissionCode.cms_time_limit_3,
                EPermissionCode.cms_time_limit_0
        )));

        return lists;
    }


    @Transactional(transactionManager = "yunyingTransactionManager", rollbackFor = RuntimeException.class)
    @Override
    public CommonRespon disableUser(UserIdReq req) {
        UserInfo operationUser = req.getOperationUser();
        boolean notPermission = !operationUser.isRoot() && !isDescendant(operationUser.getId(), req.getUserId());
        if (notPermission) {
            //不是超级账号 或者 停用的不是后代账号 或者 停用自己
            return CommonRespon.failure(ResponseCodeEnum.PERMISSION_ACCESS_DENIED);
        }
        Set<Integer> needDisableIds = this.findDescendantUsers(req.getUserId())
                .stream().filter(authUser -> authUser.getStatus().equals(AuthUserStatus.ENABLE.getCode()))
                .map(AuthUser::getId).collect(Collectors.toSet());
        needDisableIds.add(req.getUserId());

        authUserDao.updateStatusIn(needDisableIds, AuthUserStatus.DISABLE.getCode(), operationUser.getId());

        //停用账号需要把对应的token置为失效（从redis中移除即可）
        for (Integer userId : needDisableIds) {
            String redisUserKey = config.redisUserKey(userId, AuthSystemCode.OSM);
            Set<Object> members = redisTemplate.opsForSet().members(redisUserKey);
            Set<String> deleteKeys = new HashSet<>();
            deleteKeys.add(redisUserKey);
            Optional.ofNullable(members).ifPresent(keys -> {
                Set<String> set = keys.stream().map(Object::toString).collect(Collectors.toSet());
                deleteKeys.addAll(set);
            });

            redisTemplate.delete(deleteKeys);
        }
        return CommonRespon.success();
    }

    @Override
    public CommonRespon enableUser(EnableUserStatusReq req) {
        UserInfo operationUser = req.getOperationUser();
        boolean notPermission = !operationUser.isRoot() && !isDescendant(operationUser.getId(), req.getUserId());
        if (notPermission) {
            //不是超级账号 或者 启用的不是后代账号
            return CommonRespon.failure(ResponseCodeEnum.PERMISSION_ACCESS_DENIED);
        }

        Set<Integer> needEnableIds = this.findAncestorUsers(req.getUserId())
                .stream().filter(authUser -> authUser.getStatus().equals(AuthUserStatus.DISABLE.getCode()))
                .map(AuthUser::getId).collect(Collectors.toSet());
        if (!needEnableIds.isEmpty() && !req.getEnableParent()) {
            //有祖先账号未启用，并且请求没有要求启用祖先账号，操作拒绝
            return CommonRespon.failure(ResponseCodeEnum.NEED_ENABLE_ANCESTOR_USER);
        }

        needEnableIds.add(req.getUserId());

        if (req.getEnableChild()) {
            //确认启用子账号
            Set<Integer> childIds = this.findChilds(req.getUserId())
                    .stream().filter(authUser -> authUser.getStatus().equals(AuthUserStatus.DISABLE.getCode()))
                    .map(AuthUser::getId).collect(Collectors.toSet());
            needEnableIds.addAll(childIds);
        }

        authUserDao.updateStatusIn(needEnableIds, AuthUserStatus.ENABLE.getCode(), operationUser.getId());
        return CommonRespon.success();
    }

    @Override
    public CommonRespon deleteUser(UserIdReq req) {
        UserInfo operationUser = req.getOperationUser();
        boolean notPermission = !operationUser.isRoot() && !isDescendant(operationUser.getId(), req.getUserId());
        if (notPermission) {
            //不是超级账号 或者 删除的不是后代账号
            return CommonRespon.failure(ResponseCodeEnum.PERMISSION_ACCESS_DENIED);
        }
        Set<Integer> needDeleteIds = this.findDescendantUsers(req.getUserId())
                .stream().map(AuthUser::getId).collect(Collectors.toSet());
        needDeleteIds.add(req.getUserId());

        authUserDao.deleteIn(needDeleteIds, operationUser.getId());
        return CommonRespon.success();
    }

    @Override
    public CommonRespon<DisableAncestorResp> hasDisableAncestor(UserIdReq req) {
        long count = this.findAncestorUsers(req.getUserId()).stream()
                .filter(authUser -> authUser.getStatus().equals(AuthUserStatus.DISABLE.getCode()))
                .count();

        return CommonRespon.success(new DisableAncestorResp(count > 0, (int) count));
    }

    private SystemPermissionResp getSystemPermission(AuthUser authUser, AuthSystemCode systemCode) {
        List<AuthPermission> allPermissions = getMaxAllowUserPermissions(authUser, systemCode);
        //隐藏的权限不显示
        allPermissions = allPermissions.stream().filter(authPermission -> !authPermission.getHidden()).collect(Collectors.toList());

        //获取所有权限分组
        List<PermissionGroupResp> permissionGroups = authPermissionGroupService.findAll(systemCode)
                .stream().map(group -> PermissionGroupResp.builder()
                        .id(group.getId())
                        .name(group.getName())
                        .checked(true)
                        .radio(group.getRadio())
                        .permissions(new ArrayList<>())
                        .build()).collect(Collectors.toList());
        Map<Integer, PermissionGroupResp> groupMap = permissionGroups.stream()
                .collect(Collectors.toMap(PermissionGroupResp::getId, Function.identity()));

        //把权限根据关系分配到对应的分组里面
        Set<Integer> allowPermissionIds = authPermissionService.findUserPermissionIds(authUser.getId(), systemCode);
        allPermissions.forEach(permission -> {
            PermissionGroupResp group = groupMap.get(permission.getAuthPermissionGroupId());
            if (group == null) {
                return;
            }
            boolean checked = allowPermissionIds.contains(permission.getId());
            PermissionResp resp = PermissionResp.builder()
                    .id(permission.getId())
                    .name(permission.getName())
                    .checked(checked)
                    .build();
            group.setChecked(group.getChecked() && checked);
            group.getPermissions().add(resp);
        });

        //移除没有权限的权限组
        permissionGroups.removeIf(group -> group.getPermissions().isEmpty());

        //获取系统信息
        AuthSystem authSystem = authSystemDao.selectByPrimaryKey(systemCode.name());
        return new SystemPermissionResp(authSystem.getCode(), authSystem.getName(), permissionGroups);
    }

    /**
     * 获取该用户最大允许的权限集合
     *
     * @param authUser 用户
     * @param systemCode 登录的系统，可空
     * @return 涉及的权限
     */
    private List<AuthPermission> getMaxAllowUserPermissions(AuthUser authUser, AuthSystemCode systemCode) {
        //超级用户默认显示所有权限可配，非超级管理员只能显示父用户的权限可配
        boolean isRoot = AuthTypeCode.ROOT.name().equals(authUser.getAuthTypeCode());
        List<AuthPermission> allPermissions;
        if (isRoot || authUser.getParentId() == null) {
            //超级账号，或由超级账号创建的用户
            allPermissions = authPermissionService.findAll();
        } else {
            allPermissions = authPermissionService.findUserPermissions(authUser.getParentId(), systemCode);
        }
        //注意：如果父账号的CMS时间限制为：无限制，则子账号可以选则无限制，7天，3天
        Set<Integer> allPermissionIds = allPermissions.stream().map(AuthPermission::getId).collect(Collectors.toSet());
        List<AuthPermission> finalAllPermissions = allPermissions;
        List<EPermissionCode> permissionCodes = allRadioGroupPermissionCodes().stream()
                .filter(v -> systemCode == null || v.getSystemCode().equals(AuthSystemCode.OSM) || v.getSystemCode().equals(AuthSystemCode.CMS))
                .flatMap(v -> childsRadioPermissionCodes(finalAllPermissions, v.getPermissionCodes()).stream())
                .collect(Collectors.toList());
        List<AuthPermission> radioGroupPermissions = authPermissionService.findByPermissionCodes(permissionCodes);
        log.info("permissionCodes: {}", permissionCodes);
        for (AuthPermission timeLimitPermission : radioGroupPermissions) {
            if (!allPermissionIds.contains(timeLimitPermission.getId())) {
                allPermissions.add(timeLimitPermission);
                allPermissionIds.add(timeLimitPermission.getId());
            }
        }

        //移除掉不支持该用户类型的权限
        authPermissionStandByService.removeNotAllow(allPermissionIds, AuthTypeCode.valueOf(authUser.getAuthTypeCode()));
        allPermissions = allPermissions.stream().filter(permission -> allPermissionIds.contains(permission.getId())).collect(Collectors.toList());
        return allPermissions;
    }

    /**
     * 返回子账号可选的权限
     *
     * @param allPermissions 所有权限
     * @param oneGroupRadioPermissionCodes 同一个权限分组的code
     * @return 后代账号最大可选的权限
     */
    private List<EPermissionCode> childsRadioPermissionCodes(List<AuthPermission> allPermissions, List<EPermissionCode> oneGroupRadioPermissionCodes) {
        Set<String> allCodes = allPermissions.stream().map(AuthPermission::getCode).filter(Objects::nonNull).collect(Collectors.toSet());
        int index = -1;
        for (int i = 0; i < oneGroupRadioPermissionCodes.size(); i++) {
            if (allCodes.contains(oneGroupRadioPermissionCodes.get(i).getCode())) {
                index = i;
                break;
            }
        }
        if (index != -1) {
            return new ArrayList<>(oneGroupRadioPermissionCodes.subList(index, oneGroupRadioPermissionCodes.size()));
        }
        return new ArrayList<>();
    }

    /**
     * 判断 descendantId 是否为 ancestorId 的后代
     *
     * @param ancestorId   祖先ID
     * @param descendantId 后代ID
     * @return descendantId是否为ancestorId的后代
     */
    private boolean isDescendant(Integer ancestorId, Integer descendantId) {
        List<AuthUser> descendantUsers = this.findDescendantUsers(ancestorId);
        return descendantUsers.stream().anyMatch(user -> user.getId().equals(descendantId));
    }

    /**
     * 判断 ancestorId 是否为 descendantId 的祖先
     *
     * @param ancestorId   祖先ID
     * @param descendantId 后代ID
     * @return ancestorId是否为descendantId的祖先
     */
    private boolean isAncestor(Integer ancestorId, Integer descendantId) {
        List<AuthUser> ancestorUsers = this.findAncestorUsers(descendantId);
        return ancestorUsers.stream().anyMatch(user -> user.getId().equals(ancestorId));
    }

    /**
     * 查找后代
     *
     * @param ancestorId 查找该用户的后代
     * @return 返回后代用户
     */
    private List<AuthUser> findDescendantUsers(Integer ancestorId) {
        List<AuthUser> descendantUsers = new ArrayList<>();
        this.findDescendantUsers(descendantUsers, ancestorId);
        return descendantUsers;
    }

    private List<AuthUser> findAncestorUsers(Integer descendantId) {
        List<AuthUser> ancestorUsers = new ArrayList<>();
        AuthUser descendantUser = authUserDao.selectByPrimaryKey(descendantId);
        this.findAncestorUsers(ancestorUsers, descendantUser.getParentId());
        return ancestorUsers;
    }

    private void findAncestorUsers(List<AuthUser> ancestorUsers, Integer parentId) {
        if (parentId == null) {
            return;
        }
        AuthUser parent = authUserDao.selectByPrimaryKey(parentId);
        if (parent == null) {
            return;
        }
        ancestorUsers.add(parent);
        findAncestorUsers(ancestorUsers, parent.getParentId());
    }

    private void findDescendantUsers(List<AuthUser> descendantUsers, Integer ancestorId) {
        List<AuthUser> childs = this.findChilds(ancestorId);
        if (childs.isEmpty()) {
            return;
        }
        descendantUsers.addAll(childs);
        for (AuthUser child : childs) {
            findDescendantUsers(descendantUsers, child.getId());
        }
    }

    private List<AuthUser> findChilds(Integer parentId) {
        AuthUserExample example = new AuthUserExample();
        example.or().andParentIdEqualTo(parentId)
                .andDeletedNotEqualTo(true);
        return authUserDao.selectByExample(example);
    }

    @Override
    public CommonRespon updatePassword(UpdatePasswordReq req) {
        CommonRespon response = req.validatePassword();
        if (!response.isSuccess()) {
            return response;
        }

        String hashed = BCrypt.hashpw(req.decodePassword(), BCrypt.gensalt());
        AuthUser authUser = new AuthUser();
        authUser.setId(req.getOperationUser().getId());
        authUser.setPassword(hashed);
        authUserDao.updateByPrimaryKeySelective(authUser);
        return CommonRespon.success();
    }

    @Override
    public CommonRespon updateTotp(UpdateTotpReq req) {
        Number authUserId = req.getOperationUser().getId();
        AuthUser authUser = authUserDao.selectByPrimaryKey(authUserId.intValue());

        if (req.getTotpEnable() != null) {
            if (req.getTotpEnable() == 1) {
                Map<String, String> responseData = new HashMap<>();

                if (req.getTotpConfirm() != null && req.getTotpConfirm() == 1) {
                    boolean isTotpValid = TotpUtils.verifyTotpCode(req.getTotpCode(), authUser.getTotpSecret());
                    if (!isTotpValid) {
                        return CommonRespon.failure(ResponseCodeEnum.TOTP_CODE_INVALID);
                    }

                    authUser.setTotpEnable(1);
                } else {
                    // 启用 TOTP：生成密钥并返回二维码
                    String secret = TotpUtils.genSecret();
                    String issuer = req.getOperationUser().getAuthTypeCode() == AuthTypeCode.TRIBE || req.getOperationUser().getAuthTypeCode() == AuthTypeCode.CLUB ? "CMS" : "OSM";
                    String qrCode = TotpUtils.generateQrCode(secret, req.getOperationUser().getNickname(), issuer);

                    // 保存到数据库
                    authUser.setTotpSecret(secret);
                    responseData.put("secret", secret);
                    responseData.put("qrCode", qrCode);

                }

                authUserDao.updateByPrimaryKey(authUser);

                // 返回响应，包含 secret 和 qrCode
                return CommonRespon.success(responseData);
            }
            else if (req.getTotpEnable() == 0) {
                // 禁用 TOTP：将数据库中的密钥置为 null
                authUser.setTotpSecret(null);
                authUser.setTotpEnable(0);
                authUserDao.updateByPrimaryKey(authUser);

                // 返回成功响应
                return CommonRespon.success();
            }
        }

        return CommonRespon.failure(ResponseCodeEnum.ERROR);
    }

    @Override
    public void addPayChannelPermissions(Integer authUserId) {
        //支付渠道需要这些权限
        List<EPermissionCode> permissionCodes = Lists.newArrayList(
                EPermissionCode.cms_pay_channel_manage,
                EPermissionCode.cms_extract_king_dou,
                EPermissionCode.cms_pay_channel_abnormal_order
        );
        AuthSystemCode systemCode = AuthSystemCode.CMS;

        List<AuthPermission> permissions = authPermissionService.findByPermissionCodes(permissionCodes);
        Set<Integer> permissionIds = permissions.stream().map(AuthPermission::getId).collect(Collectors.toSet());

        //获取用户已拥有的权限, CMS系统
        Set<Integer> userPermissionIds = authPermissionService.findUserPermissionIds(authUserId, systemCode);
        //移除已分配到的权限
        permissionIds.removeAll(userPermissionIds);
        if (permissionIds.isEmpty()) {
            //这些权限均已分配
            return;
        }

        List<AuthUserPermission> userPermissions = permissionIds.stream().map(permissionId -> AuthUserPermission.builder()
                .authUserId(authUserId)
                .authSystemCode(systemCode.name())
                .authPermissionId(permissionId)
                .createdBy(-1)
                .updatedBy(-1)
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .deleted(false)
                .build()).collect(Collectors.toList());
        authUserPermissionService.batchInsert(userPermissions);

        //更新权限
        userInfoService.refreshPermissions(Collections.singleton(authUserId));
    }

    @Override
    public Map<Integer, AuthUser> findUserMapByUserIds(Collection<Integer> userIds) {
        if (userIds.isEmpty()) {
            return new HashMap<>(0);
        }
        AuthUserExample example = new AuthUserExample();
        example.or().andIdIn(new ArrayList<>(userIds));
        List<AuthUser> authUsers = authUserDao.selectByExample(example);
        return authUsers.stream().collect(Collectors.toMap(AuthUser::getId, v -> v));
    }

    @Override
    public CommonRespon<LoginResp> login(LoginReq req) {
        String username = req.getUsername();
        AuthUser user = this.findByUsername(username);
        if (user == null) {
            return CommonRespon.failure(ResponseCodeEnum.USERNAME_OR_PASSWORD_ERROR);
        }
        boolean isRoot = AuthTypeCode.ROOT.name().equals(user.getAuthTypeCode());
        //校验密码是否一致
        String password = new String(Base64.getDecoder().decode(req.getPassword()));
        boolean isPasswordEquals = BCrypt.checkpw(password, user.getPassword());
        if (!isPasswordEquals) {
            ResponseCodeEnum responseCode = isRoot ? ResponseCodeEnum.LOGIN_FAILURE : ResponseCodeEnum.USERNAME_OR_PASSWORD_ERROR;
            return CommonRespon.failure(responseCode);
        }

        String ip = IpUtils.getIp(req.getRequest());

        AuthSystemCode systemCode = AuthSystemCode.valueOf(req.getSystemCode());
        // OSM
        if (AuthSystemCode.OSM.equals(systemCode)) {
            // OSM 用公共的白名单
            Integer ipListCount = loginWhiteIpDao.getOSMPublicWhiteCount();
            if (ipListCount != null && ipListCount > 0) {
                // 有设置白名单，需要校验登录IP
                if (StringUtils.isEmpty(ip)) {
                    log.error("获取IP失败， ip: {}", ip);
                    return CommonRespon.failure(ResponseCodeEnum.PARAM_IP_FAILURE);
                }
                Integer check = loginWhiteIpDao.checkOSMPublicWhiteCount(ip);
                if (check <= 0) {
                    // 登录IP不在白名单内
                    log.error("OSM登录失败，ip不在白名单内， ip: {}", ip);
                    return CommonRespon.failure(ResponseCodeEnum.LOGIN_IP_NOT_IN_WHITE_LIST);
                }
            }
            log.info("OSM登录ip校验成功， ip: {}， userName: {}", ip, req.getUsername());
        } else if (AuthSystemCode.CMS.equals(systemCode)) {
            // CMS 用账号所属的白名单
            Integer ipListCount = loginWhiteIpDao.getCMSPrivateWhiteCount(user.getId());
            if (ipListCount != null && ipListCount > 0) {
                // 有设置白名单，需要校验登录IP
                if (StringUtils.isEmpty(ip)) {
                    log.error("获取IP失败， ip: {}", ip);
                    return CommonRespon.failure(ResponseCodeEnum.PARAM_IP_FAILURE);
                }
                Integer check = loginWhiteIpDao.checkCMSPrivateWhiteCount(user.getId(), ip);
                if (check <= 0) {
                    // 登录IP不在白名单内
                    log.error("CMS登录失败，ip不在白名单内， ip: {}", ip);
                    return CommonRespon.failure(ResponseCodeEnum.LOGIN_IP_NOT_IN_WHITE_LIST);
                }
            } else {
                if (user.getParentId() != null) {
                    ipListCount = loginWhiteIpDao.getCMSPrivateWhiteCount(user.getParentId());
                    if (ipListCount != null && ipListCount > 0) {
                        // 有设置白名单，需要校验登录IP
                        if (StringUtils.isEmpty(ip)) {
                            log.error("获取IP失败， ip: {}", ip);
                            return CommonRespon.failure(ResponseCodeEnum.PARAM_IP_FAILURE);
                        }
                        Integer check = loginWhiteIpDao.checkCMSPrivateWhiteCount(user.getParentId(), ip);
                        if (check <= 0) {
                            // 登录IP不在白名单内
                            log.error("CMS登录失败，ip不在白名单内， ip: {}", ip);
                            return CommonRespon.failure(ResponseCodeEnum.LOGIN_IP_NOT_IN_WHITE_LIST);
                        }
                    }
                }
            }
            log.info("CMS登录ip校验成功， ip: {}， userName: {}", ip, req.getUsername());
        }


        if (user.getTotpEnable() == 1) {
            if (StringUtils.isEmpty(req.getTotpCode())) {
                return CommonRespon.failure(ResponseCodeEnum.TOTP_CODE_REQUIRED);
            }

            boolean isTotpValid = TotpUtils.verifyTotpCode(req.getTotpCode(), user.getTotpSecret());
            if (!isTotpValid) {
                log.info("auth user login, {} is root account, totp code error", req.getUsername());
                return CommonRespon.failure(ResponseCodeEnum.TOTP_CODE_INVALID);
            }
        }

        //校验账号是否被停用
        if (user.getStatus().equals(AuthUserStatus.DISABLE.getCode())) {
            ResponseCodeEnum responseCode = isRoot ? ResponseCodeEnum.LOGIN_FAILURE : ResponseCodeEnum.AUTH_USER_DISABLE;
            return CommonRespon.failure(responseCode);
        }

        //该用户允许的角色
        UserInfo info = userInfoService.genTokenSaveRedis(user, systemCode, ip);

        List<MenuResp> menus = authUserPermissionService.findAuthUserMenus(info.getId(), info.getSystemCode());
        LoginResp loginInfo = new LoginResp();
        loginInfo.setUserId(info.getId());
        loginInfo.setNickname(info.getNickname());
        loginInfo.setTotpEnable(info.getTotpEnable());
        loginInfo.setAuthTypeCode(info.getAuthTypeCode());
        loginInfo.setClubTribeCreator(info.getClubTribeCreator());
        loginInfo.setToken(info.getToken());
        loginInfo.setMenus(menus);

        Set<String> dataPermissions = authUserPermissionService.findAuthUserDataPermission(info.getId(), info.getSystemCode());
        loginInfo.setDataPermissions(dataPermissions);

        return CommonRespon.success(loginInfo);
    }

    @Override
    public CommonRespon sendSmsForRoot(SendRootSmsReq req) {
        String username = req.getUsername();
        AuthUser user = this.findByUsername(username);
        if (user == null) {
            return CommonRespon.failure(ResponseCodeEnum.USERNAME_OR_PASSWORD_ERROR);
        }
        boolean isRoot = AuthTypeCode.ROOT.name().equals(user.getAuthTypeCode());
        if (!isRoot) {
            return CommonRespon.failure(ResponseCodeEnum.PERMISSION_ACCESS_DENIED);
        }
        //校验密码是否一致
        String password = new String(Base64.getDecoder().decode(req.getPassword()));
        boolean isPasswordEquals = BCrypt.checkpw(password, user.getPassword());
        if (!isPasswordEquals) {
            return CommonRespon.failure(ResponseCodeEnum.USERNAME_OR_PASSWORD_ERROR);
        }
        //校验账号是否被停用
        if (user.getStatus().equals(AuthUserStatus.DISABLE.getCode())) {
            return CommonRespon.failure(ResponseCodeEnum.AUTH_USER_DISABLE);
        }
        //发送验证码
        smsService.sendSmsCode(user.getMobileNo(), user.getMobileAreaCode(), ESmsType.ROOT_ACCOUNT_LOGIN.getValue());
        return CommonRespon.success();
    }

    @Override
    public void logout(String token) {
        if (StringUtils.isEmpty(token)) {
            return;
        }
        try {
            //从redis中删除token
            TokenUtils.Info info = TokenUtils.getInfo(token, config.getTokenSecret());
            String redisKey = config.redisTokenKey(info.getRedisKey(), AuthSystemCode.OSM);
            redisTemplate.opsForValue().getOperations().delete(redisKey);
            //加入黑名单
            String blackKey = config.redisBlacklistKey(redisKey, AuthSystemCode.OSM);
            redisTemplate.opsForValue().setIfAbsent(blackKey, System.currentTimeMillis(), config.getTokenExpireMinutes(), TimeUnit.MINUTES);
            redisTemplate.opsForSet().remove(config.redisUserKey(info.getUserId(), AuthSystemCode.OSM), config.redisTokenKey(redisKey, AuthSystemCode.OSM));
        } catch (Exception e) {
            //do nothing
        }
    }


    @Override
    public PageBean<LoginWhiteIpResp> listWhiteIp(ListLoginWhiteIpReq req) {
        //分页
        PageHelper.startPage(req.getPage(), req.getSize());
        Page<LoginWhiteIp> page = loginWhiteIpDao.getOSMLoginWhiteIpPage();
        List<LoginWhiteIpResp> list = page.stream().map(whiteIp -> LoginWhiteIpResp.builder()
                .id(whiteIp.getId())
                .name(whiteIp.getName())
                .ip(whiteIp.getIp())
                .systemCode(whiteIp.getSystemCode())
                .authUserId(whiteIp.getAuthUserId())
                .createdAt(whiteIp.getCreatedAt())
                .updatedAt(whiteIp.getUpdatedAt())
                .operatorId(whiteIp.getOperatorId())
                .build()).collect(Collectors.toList());
        return PageBean.of(page.getTotal(), req.getPage(), req.getSize(), list);
    }

    @Override
    public CommonRespon getWhiteIpById(LoginWhiteIpReq req) {
        if (req == null || req.getId() == null) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_ERROR);
        }
        return CommonRespon.success(loginWhiteIpDao.getLoginWhiteIpById(req.getId()));
    }

    @Override
    public CommonRespon insertWhiteIp(LoginWhiteIpReq req) {
        if (req == null || StringUtils.isEmpty(req.getIp()) || StringUtils.isEmpty(req.getName())) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_ERROR);
        }
        LoginWhiteIp loginWhiteIp = LoginWhiteIp.builder()
                .ip(req.getIp())
                .name(req.getName())
                .authUserId(0L)
                .isHide(0)
                .status(1)
                .systemCode(AuthSystemCode.OSM.name())
                .operatorId(Long.parseLong(req.getOperationUser().getId().toString()))
                .build();
        if (loginWhiteIpDao.insertLoginWhiteIp(loginWhiteIp) > 0) {
            // 登出不在白名单的账号
            logOutNotWhiteListOSM();
            return CommonRespon.success();
        } else {
            return CommonRespon.failure(ResponseCodeEnum.ERROR);
        }
    }

    @Override
    public CommonRespon updateWhiteIp(LoginWhiteIpReq req) {
        if (req == null || req.getId() == null || StringUtils.isEmpty(req.getIp()) || StringUtils.isEmpty(req.getName())) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_ERROR);
        }
        LoginWhiteIp loginWhiteIp = LoginWhiteIp.builder()
                .id(req.getId())
                .ip(req.getIp())
                .name(req.getName())
                .authUserId(0L)
                .isHide(0)
                .status(1)
                .systemCode(AuthSystemCode.OSM.name())
                .operatorId(Long.parseLong(req.getOperationUser().getId().toString()))
                .build();
        if (loginWhiteIpDao.updateLoginWhiteIp(loginWhiteIp) > 0) {
            // 登出不在白名单的账号
            logOutNotWhiteListOSM();
            return CommonRespon.success();
        } else {
            return CommonRespon.failure(ResponseCodeEnum.ERROR);
        }
    }

    @Override
    public CommonRespon deleteWhiteIp(LoginWhiteIpReq req) {
        if (req == null || req.getId() == null) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_ERROR);
        }
        if (loginWhiteIpDao.deleteLoginWhiteIp(req.getId()) > 0) {
            // 登出不在白名单的账号
            logOutNotWhiteListOSM();
            return CommonRespon.success();
        } else {
            return CommonRespon.failure(ResponseCodeEnum.ERROR);
        }
    }

    @Override
    public CommonRespon resetWhiteIp(LoginWhiteIpReq req) {
        if (req == null || req.getAuthUserId() == null) {
            return CommonRespon.failure(ResponseCodeEnum.PARAM_ERROR);
        }
        loginWhiteIpDao.resetLoginWhiteIp(req.getAuthUserId());
        return CommonRespon.success();
    }

    /**
     * OSM登出不在白名单的账号
     */
    void logOutNotWhiteListOSM() {
        log.info("OSM登出不在白名单的账号");
        // OSM 获取所有公共白名单
        List<LoginWhiteIp> whiteList = loginWhiteIpDao.getOSMPublicWhiteList();
        Set<String> ipList = new HashSet<>();
        if (whiteList != null && whiteList.size() > 0) {
            log.info("whiteList.size: {}", whiteList.size());
            whiteList.forEach(white -> {
                if (!StringUtils.isEmpty(white.getIp())) {
                    if (white.getIp().contains(",")) {
                        ipList.addAll(Arrays.stream(white.getIp().split(",")).filter(ip -> !StringUtils.isEmpty(ip)).collect(Collectors.toSet()));
                    } else {
                        ipList.add(white.getIp());
                    }
                }
            });
        }
        log.info("ipList.size: {}", ipList.size());
        if (ipList.size() > 0) {
            // 获取除俱乐部账号外的所有账号
            AuthUserExample example = new AuthUserExample();
            AuthUserExample.Criteria criteria = example.or();
            criteria.andAuthTypeCodeNotEqualTo("CLUB");
            List<AuthUser> userList =  authUserDao.selectByExample(example);
            if (userList != null && userList.size() > 0) {
                log.info("userList.size: {}", userList.size());
                userList.forEach(user -> {
                    // 获取账号的所有登录token
                    String userKey = config.redisUserKey(user.getId(), AuthSystemCode.OSM);
                    Set<Object> tokenList = redisTemplate.opsForSet().members(userKey);
                    if (tokenList != null && tokenList.size() > 0) {
                        log.info("tokenList.size: {}", tokenList.size());
                        tokenList.forEach(key -> {
                            log.info("key: {}", key.toString());
                            // 根据token获取用户信息，有登录ip
                            if (redisTemplate.hasKey(key.toString())) {
                                UserInfo userInfo = (UserInfo) redisTemplate.opsForValue().get(key.toString());
                                if (userInfo != null && !StringUtils.isEmpty(userInfo.getLoginIp())) {
                                    log.info("userInfo.loginIp: {}", userInfo.getLoginIp());
                                    if (!ipList.contains(userInfo.getLoginIp())) {
                                        // 不在白名单内
                                        try {
                                            log.info("不在白名单内: {}", userInfo.getLoginIp());
                                            //从redis中删除token
                                            String redisKey = key.toString();
                                            redisTemplate.opsForValue().getOperations().delete(redisKey);
                                            //加入黑名单
                                            String blackKey = config.redisBlacklistKey(redisKey, AuthSystemCode.OSM);
                                            redisTemplate.opsForValue().setIfAbsent(blackKey, System.currentTimeMillis(), config.getTokenExpireMinutes(), TimeUnit.MINUTES);
                                            redisTemplate.opsForSet().remove(userKey, redisKey);
                                        } catch (Exception e) {
                                            log.error("OSM白名单变更，登出账号错误。 e:{}", e.getMessage());
                                        }
                                    }
                                }
                            }
                        });
                    }
                });
            }
        }
    }
}
