package com.allinpokers.yunyingjob.service.impl;

import com.allinpokers.yunying.util.JsonUtils;
import com.allinpokers.yunying.util.PhoneNumUtil;
import com.allinpokers.yunyingjob.dao.crazypoker.*;
import com.allinpokers.yunyingjob.entity.crazypoker.*;
import com.allinpokers.yunyingjob.entity.crazypoker.example.AutoCreateUserTaskExample;
import com.allinpokers.yunyingjob.rabbitmq.send.BusinessMessageSender;
import com.allinpokers.yunyingjob.rabbitmq.send.bean.PromotionChannelsCount;
import com.allinpokers.yunyingjob.rabbitmq.send.config.PromotionChannelsCountAction;
import com.allinpokers.yunyingjob.rabbitmq.send.util.CountDayUtils;
import com.allinpokers.yunyingjob.service.UserDetailsInfoService;
import com.allinpokers.yunyingjob.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

@Service
@Slf4j
public class UserServiceImpl implements UserService {

    @Resource
    private AutoCreateUserLogDao autoCreateUserLogDao;
    @Resource
    private AutoCreateUserTaskDao autoCreateUserTaskDao;
    @Resource
    private UserDetailsInfoService userDetailsInfoService;
    @Resource
    private UserVIPDao userVIPDao;
    @Autowired
    @Lazy
    private UserServiceImpl self;
    @Resource
    private UserAccountDao userAccountDao;
    @Resource
    private UserBasicInfoDao userBasicInfoDao;
    @Resource
    private AutoUserServiceImpl autoUserService;

    @Resource
    BusinessMessageSender businessMessageSender;


    private ReentrantLock lock = new ReentrantLock();

    private static final int RANDOM_SEX_MALE_RATIO = 70;

    @Override
    public void autoCreateUserJob() {

        List<AutoCreateUserTask> taskList = this.findAutoCreateUserTask();
        if (taskList.isEmpty()) {
            return;
        }
        for (Integer i = 0; i < taskList.size(); i++) {
            //更新状尝试去锁定状态，如果成功就去处理任务
            Integer num = autoCreateUserTaskDao.updateTaskStatus(taskList.get(i).getId());
            log.info("一键生成用户任务  {}  锁定是否成功{}", JsonUtils.write(taskList.get(i)), num == 1);
            if (num == 1) {
                taskList.get(i).setStatus(1);
                batchAutoUser(taskList.get(i));
            }
        }
    }

    @Override
    public void initAutoCreateUserJob() {
        //把处理中的任务修改称为等待处理的状态
        AutoCreateUserTaskExample example = new AutoCreateUserTaskExample();
        example.or().andStatusEqualTo(1).andActualNumberGreaterThanOrEqualTo(0);
        List<AutoCreateUserTask> userTasks = autoCreateUserTaskDao.selectByExample(example);
        for (int i = 0; i < userTasks.size(); i++) {
            userTasks.get(i).setStatus(0);
            autoCreateUserTaskDao.updateByPrimaryKey(userTasks.get(i));
        }
        log.info("init auto create user job ok task size={}", userTasks.size());
    }

    /**
     * 等待处理的任务
     *
     * @return
     */
    private List<AutoCreateUserTask> findAutoCreateUserTask() {
        AutoCreateUserTaskExample example = new AutoCreateUserTaskExample();
        example.or().andStatusEqualTo(0);
        List<AutoCreateUserTask> userTasks = autoCreateUserTaskDao.selectByExample(example);
        return userTasks;
    }

    /**
     * 处理单个自动任务
     *
     * @param task
     */
    private void batchAutoUser(AutoCreateUserTask task) {

        List<UserDetailsInfo> userDetailsInfoList = userDetailsInfoService.findAllUser();
        List<String> dbUserPhoneList = new ArrayList<>();
        List<String> dbUserNickNameList = new ArrayList<>();

        //获取数据库里面现有用户的手机号码和昵称
        userDetailsInfoList.stream().forEach(e -> {
            String tempPhone = PhoneNumUtil.decoder(e.getPhone());

            if (!StringUtils.isEmpty(tempPhone)) {
                dbUserPhoneList.add(tempPhone);
            }
            if (!StringUtils.isEmpty(e.getNikeName())) {
                dbUserNickNameList.add(e.getNikeName());
            }
        });

        AtomicInteger actualNumber = new AtomicInteger(task.getActualNumber());
        try {
            //生成的等待插入数据库的随机用户
            List<AutoCreateUserLog> autoCreateUserLogList = Collections.synchronizedList(this.createUserInfo(task, dbUserPhoneList, dbUserNickNameList));
            log.info("生成了 datasize={}", autoCreateUserLogList.size());
            String password = DigestUtils.md5Hex(URLDecoder.decode(task.getPassword(), "UTF-8")).toLowerCase();
            self.asyncTask(actualNumber, autoCreateUserLogList, password, task);
//            for (int i = 0; i < autoCreateUserLogList.size(); i++) {
//                try {
//                    self.insertUserToDb(autoCreateUserLogList.get(i), password, actualNumber, task);
//                } catch (Exception e) {
//                    log.error("传入用户异常", e);
//                }
//            }
        } catch (Exception e) {
            log.error("处理创建任务 异常", e);
        } finally {
            //更新任务的状态和实际生成的数量
            task.setActualNumber(actualNumber.intValue());
            task.setFinishTime(LocalDateTime.now());
            task.setStatus(2);
            //如果只是生成0个用户就任务状态 标记失败 3
            if (actualNumber.intValue() == 0) {
                task.setStatus(3);
            }
            autoCreateUserTaskDao.updateByPrimaryKeySelective(task);
            log.info("一键生成用户任务处理完 任务id={} 任务是否成功{}", task.getId(), actualNumber.intValue() != 0);
        }
    }


    @Override
    public void asyncTask(AtomicInteger actualNumber, List<AutoCreateUserLog> autoCreateUserLogList, String password, AutoCreateUserTask task) {

        try {
            Iterator<AutoCreateUserLog> iterator = autoCreateUserLogList.iterator();
            while (iterator.hasNext()) {

                AutoCreateUserLog temp = iterator.next();
                iterator.remove();
                if (temp != null) {
                    self.insertUserToDb(temp, password, actualNumber, task);
                }
            }
            log.info("sdafsaf   ",actualNumber.get());
        } catch (Exception e) {
            log.error("传入用户异常", e);
        }


    }

    /**
     * 插入数据库里面 user_details_info user_account user_basic_info
     * *********** 1000031  100
     *
     * @param autoCreateUserLog
     * @param password
     */
    @Override
//    @Async("taskExecutor")
    @Transactional(transactionManager = "crazyPokerTransactionManager", rollbackFor = Exception.class)
    public void insertUserToDb(AutoCreateUserLog autoCreateUserLog, String password, AtomicInteger actualNumber, AutoCreateUserTask task) {

        String userNickName = autoCreateUserLog.getNickName();
        String userPhone = PhoneNumUtil.encoder(autoCreateUserLog.getUserPhone());
        LocalDateTime now = LocalDateTime.now();


        //1.插入用户基础表 user_basic_info
        UserBasicInfo userBasicInfoPo = UserBasicInfo.builder().imei("************").activeTime(now).regTime(now)
                .type(9).channel(0).active(1).build();
        userBasicInfoDao.insertSelective(userBasicInfoPo);

        //1.1 获取用户的信息
        Integer userId = userBasicInfoPo.getUserId();
        log.info(" insert into com.dzpk.processor.db userId={}  now={}",userId,now.toString());
        String head = genRandomHead();
        String randomNum = genRandomNum(userId);
        int sex = genRandomSex(RANDOM_SEX_MALE_RATIO);


        //2.插入用户表 user_details_info
        UserDetailsInfo userDetailsInfo = UserDetailsInfo.builder().nikeName(randomNum).phone(userPhone).password(password)
                .roomPersionType(1).ctime(now).head(head).sex(sex).randomNum(randomNum).userId(userId).build();
        userDetailsInfoService.addUserDetailInfo(userDetailsInfo);

        //3.插入用户钱包表 user_account
        UserAccount userAccountBo = UserAccount.builder().userId(userId).createdTime(now).updatedTime(now).build();
        userAccountDao.insertSelective(userAccountBo);

        // 生成默认层级
        userAccountDao.insertUserDefaultRoomTier(userId);


        //4.插入自动生成记录表 auto_create_user_log
        autoCreateUserLog.setRandomId(randomNum);
        autoCreateUserLog.setNickName(randomNum);
        autoCreateUserLog.setCreateTime(now);

        autoCreateUserLogDao.insertSelective(autoCreateUserLog);

        //5.修改任务的实际处理的数量 成功插入就+1
        autoCreateUserTaskDao.addAutoCreateTaskActualNum(task.getId());
         actualNumber.incrementAndGet();
//        LocalDate now1=LocalDate.now();
//        ZoneId zone = ZoneId.systemDefault();
//        Instant endDate = now1.plus(10, ChronoUnit.YEARS)
//                .atStartOfDay().atZone(zone).toInstant();

        userVIPDao.insterUserVip(userId,0, 2,1,95000,
                getDateWithZeroTime(new Date(),3650));

        // 发送MQ统计注册
        log.info("插入用户信息成功 userId={}  userPhone={}  userNickName={}  actualNumber={}", userId, userPhone, userNickName, actualNumber.get());

        // 插入之后，则发送MQ统计注册
        businessMessageSender.sendPromotionChannelsCountMessage(PromotionChannelsCount.builder()
                .userId(userId)
                .action(PromotionChannelsCountAction.REGISTER)
                .countDay(CountDayUtils.nowCountDay())
                .build());
    }
    /**
     * 清零Time
     * 并且增加或减少X天
     *
     * @param refDate 参考日期，可选，默认以当前系统时间
     * @param days    增加或减少X天
     * @return
     */
    public static Date getDateWithZeroTime(Date refDate, int days) {
        Calendar calendar = Calendar.getInstance();
        if (null != refDate) {
            calendar.setTime(refDate);
        }
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        if (days != 0) {
            calendar.add(Calendar.DAY_OF_MONTH, days);
        }

        return calendar.getTime();
    }

    private static String genRandomHead() {
        return String.valueOf((int) (Math.random() * 56 + 1));
    }

    private static String genRandomNum(Integer id) {
        String idStr = String.format("%07d", id);
        StringBuffer randomNum = new StringBuffer(idStr.length() + 3);

        Random random = new Random(System.currentTimeMillis());

        randomNum.append(idStr, 0, idStr.length() - 4)
                .append(random.nextInt(90) + 10)
                .append(idStr, idStr.length() - 4, idStr.length() - 2)
                .append(random.nextInt(10))
                .append(idStr, idStr.length() - 2, idStr.length());

        return randomNum.toString();

    }

    private static int genRandomSex(int maleRatio) {
        return Math.random() * 100 < maleRatio ? 0 : 1;
    }

    /**
     * 生成等待插入的用户
     *
     * @param task               任务
     * @param dbUserPhoneList    数据库存在的用户手机号码列表
     * @param dbUserNickNameList 数据库存在的用户昵称列表
     * @return
     */
    public List<AutoCreateUserLog> createUserInfo(AutoCreateUserTask task, List<String> dbUserPhoneList, List<String> dbUserNickNameList) {
        LinkedList<AutoCreateUserLog> autoCreateUserLogList = new LinkedList<>();
        List<String> userNickNameList = new ArrayList<>();
        List<String> userPhoneList = new ArrayList<>();
        Integer nickLock = 0;
        Integer phoneLock = 0;
        String password = task.getPassword();
        Integer num = task.getTargetNumber() - task.getActualNumber();
        log.info("生成等待插入的用户 num={}  getTargetNumber={}  getActualNumber={}", num, task.getTargetNumber(), task.getActualNumber());
        Integer taskId = task.getId();
        //1.生成用户随机信息
        while (userNickNameList.size() < num || userPhoneList.size() < num) {
            if (nickLock == 0) {
                String tempNickName = autoUserService.createUserNickName();
                if (!dbUserNickNameList.contains(tempNickName) && !userNickNameList.contains(tempNickName)) {
                    userNickNameList.add(tempNickName);
                }
                if (userNickNameList.size() == num) {
                    nickLock = 1;
                }
            }

            if (phoneLock == 0) {
                String tempPhone = autoUserService.createUserPhone();
                if (!dbUserPhoneList.contains(tempPhone) && !userPhoneList.contains(tempPhone)) {
                    userPhoneList.add(tempPhone);
                }
                if (userPhoneList.size() == num) {
                    phoneLock = 1;
                }
            }
        }

        //2.自动创建用户的列表
        for (int i = 0; i < userPhoneList.size(); i++) {
            AutoCreateUserLog item = AutoCreateUserLog.builder().autoTaskId(taskId).userPhone(userPhoneList.get(i)).nickName(userNickNameList.get(i)).password(password).build();
            autoCreateUserLogList.add(item);
        }
        return autoCreateUserLogList;
    }


}
