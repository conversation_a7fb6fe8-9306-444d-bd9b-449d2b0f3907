package com.allinpokers.yunyingjob.dao.crazypoker;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunyingjob.entity.crazypoker.UserAccount;
import com.allinpokers.yunyingjob.entity.crazypoker.example.UserAccountExample;
import com.allinpokers.yunyingjob.service.model.UserAccountSum;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户账户表  Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserAccountDao extends BaseDao<UserAccount, UserAccountExample, Integer> {

    /**
     * 统计用户仓
     *
     * @return
     */
    UserAccountSum sumExtractChipAndNotExtractChip();


    @Insert("INSERT INTO user_room_tier (user_id, tribe_id, tier_id) VALUES (#{userId}, 0, 1)")
    void insertUserDefaultRoomTier(@Param("userId") Integer userId);

}