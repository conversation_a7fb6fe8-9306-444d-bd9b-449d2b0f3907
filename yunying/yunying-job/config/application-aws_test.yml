spring:
  data:
    mongodb:
          uri: ***************************************************
          database: crazy_poker
      datasource:
        crazy-poker:
          # &useLegacyDatetimeCode=false&serverTimezone=Asia/Shanghai 设置时区
          jdbcUrl: ***************************************************************************************************************************************************************************
          username: work
          password: SqL0301myT2016est
        yunying:
          jdbcUrl: ***********************************************************************************************************************************************************************
          username: work
          password: SqL0301myT2016est
      redis:
        host: ************
        password: YUjV9YbA%qwuJJT
        port: 5733
      #rabbit mq设置
      rabbitmq:
        host: ************
        port: 5670
        username: work
        password: YUjV9YbAqwuJJT
        #发布确认
        publisher-confirms: true
        #退回确认
        publisher-returns: true
        virtual-host: /
        cache:
          channel:
            size: 100



    config:
      dirPath: config/data
      imageServiceUrl: http://************:9663/api/upload/head
      nickNameFile: nickname.txt
      picSdkUrl: https://api.uomg.com/api/rand.avatar



logging:
  file: /mnt/logs/yunying-job/yunying-job.log

#阿里云oss 配置
aliyun:
  oss:
    endpoint: oss-cn-shenzhen.aliyuncs.com
    access-key-id: LTAI5t6UYvAeqQZb6Zb3LRdw
    access-key-secret: ******************************
    bucket-name: qqpoker-export

export:
  expiredConfig: 30

# cms 请求
cms:
  base: https://vuebackend-dev.qqpoker.fun
  api:
    updateGameRecord: /api/updateGameRecord/sync
