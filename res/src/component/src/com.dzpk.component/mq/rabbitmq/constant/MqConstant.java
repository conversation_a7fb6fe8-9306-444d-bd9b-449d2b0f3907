package com.dzpk.component.mq.rabbitmq.constant;

public class MqConstant {

    public static String EXCHANGE_TYPE_DIRECT = "direct";  //直连方式
    public static String EXCHANGE_TYPE_TOPIC = "topic";  //主题方式

    /**
     * 内部消息mq相关配置
     */
    public static String MQ_INTERIOR_QUEUE = "crazypoker.queue.interior";
    public static String MQ_INTQUENE_EXCHANGE = "crazypoker.topic.exchange.interior";
    public static String MQ_INTQUENE_ROUTEKEY = "crazypoker.routing.interior";

    /**
     * 房间健康检查异常时发送
     */
    public static String MQ_GAME_HEARTBEAT_QUEUE = "crazypoker.queue.game.heartbeat";
    public static String MQ_GAME_HEARTBEAT_EXCHANGE = "crazypoker.topic.exchange.game.heartbeat";
    public static String MQ_GAME_HEARTBEAT_ROUTEKEY = "crazypoker.routing.game.heartbeat";
    /**
     * 房间关闭发送相关数据
     */
    public static String MQ_GAME_CLOSE_QUEUE = "crazypoker.queue.game.close";
    public static String MQ_GAME_CLOSE_EXCHANGE = "crazypoker.topic.exchange.game.close";
    public static String MQ_GAME_CLOSE_ROUTEKEY = "crazypoker.routing.game.close";
    /**
     * 房间请求带入
     */
    public static String MQ_GAME_REQUEST_BRING_QUEUE = "crazypoker.queue.request.bring ";
    public static String MQ_GAME_REQUEST_BRING_EXCHANGE = "crazypoker.topic.exchange.request.bring";
    public static String MQ_GAME_REQUEST_BRING_ROUTEKEY = "crazypoker.routing.request.bring";
    /**
     * 俱乐部消息mq相关配置
     */
    public static String MQ_CLUB_QUEUE = "crazypoker.queue.club";
    public static String MQ_CLUB_EXCHANGE = "crazypoker.topic.exchange.club";
    public static String MQ_CLUB_ROUTEKEY = "crazypoker.routing.club";


    /**
     * 计算返佣金豆相关
     */
    public static String REBATE_CACULATECHIP_QUENE_NAME = "crazypoker.queue.rebate.caculatechip";
    public static String REBATE_CACULATECHIP_EXCHANGE = "crazypoker.direct.exchange.rebate";
    public static String REBATE_CACULATECHIP_ROUTEKEY = "crazypoker.route.rebate.caculatechip";

    /**
     * 埋点相关
     */
    public static String MQ_EVENTTRACK_QUENE_NAME = "crazypoker.queue.eventtrack";
    public static String MQ_EVENTTRACK_EXCHANGE = "crazypoker.direct.exchange.eventtrack";
    public static String MQ_EVENTTRACK_ROUTEKEY = "crazypoker.route.eventtrack";

    // 审计操作
    public static final String AUDIT_OPERATION_QUENE_NAME = "crazypoker.queue.audit.operation";
    public static final String AUDIT_OPERATION_EXCHANGE = "crazypoker.direct.exchange.audit.operation";
    public static final String AUDIT_OPERATION_ROUTEKEY = "crazypoker.route.audit.operation";
 
    /**
     * aws sns
     */
    public static String MQ_SNS_QUEUE = "crazypoker.queue.sns";
    public static String MQ_SNS_EXCHANGE = "crazypoker.topic.exchange.sns";
    public static String MQ_SNS_ROUTEKEY = "crazypoker.routing.sns";

    /**
     * user balance sync
     */
    public static String USER_BALANCE_SYNC_EXCHANGE = "crazypoker.topic.exchange.user_balance.sync";
    public static String USER_BALANCE_SYNC_ROUTEKEY = "crazypoker.routing.user_balance.sync";

    /**
     * system config update
     */
    public static String SYSTEM_CONFIG_UPDATE_EXCHANGE = "crazypoker.topic.exchange.system_config.update";
    public static String SYSTEM_CONFIG_UPDATE_ROUTEKEY = "crazypoker.routing.system_config.update";

    public static final String EXCHANGE_BROADCAST_SEND = "poker.topic.exchange.sys.broadcast.send";
    public static final String EXCHANGE_BROADCAST_STATUS = "poker.topic.exchange.sys.broadcast.status";
    public static final String ROUTING_KEY_BROADCAST_SEND = "poker.routing.sys.broadcast.send";
    public static final String ROUTING_KEY_BROADCAST_STATUS = "poker.routing.sys.broadcast.status";

    /**
     * user login record
     */
    public static final String EXCHANGE_USER_LOGIN_LOG = "poker.topic.exchange.user.login.log";
    public static final String QUEUE_USER_LOGIN_LOG = "poker.queue.user.login.log";
    public static final String ROUTING_KEY_USER_LOGIN_LOG = "poker.routing.user.login.log";

    /**
     * 联盟成员交易统计
     */
    public static final String EXCHANGE_PROMOTION_CHANNELS_COUNT = "poker.topic.exchange.promotion.channels.count";
    public static final String QUEUE_PROMOTION_CHANNELS_COUNT = "poker.queue.promotion.channels.count";
    public static final String ROUTING_KEY_PROMOTION_CHANNELS_COUNT = "poker.routing.promotion.channels.count";

}
