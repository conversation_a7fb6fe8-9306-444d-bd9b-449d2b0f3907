package com.i366.main;

import com.dzpk.component.mq.rabbitmq.RabbitMQService;
import com.i366.broadcast.SysBroadcastListener;
import com.i366.data.Data;
import com.i366.listener.SystemConfigUpdateListener;
import com.i366.listener.UserBalanceSyncListener;
import com.work.comm.endpoints.*;
import com.work.comm.endpoints.res.ResServerManager;
import com.work.comm.s2s.client.ServerManager;
import com.work.comm.s2s.server.boostrap.S2Server;
import com.work.engine.service.Service;


/**
 * Created by IntelliJ IDEA.
 * User: Administrator
 * To change this template use File | Settings | File Templates.
 * 
 */
public class platformService {
	
    public static void main(String[] args) {
        Service service = new Service();

        // 初始化内部访问的服务管理器
        ServerManager.initialize(Data.PRO);
        // CoreServerManager.initialize();
        LoginServerManager.initialize();
        RoomServerManager.initialize();
        OmahaServerManager.initialize();
        AofRoomServerManager.initialize();
        AofOmahaServerManager.initialize();
        MttServerManager.initialize();

        // 启动内部访问服务
        S2Server s2Server = new S2Server(Data.PRO);
        s2Server.run();

        // 注册节点到zk
        ResServerManager.getInstance().initialize(ServerManager.getInstance().getAppConfig());

        // 初始化RabbitMQ服务
        RabbitMQService rabbitMQService = RabbitMQService.getInstance();
        new UserBalanceSyncListener(rabbitMQService).listen();
        new SystemConfigUpdateListener(rabbitMQService).listen();
        // 创建广播监听
        new SysBroadcastListener(rabbitMQService).listen();

        // 设置钩子
        Runtime.getRuntime().addShutdownHook(new Thread(){
            @Override
            public void run(){
                rabbitMQService.destroy();
                ResServerManager.getInstance().destroy();
                ServerManager.getInstance().destroy();
                s2Server.shutdown();
            }
        });

        // 启动外部监听端口
        service.start(Service.DZPK_SERVICE, Integer.valueOf(Data.PRO.getProperty("socket.port")), 
                Integer.valueOf(Data.PRO.getProperty("service.socket.port")));
        
        // DomainManageService.init();

//    	Service s1 = new Service();
//    	Service s2 = new Service();
//    	//开启外部通讯服务器
//    	s1.start(Service.ZEPK_CLIENT_SERVICE, Integer.valueOf(Data.PRO.getProperty("socket.port")));
//    	//开启内部通讯服务器
//    	s2.start(Service.ZEPK_SERVICE_SERVICE, Integer.valueOf(Data.PRO.getProperty("service.socket.port")));

//		//  消息队列通知线程
//		MsgListenThread msg = new MsgListenThread();
//		Thread msgThread = new Thread(msg, "MsgListenThread");
//		msgThread.start();
    }
}
