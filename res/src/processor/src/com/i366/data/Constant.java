/**
 * $RCSfile: Constant.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-13  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.i366.data;
/**
 * <p>Title: Constant</p> 
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2016</p> 
 * <AUTHOR>
 * @version 1.0
 */
public interface Constant {

	public static final String IP_LIMIT_TAKE_EFFICACY = "0";//ip限制生效
	public static final String IP_LIMIT_LOSE_EFFICACY = "1";//ip限制失效
	
	public static final int OLD_REQ_CHECK = 2;//2协议号

	public static final int REQ_CHECK = 8;//原本2的协议号 对应当前8的协议号

	public static final int REQ_9_DOMIAN = 9;               // 请求当前的紧急域名

	public static final int REQ_USER_BALANCE_SYNC = 10; // 用户余额同步

	public static final int REQ_SYSTEM_CONFIG_UPDATE = 11; // 系统配置更新

	public static final int REQ_55_EXIT = 55;			//注销

	public static final int REQ_97_SNGROOMSETTING = 97;//sng房间设置

	public static final int REQ_180_MTT_APPLY = 180; 	//	MTT 报名

	public static final int RESP_186_MTT_REBUY = 186;		//牌局外重购请求应答

	public static final int REQ_196_MTT_PLAYER_LIST = 196;  // MTT玩家报名列表

	public static final int REQ_274_SNG_APPLY = 274;   // sng报名

	public static final int REQ_295_RES_BATCH_CONTROL = 295;     // 批量处理请求

	public static final int REQ_293_REQUEST_BATCH = 293;		   // 批量请求

	public static final int REQ_294_REQUEST_EXPIRE = 294;		   // 消息过期通知

	public static final int REQ_296_MTT_CONTROL = 296;		   // mtt重购审批

	public static final int REQ_166_MTT_ONE_MIN = 166; 	//	MTT 1min notify

	public static final int REQ_222_MTT_GAME_CANCELLED = 222;	// 解散MTT比赛通知

	public static final int REQ_131_SNG_OWNER_LIST = 131;	//	SNG房主报名列表

	public static final int REQ_133_SNG_QUEST_READ = 133;	//	SNG报名消息已阅通知

	public static final int REQ_134_SNG_QUEST_PROCESS = 134;	//	SNG报名消息处理

	public static final int REQ_136_SNG_PLAYER_LIST = 136;		//	SNG玩家报名列表
	
	public static final int REQ_292_ADDCHIP_NOTICE = 292;  // 控制带入

    public static final int REQ_MTT_PLAYERS_LIST = 188; //牌局外玩家tab

	public static final int REQ_MTT_NEW_APPLY = 186;// 新的玩家申请通知
	public static final int REQ_MTT_APPLY_NOTICE = 195; // MTT玩家报名结果通知
	public static final int REQ_MTT_PLAYER_REMOVED = 554;	// 移除玩家通知
	public static final int REQ_MSG_COUNT = 205;	//	获取消息数量
	public static final int REQ_MSG_LIST = 206;		//	获取消息列表
	public static final int REQ_MSG_READ = 207;		//	消息已读
	public static final int REQ_MSG_SEND = 208;		//	单个消息推送
	public static final int REQ_SNG_OWNER_MSG = 130;	//	SNG房主报名通知
	public static final int REQ_SNG_EXPIRE_MSG = 132;	//	SNG房主报名失效通知
	public static final int REQ_SNG_PLAYER_MSG = 135;		//	SNG玩家报名结果通知

	public static final int REQ_MTT_REBUY = 171;               // 比赛重购(R)请求

	public static final int REQ_SNG_NOTICE = 281;  // sng通知
	public static final int REQ_RES_BROADCAST  = 266;     // 跑马灯广播
	public static final int REQ_RES_GAME_BROADCAST  = 268;  //游戏服跑马灯广播
    public static final int REQ_JP_BROADCAST = 718;       // jackpot广播回包718


    public static final int MTT_APPLY_TIMEOUT  = 10001;     //MTT报名3分钟超时


	public static final int REQUEST_MIN_TIMEOUT = 160 * 1000;	// 单个请求160s认为失效
	public static final int REQUEST_MAX_TIMEOUT = 180 * 1000;	// 单个请求180s直接删除

	public static final double MIN_GPS_DISTANCE = 600.0;      // 最小的GPS距离限制为600m

    public static final int MTT_ROOM_TYPE = 2;  //MTT比赛类型
    public static final int MTT_APPLY = 0;      //MTT 报名
    public static final int MTT_REBUY = 1;      //MTT 重购
    public static final int MTT_ADDBUY = 2;     //MTT 增购

    public static final int MTT_OPERATE_APPLY = 4;          //  mtt报名

    public static final int MSG_TYPE_AGREE = 0;             //  mtt审批消息 同意
    public static final int MSG_TYPE_REFUSED = 1;           //  mtt审批消息 拒绝
    public static final int REQ_TYPE_UNREAD = -1;           //  mtt审批消息 未读
	public static final int MSG_TYPE_REMOVED_PLAYER = 7;	//  mtt审批消息 移除玩家

    public static final int REQUEST_MTT_MAX_TIMEOUT = 3 * 60 * 1000;	// mtt重购请求时间
    public static final int REQUEST_MTT_APPLY_TIMEOUT = 3 * 60 * 1000;	// mtt报名超时时间

    public static final int SNG_ROOM_TYPE = 4;				// sng 比赛类型
	public static final int SNG_APPLY = 0;            // sng 报名
	public static final int OMGRU_AUDIMSG = 297;			// 其他管理员的审批消息

    public static final int PAGE_SIZE = 50;  //玩家tab页每页显示数量

}