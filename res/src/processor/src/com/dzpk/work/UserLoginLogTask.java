package com.dzpk.work;


import com.dzpk.component.mq.rabbitmq.RabbitMQService;
import com.dzpk.component.mq.rabbitmq.constant.MqConstant;
import com.dzpk.work.util.CountDayUtils;
import com.dzpk.work.util.PromotionChannelsCountAction;
import com.i366.data.Data;
import com.i366.model.UserLoginLog;
import com.i366.util.EnToCnUtils;
import com.i366.util.GpsToRegionUtils;
import com.i366.util.IpToRegionUtils;
import com.i366.util.MapBuilder;
import com.work.comm.util.GsonUtil;
import com.work.db.imp.DZPKDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;

/**
 * UserLoginLogTask
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@Slf4j
public class UserLoginLogTask implements Runnable{


    private final UserLoginLog userLoginLog;

    public UserLoginLogTask(UserLoginLog userLoginLog) {
        this.userLoginLog = userLoginLog;
    }

    @Override
    public void run() {
        try {
            log.info("UserLoginLogTask:run: {}", GsonUtil.getInstance().toJson(userLoginLog));
            // 如果为空则不处理
            if (userLoginLog == null) {
                log.info("UserLoginLogTask:为空不处理");
                return;
            }
            DZPKDao dao = new DZPKDao();
            // IP
            log.info("UserLoginLogTask:IP前: {}", userLoginLog.getIp());
            if (StringUtils.isNotBlank(userLoginLog.getIp())) {
                log.info("UserLoginLogTask:处理IP");
                try {
                    userLoginLog.setIpAddress(IpToRegionUtils.getRegion(userLoginLog.getIp()));
                    log.info("获取IP位置成功");
                } catch (Exception e) {
                    log.error("获取IP位置失败", e);
                }
            }
            // GPS
            if (StringUtils.isNotBlank(userLoginLog.getGps())) {
                if (StringUtils.isBlank(userLoginLog.getGpsAddress()) || userLoginLog.getGpsAddress().contains("用户未授权GPS")) {
                    // gps位置 为空
                    // 调用api查询
                    String[] gps = userLoginLog.getGps().split(",");
                    if (gps.length == 2) {
                        // 0 经度 1 纬度
                        // 经度
                        String longitude = gps[0];
                        // 纬度
                        String latitude = gps[1];
                        try {
                            log.info("UserLoginLogTask:处理GPS");
                            userLoginLog.setGpsAddress(GpsToRegionUtils.getRegion(longitude, latitude));
                        } catch (Exception e) {
                            log.error("获取GPS位置失败", e);
                        }
                    } else {
                        log.error("经纬度错误：{}", userLoginLog.getGps());
                    }
                } else {
                    log.info("gps位置不为空");
                    // 英文转简中
                    userLoginLog.setGpsAddress(EnToCnUtils.getChangeRegion(userLoginLog.getGpsAddress()));
                }
            }
            log.info("UserLoginLogTask:IP: {}", GsonUtil.getInstance().toJson(userLoginLog));
            Object[] param = new Object[] {
                    userLoginLog.getUserId(),
                    userLoginLog.getLoginTime(),
                    userLoginLog.getIp(),
                    userLoginLog.getNikeName(),
                    userLoginLog.getRandomNum(),
                    userLoginLog.getGps(),
                    userLoginLog.getIpAddress(),
                    userLoginLog.getAppVersion(),
                    userLoginLog.getClientLang(),
                    userLoginLog.getOsLang(),
                    userLoginLog.getOsVersion(),
                    userLoginLog.getDeviceModel(),
                    userLoginLog.getDeviceId(),
                    userLoginLog.getToken(),
                    userLoginLog.getGpsAddress()
            };
            // 保存登录记录
            log.info("保存登录记录：{}", GsonUtil.getInstance().toJson(param));
            dao.operation(Data.SQL_MAP.get("add_login_records"), param);
            log.info("保存登录记录成功");
            try {
                sendPromotionChannelsCountMessage(userLoginLog.getUserId());
                sendMqUserLoginLog(userLoginLog.getUserId(), userLoginLog.getIp(), userLoginLog.getDeviceId());
            } catch (Exception e) {
                log.error("发送登录记录mq错误", e);
            }
        } catch (Exception e) {
            log.error("保存登录记录错误", e);
        }
    }

    private void sendMqUserLoginLog(Integer userId, String ip, String deviceId) {
        RabbitMQService rabbitMQProducerService = RabbitMQService.getInstance();
        // 记录登录后发送mq记录用户登录的IP,设备码
        rabbitMQProducerService.sendMessage(
                MqConstant.EXCHANGE_USER_LOGIN_LOG,
                MqConstant.ROUTING_KEY_USER_LOGIN_LOG,
                GsonUtil.getInstance().toJson(MapBuilder.builder()
                        .put("ip", ip)
                        .put("deviceId", deviceId)
                        .put("userId", userId)
                        .build()));
    }

    /**
     * 发送推广渠道统计消息
     * @param userId 用户ID
     */
    private void sendPromotionChannelsCountMessage(Integer userId) {
        RabbitMQService rabbitMQProducerService = RabbitMQService.getInstance();
        // 记录登录后发送mq记录用户登录的IP,设备码
        rabbitMQProducerService.sendMessageJson(
                MqConstant.EXCHANGE_PROMOTION_CHANNELS_COUNT,
                MqConstant.ROUTING_KEY_PROMOTION_CHANNELS_COUNT,
                GsonUtil.getInstance().toJson(MapBuilder.builder()
                        .put("action", PromotionChannelsCountAction.LOGIN)
                        .put("userId", userId)
                        .put("countDay", CountDayUtils.nowCountDay())
                        .build()));
    }
}
