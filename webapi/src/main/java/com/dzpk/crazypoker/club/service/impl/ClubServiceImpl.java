package com.dzpk.crazypoker.club.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dzpk.crazypoker.appmessage.bean.UserRoomTierBean;
import com.dzpk.crazypoker.appmessage.dao.AppBusinessMessageDao;
import com.dzpk.crazypoker.appmessage.send.AppBusinessMessageSender;
import com.dzpk.crazypoker.appmessage.send.BusinessMessageSender;
import com.dzpk.crazypoker.appmessage.send.bean.*;
import com.dzpk.crazypoker.appmessage.utils.AppMessageConstants;
import com.dzpk.crazypoker.appmessage.utils.MapValueUtils;
import com.dzpk.crazypoker.club.api.req.ClubChipReq;
import com.dzpk.crazypoker.club.api.req.ClubGoldLogReq;
import com.dzpk.crazypoker.club.api.vo.ClubChipLog;
import com.dzpk.crazypoker.club.api.vo.ClubChipTransferLog;
import com.dzpk.crazypoker.club.api.vo.ClubChipTransferRecipient;
import com.dzpk.crazypoker.club.api.vo.ClubGoldLog;
import com.dzpk.crazypoker.club.config.ClubProperties;
import com.dzpk.crazypoker.club.constant.EClubFundHistoryCode;
import com.dzpk.crazypoker.club.constant.EClubIdentityCode;
import com.dzpk.crazypoker.club.constant.EClubRequestMessageCode;
import com.dzpk.crazypoker.club.msgservice.IMsgService;
import com.dzpk.crazypoker.club.msgservice.bean.ClubMsgBo;
import com.dzpk.crazypoker.club.repositories.mysql.*;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.mapper.ClubContactInfoPoMapper;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.mapper.ClubMemberPoMapper;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.mapper.ClubRecordPoMapper;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.model.*;
import com.dzpk.crazypoker.club.repositories.mysql.model.ClubCreateRequestPo;
import com.dzpk.crazypoker.club.repositories.mysql.model.*;
import com.dzpk.crazypoker.club.service.IClubService;
import com.dzpk.crazypoker.club.service.bean.*;
import com.dzpk.crazypoker.club.service.transaction.IClubTransation;
import com.dzpk.crazypoker.common.constant.AuditOperationCode;
import com.dzpk.crazypoker.common.constant.RespCode;
import com.dzpk.crazypoker.common.dozer.BeanUtil;
import com.dzpk.crazypoker.common.rabbitmq.client.MessageSender;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.MoneyMessage;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.OsmMessage;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.osm.ClubCreateApplyParams;
import com.dzpk.crazypoker.common.rabbitmq.client.bean.userbalance.UserBalanceSyncMessage;
import com.dzpk.crazypoker.common.rabbitmq.constant.EMessageChannelCode;
import com.dzpk.crazypoker.common.rabbitmq.constant.EMessageCode;
import com.dzpk.crazypoker.common.rabbitmq.constant.EMessageStatusCode;
import com.dzpk.crazypoker.common.rabbitmq.constant.EOsmMessageCode;
import com.dzpk.crazypoker.common.redis.lock.*;
import com.dzpk.crazypoker.common.service.InvokedResult;
import com.dzpk.crazypoker.common.service.exception.ServiceException;
import com.dzpk.crazypoker.common.utils.JsonUtil;
import com.dzpk.crazypoker.oss.service.OssService;
import com.dzpk.crazypoker.permission.IPermissionService;
import com.dzpk.crazypoker.promotion.service.IPromotionService;
import com.dzpk.crazypoker.room.repositories.mysql.IRoomSearchDao;
import com.dzpk.crazypoker.room.repositories.mysql.model.RoomSearchRemainTimeDo;
import com.dzpk.crazypoker.tribe.repositories.mysql.ITribeDao;
import com.dzpk.crazypoker.tribe.repositories.mysql.autogen.mapper.TribeMemberPoMapper;
import com.dzpk.crazypoker.tribe.repositories.mysql.autogen.mapper.TribeRecordPoMapper;
import com.dzpk.crazypoker.tribe.repositories.mysql.autogen.model.TribeMemberPo;
import com.dzpk.crazypoker.tribe.repositories.mysql.autogen.model.TribeMemberPoExample;
import com.dzpk.crazypoker.tribe.repositories.mysql.autogen.model.TribeRecordPo;
import com.dzpk.crazypoker.tribe.repositories.mysql.model.TribeJoinOrCreatedPo;
import com.dzpk.crazypoker.tribe.repositories.mysql.model.TribeStatusPo;
import com.dzpk.crazypoker.tribe.service.ITribeService;
import com.dzpk.crazypoker.user.repositories.model.UserBalanceAuditLog;
import com.dzpk.crazypoker.user.repositories.mysql.IUserBalanceAuditDao;
import com.dzpk.crazypoker.user.repositories.mysql.autogen.mapper.ClubBlacklistMapper;
import com.dzpk.crazypoker.user.repositories.mysql.autogen.mapper.UserLoginLogMapper;
import com.dzpk.crazypoker.user.repositories.mysql.autogen.model.ClubBlacklist;
import com.dzpk.crazypoker.user.repositories.mysql.autogen.model.ClubBlacklistExample;
import com.dzpk.crazypoker.user.repositories.mysql.autogen.model.UserLoginLog;
import com.dzpk.crazypoker.user.service.IUserService;
import com.dzpk.crazypoker.user.service.bo.UserDetailsInfoBo;
import com.dzpk.crazypoker.vip.service.IUserVipService;
import com.dzpk.crazypoker.vip.service.bean.UserVipBaseBo;
import com.dzpk.crazypoker.wallet.repositories.mysql.IUserGoldAccountDao;
import com.dzpk.crazypoker.wallet.repositories.mysql.autogen.mapper.UserAccountPoMapper;
import com.dzpk.crazypoker.wallet.repositories.mysql.autogen.model.UserAccountPo;
import com.dzpk.crazypoker.wallet.api.constant.EChipConsumeFunction;
import com.dzpk.crazypoker.wallet.api.constant.EChipSource;
import com.dzpk.crazypoker.wallet.api.constant.ETransType;
import com.dzpk.crazypoker.wallet.repositories.mysql.IPlatformConsumptionConfigDao;
import com.dzpk.crazypoker.wallet.repositories.mysql.model.PlatformConsumptionConfigPo;
import com.dzpk.crazypoker.wallet.service.IAuditService;
import com.dzpk.crazypoker.wallet.service.IUserAccountService;
import com.dzpk.crazypoker.wallet.service.bean.ConsumeChipBo;
import com.dzpk.crazypoker.wallet.service.bean.UserAccountBo;
import com.dzpk.crazypoker.wallet.service.IWalletService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.TextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by jayce on 2019/2/25
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ClubServiceImpl implements IClubService {

    @Resource
    BusinessMessageSender businessMessageSender;

    @Autowired
    private IClubDao clubDao;

    @Autowired
    private ClubRecordPoMapper clubRecordDao;

    @Autowired
    private IClubTransation clubTransation;

    @Autowired
    private IUserService userService;

    @Autowired
    private IUserVipService vipService;

    @Autowired
    private BeanUtil beanUtil;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private IPermissionService permissionService;

    @Autowired
    private IPromotionService promotionService;

    @Autowired
    private ClubContactInfoPoMapper clubContactDao;

    @Autowired
    private ITribeService tribeService;
    @Autowired
    private TribeMemberPoMapper tribeMemberPoMapper;

    @Autowired
    private IUserAccountService userAccountService;

    @Autowired
    private IAuditService auditService;

    @Autowired
    private ClubMemberPoMapper clubMemberPoMapper;

    @Autowired
    private ClubProperties clubProperties;

    @Autowired
    private LinkRebateConfigDao linkRebateConfigDao;

    @Autowired
    private LinkOperateLogDao linkOperateLogDao;

    @Autowired
    private ClubBlacklistMapper clubBlacklistMapper;

    @Autowired
    private IUserBalanceAuditDao userBalanceAuditDao;

    @Autowired
    private UserLoginLogMapper userLoginLogMapper;

    @Autowired
    private IRoomSearchDao roomSearchDao;

    @Autowired
    private IUserGoldAccountDao userGoldAccountDao;

    @Autowired
    private IPlatformConsumptionConfigDao platformConsumptionConfigDao;

    @Autowired
    private IWalletService walletService;

    @Autowired
    private UserAccountPoMapper userAccountPoMapper;

    @Autowired
    private IMsgService msgService;

    @Autowired
    private ICustomerServiceConfigDao customerServiceConfigDao;

    @Autowired
    private IUserDetailsInfoDao userDetailsInfoDao;

    @Autowired
    AppBusinessMessageDao appBusinessMessageDao;

    @Autowired
    private OssService ossService;


    // 基金页大小
    private static final int PAGE_SIZE = 20;
    // 最长查询时间
    private static final long MAX_TIME = 60 * 60 * 24 * 14 * 1000;
    @Autowired
    private AppBusinessMessageSender appBusinessMessageSender;

    @Override
    public InvokedResult create(int creatorId, ClubCreationBo bo) {
        InvokedResult result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        try {
            if (bo.getPhone().isEmpty() && bo.getWechat().isEmpty() &&
                    bo.getTelegram().isEmpty() && bo.getEmail().isEmpty() && bo.getMessageStr().isEmpty()) {
                result.setCode(RespCode.CLUB_PARAM_ERROR.getCode());
                result.setMsg(RespCode.CLUB_PARAM_ERROR.getDesc());
                return result;
            }

            // 查询是否有创建俱乐部消息待处理，有的话不给于继续申请创建
            if (clubDao.getCreateClubRequestNums(creatorId) > 0) {
                result.setCode(RespCode.CLUB_CREATING.getCode());
                result.setMsg(RespCode.CLUB_CREATING.getDesc());
                return result;
            }

            // 先判断是否有重名的社区
            if (clubDao.checkNameInClub(bo.getClubName()) > 0) {
                result.setCode(RespCode.CLUB_NAME_EXIST.getCode());
                result.setMsg(RespCode.CLUB_NAME_EXIST.getDesc());
                return result;
            }

            if (bo.getClubHead().equals("")) {
                bo.setClubHead("-1");
            }
            if (bo.getUseCustom() == null) {
                if (!StringUtils.isEmpty(bo.getCustomUrl())) {
                    bo.setUseCustom(1);
                }
            }
            if (bo.getAreaId().equals("")) {
                bo.setAreaId("99912");
            }

            //扣除鑽石
            EChipConsumeFunction consumeFunction = EChipConsumeFunction.CREATE_CLUB;
            PlatformConsumptionConfigPo configPo = platformConsumptionConfigDao.findById(consumeFunction.getId());
            UserAccountPo userAccountPo = userAccountPoMapper.selectByPrimaryKey(creatorId);
            Integer cp = userAccountPo.getChip();
            Integer fee = configPo.getQuantity() * 100;

            if(fee != 0 && fee > cp){
                log.error("用户：{}，钻石数量不够，无法创建俱乐部！", creatorId);
                result.setCode(RespCode.USER_CHIP_ILLEGAL.getCode());
                result.setMsg(RespCode.USER_CHIP_ILLEGAL.getDesc());
                return result;
            }

            walletService.consumeChip(creatorId, ConsumeChipBo.builder()
                        .chip(fee)
                        .consume(consumeFunction.getConsumeType())
                        .description(consumeFunction.getConsumeType().getDesc())
                        .opId(creatorId)
                        .source(EChipSource.API)
                        .build(), ETransType.NULL.getCode());
            
            //生成审批请求
            log.info("创建俱乐部:userId:{}", creatorId);
            String msgId = UUID.randomUUID().toString();
            ClubCreateRequestPo clubCreateRequestPo = ClubCreateRequestPo.builder().clubName(bo.getClubName())
                    .clubHead(bo.getClubHead()).useCustom(bo.getUseCustom()).customUrl(bo.getCustomUrl())
                    .clubArea(bo.getAreaId()).clubDesc(bo.getDesc()).phone(bo.getPhone())
                    .wechat(bo.getWechat()).telegram(bo.getTelegram()).email(bo.getEmail()).message(bo.getMessageStr())
                    .fee(fee.toString())
                    .build();
            Integer change = clubTransation.addCreateClubRequest("", String.valueOf(creatorId), msgId,
                    JsonUtil.toJson(clubCreateRequestPo, false), EClubRequestMessageCode.CREATE.getCode());

            // 增加申请消息 增加成功则直接返回成功给申请者
            if (change > 0) {
                // 通知OSM
                UserDetailsInfoBo user = this.userService.findById(creatorId);
                String nickName = user == null ? "" : user.getNickName();
                messageSender.sendOsmMessage(OsmMessage.builder().senderId(String.valueOf(creatorId))
                        .type(EOsmMessageCode.CLUB_CREATE_APPLY.getCode())
                        .params(JsonUtil.toJson(ClubCreateApplyParams.builder().relateMsgId(msgId).userId(creatorId)
                                .userNickname(nickName).build(), false))
                        .build());
                result.setCode(RespCode.SUCCEED.getCode());
                result.setMsg(RespCode.SUCCEED.getDesc());
            }
        } catch (Exception e) {
            log.error("create error", e);
        }

        return result;
    }

    @Override
    public InvokedResult join(int userId, String clubId, String deviceCode, int type) {
        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        //get ip
        UserLoginLog loginLog = userLoginLogMapper.selectOneByUserId(userId);
        RespCode checkBlackList = checkBlackList(Integer.parseInt(clubId), userId, loginLog.getIp(), deviceCode);
        if(checkBlackList != null){
            result.setCode(checkBlackList.getCode());
            result.setMsg(checkBlackList.getDesc());
            return result;
        }

        ClubRecordPoExample example = new ClubRecordPoExample();
        example.or().andIdEqualTo(Integer.parseInt(clubId));
        List<ClubRecordPo> clubList = clubRecordDao.selectByExample(example);
        if (null == clubList || clubList.isEmpty()) {
            result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
            result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
            return result;
        }
        ClubRecordPo club = clubList.get(0);

        /*
        20241128：注释原有的消息推送代码 <AUTHOR>
        if (club.getUpperLimit() <= club.getClubMembers()) {
            // 已满员
            result.setCode(RespCode.CLUB_MEMBER_LIMIT.getCode());
            result.setMsg(RespCode.CLUB_MEMBER_LIMIT.getDesc());

            // 发送消息通知俱乐部创建者
            String insertMsgId = createClubMessageRecord("", club.getCreator(), club.getCreator(), "", "", "", "",
                    EMessageCode.CLUB_MEMBER_LIMIT_NOTIFY.getCode(), EMessageStatusCode.SUCCESS.getCode());

            if (TextUtils.isEmpty(insertMsgId)) {// 插入失败
                return result;
            }

            // 2、插入未读消息数量 msg的参数 需要json序列化的数据 取出返回是json反序列化出去的
            MessageUnreadDetailBo messageBo = MessageUnreadDetailBo.builder().time(new Date().getTime())
                    .type(EMessageCode.CLUB_MEMBER_LIMIT_NOTIFY.getCode()).content("").remark("").build();
            clubTransation.updateClubUnreadMsg(1, JsonUtil.toJson(messageBo, false),
                    Integer.parseInt(club.getCreator()));

            List<String> reciverIds = new ArrayList<>();
            reciverIds.add(club.getCreator());
            messageSender.sendClubMessage(ClubMessage.builder().reciverUserIds(reciverIds)
                    .type(EMessageCode.CLUB_MEMBER_LIMIT_NOTIFY.getCode())
                    .pushChannel(EMessageChannelCode.TIM.getCode()).build());

            return result;
        }

        // 将玩家加入本地社区成员列表中
        if (clubTransation.joinClub(club.getId(), userId, type, Integer.valueOf(club.getCreator()))) {
            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());
        }
        */

        // 每个用户一次只能有一个待处理的申请
        List<Map<String, Object>> businessRecords = appBusinessMessageDao.getBusinessRecordByInitiatorIdAndStatus((long) userId, AppMessageConstants.BusinessStatus.CLUB_JOIN_APPLY.PENDING, AppMessageConstants.BusinessCode.CLUB_JOIN_APPLY);

        // 从业务单中获取俱乐部信息
        AtomicBoolean isExist = new AtomicBoolean(false);
        businessRecords.forEach(businessRecord -> {
            String jsonString = MapValueUtils.getString(businessRecord, "business_data");
            // 转化为对象
            JoinClub businessData = JSONObject.parseObject(jsonString, JoinClub.class);
            // 判断是否已经有申请，同一个俱乐部只能有一个申请
            if (businessData.getClubId().equals(Long.valueOf(club.getId()))) {
                isExist.set(true);
            }
        });

        if (isExist.get()) {
            result.setCode(RespCode.CLUB_CHIP_REQUEST_PENDING.getCode());
            result.setMsg(RespCode.CLUB_CHIP_REQUEST_PENDING.getDesc());
            return result;
        }

        // 查询用户昵称
        UserDetailsInfoBo user = this.userService.findById(userId);
        String nickName = user == null ? "" : user.getNickName();

        // 直接发送加入俱乐部申请
        appBusinessMessageSender.notifyJoinClubApply(JoinClub.builder()
                .userId((long) userId)
                .userName(nickName)
                .clubId(Long.valueOf(club.getId()))
                .clubName(club.getName())
                .clubOwnerId(Long.valueOf(club.getCreator()))
                .build());


        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        return result;
    }

    /**
     * 审批
     *
     * @param userId
     * @param clubId
     * @param iUId
     * @param checkStatus
     * @return
     */
    @Override
    public InvokedResult check(int userId, Integer clubId, Integer iUId, Integer checkStatus) {
        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        log.info("审批申请加入俱乐部成员数据:userId:{},clubId:{},checkStatus:{}", userId, clubId, checkStatus);
        ClubRecordPoExample example = new ClubRecordPoExample();
        example.or().andIdEqualTo(clubId);
        List<ClubRecordPo> clubList = clubRecordDao.selectByExample(example);
        if (null == clubList || clubList.isEmpty()) {
            result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
            result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
            return result;
        }
        ClubRecordPo club = clubList.get(0);
        boolean res = clubTransation.checkClub(club.getId(), iUId, 1, Integer.valueOf(club.getCreator()),
                checkStatus == 2 ? true : false);
        if (res) {

            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());
        }
        log.info("审批申请加入俱乐部成员数据 结果:userId:{},clubId:{},checkStatus:{},data:{}", userId, clubId, checkStatus,
                JsonUtil.toJson(result, false));
        return result;
    }

    /**
     * 设置为管理员
     *
     * @param clubId
     * @param userId
     * @return
     */
    @Override
    public InvokedResult setAdmin(Integer clubId, Integer userId, Integer type) {
        log.info("设置成员为管理员或取消:userId:{},clubId:{},type:{}", userId, clubId, type);
        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        boolean res = clubTransation.setAdmin(clubId, userId, type);
        if (res) {
            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());
        }
        log.info("设置成员为管理员或取消:返回结果：{}", JsonUtil.toJson(result, false));
        return result;
    }

    /**
     * 查询俱乐部积分
     *
     * @param userId
     * @param clubId
     * @return
     */
    @Override
    public InvokedResult<Integer> queryIntegral(int userId, String clubId) {
        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        ClubRecordPoExample example = new ClubRecordPoExample();
        example.or().andIdEqualTo(Integer.parseInt(clubId));
        List<ClubRecordPo> clubList = clubRecordDao.selectByExample(example);
        if (null == clubList || clubList.isEmpty()) {
            result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
            result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
            return result;
        }
        ClubRecordPo club = clubList.get(0);
        ClubMemberPoExample clubMemberPoExample = new ClubMemberPoExample();
        ClubMemberPoExample.Criteria criteria = clubMemberPoExample.createCriteria();
        criteria.andClubIdEqualTo(club.getId()).andUserIdEqualTo(String.valueOf(userId));
        List<ClubMemberPo> clubMemberPos = clubMemberPoMapper.selectByExample(clubMemberPoExample);
        if (null == clubMemberPos || clubMemberPos.isEmpty()) {
            result.setCode(RespCode.CLUB_MEMBERS_NOT_EXIST.getCode());
            result.setMsg(RespCode.CLUB_MEMBERS_NOT_EXIST.getDesc());
            return result;
        }
        ClubMemberPo clubMemberPo = clubMemberPos.get(0);
        if (clubMemberPo.getType() == 1) {
            ClubDetailPo clubDetail = clubDao.getClubDetail(clubId);
            result.setData((int) clubDetail.getFund());
        } else {
            result.setData(clubMemberPo.getIntegral());
        }
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        log.info("查询俱乐部积分数据 结果:userId:{},clubId:{},integral:{},data:{}", userId, clubId,
                clubMemberPos.get(0).getIntegral());
        return result;
    }

    @Override
    public InvokedResult<ClubListDetailBo> getJoinClubList(int userId) {
        InvokedResult<ClubListDetailBo> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        ClubListDetailBo data = new ClubListDetailBo();
        data.setCanCreateClub(1);// 可以创建俱乐部
        if (clubDao.getCreateClubRequestNums(userId) > 0) {// 如果有俱乐部创建申请
            data.setCanCreateClub(0);// 不可以创建俱乐部
        }
        data.setCanCreate(0);// 默认是无权限，必须加入或创建了俱乐部而且有权限才置为1
        List<ClubJoinedPo> list = this.clubDao.getJoinedClubs(userId);

        if (null != list && !list.isEmpty()) {
            boolean checkHasTribe = false;// 已经加入联盟
            List<ClubListBo> dataList = this.beanUtil.map(list, ClubListBo.class);
            Iterator<ClubJoinedPo> clubIter = list.iterator();
            for (ClubListBo item : dataList) {
                ClubJoinedPo po = clubIter.next();
                item.setTime((item.getTempTime().getTime() / 1000));
                if (item.getCreator().equals(String.valueOf(userId))) {
                    // 创建的社区
                    item.setType(EClubIdentityCode.CREATOR.getCode());
                    ClubDetailPo clubDetail = clubDao.getClubDetail(String.valueOf(item.getId()));
                    item.setIntegral((int) clubDetail.getFund());
                } else {// 2-普通成员 4-管理员 5-待审核
                    item.setType(po.getRoleType());
                }
                checkHasTribe = tribeService.checkClubHasTribe(item.getId());

                List<RoomSearchRemainTimeDo> roomList = roomSearchDao.queryClubRoomByPage(item.getId(), Optional.ofNullable(item.getTribeId()).orElse(0));

                // filter tier rooms
                roomList = filterTierRooms(roomList, userId, item.getId());
                item.setRoomCount(roomList.size());
            }
            if (checkHasTribe) {// 必须加入联盟才有权限创建房间
                data.setCanCreate(permissionService.canCreateRoom(userId) ? 1 : 0);
            }
            data.setList(dataList);
        }
        result.setData(data);
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        return result;
    }

    /**
     * 过滤房间列表
     * @param roomList
     * @param userId
     * @param clubId
     * @return
     */
    private List<RoomSearchRemainTimeDo> filterTierRooms(List<RoomSearchRemainTimeDo> roomList, int userId,int clubId) {
        Integer tribeId = appBusinessMessageDao.getTribeIdByClubId(clubId);
        List<UserRoomTierBean> userRoomTiers = appBusinessMessageDao.findUserRoomTier(userId, tribeId);
        // 按照联盟ID分组
        Map<Integer, UserRoomTierBean> userRoomTierMap = userRoomTiers.stream()
                .collect(Collectors.toMap(UserRoomTierBean::getTribeId, Function.identity(), (existing, replacement) -> existing));
        List<RoomSearchRemainTimeDo> filteredRooms = new ArrayList<>();
        for (RoomSearchRemainTimeDo room : roomList) {
            int creator = room.getCreator();
            if (creator == userId) {
                // 如果是自己创建的房间，直接添加
                filteredRooms.add(room);
                continue;
            }
            // 获取房间类型
            Integer tribeRoom = appBusinessMessageDao.isTribeRoom(room.getRoomId());
            if (tribeRoom != null && tribeRoom == 1) {
                // 如果是联盟房间，检查用户是否有权限
                Integer roomTierValue = appBusinessMessageDao.getRoomTierValue(room.getRoomId());

                if (roomTierValue == null) {
                    // 如果房间没有设置值，则直接添加
                    filteredRooms.add(room);
                    continue;
                }
                UserRoomTierBean userRoomTierBean = userRoomTierMap.get(room.getTribeId());
                if (userRoomTierBean != null) {
                    Integer userValue = userRoomTierBean.getValue();
                    if (userValue == null) {
                        userValue = 0; // 如果用户没有设置值，则默认为0
                    }

                    Integer tierEntryLimit = appBusinessMessageDao.getRoomTierEntryLimit(room.getRoomId());
                    if (tierEntryLimit == null) {
                        tierEntryLimit = 1; // 默认值为1,只允许当前层级用户加入
                    }

                    if (tierEntryLimit == 1) {
                        if (userValue.equals(roomTierValue)) {
                            filteredRooms.add(room);
                        }
                    } else {
                        if (userValue <= roomTierValue) {
                            filteredRooms.add(room);
                        }
                    }
                }
            } else {
                // 如果不是联盟房间，直接添加
                filteredRooms.add(room);
            }
        }
        return filteredRooms;
    }


    @Override
    public InvokedResult<List<ClubSearchResultBo>> search(int userId, ClubSearchBo bo) {
        InvokedResult<List<ClubSearchResultBo>> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        log.info("进入：ClubServiceImpl.search");

        try {
            List<ClubSearchPo> list = null;
            if (bo.getType() == 1) {// 关键字搜索
                if (bo.getKey().startsWith("qaq") && bo.getKey().endsWith("abc")) {
                    String key = bo.getKey().replaceFirst("qaq", "").replaceFirst("abc", "").trim();
                    list = clubDao.searchClubByKey(key, "");
                } else {
                    list = clubDao.searchClubByKey(bo.getKey(), "QAQ测%");
                }
            } else if (bo.getType() == 2) {
                list = clubDao.searchClubByArea(bo.getKey(), "QAQ测%");
            }
            if (null == list || list.isEmpty()) {
                result.setCode(RespCode.CLUB_NOT_RESULT.getCode());
                result.setMsg(RespCode.CLUB_NOT_RESULT.getDesc());
            } else {
                List<ClubSearchResultBo> dataList = this.beanUtil.map(list, ClubSearchResultBo.class);
                for (ClubSearchResultBo dBo : dataList) {
                    if (dBo.getCreator().equals(userId)) {
                        dBo.setType(EClubIdentityCode.CREATOR.getCode());// 创建者
                    } else {
                        // 每个用户一次只能有一个待处理的申请
                        List<Map<String, Object>> businessRecords = appBusinessMessageDao.getBusinessRecordByInitiatorIdAndStatus((long) userId, AppMessageConstants.BusinessStatus.CLUB_JOIN_APPLY.PENDING, AppMessageConstants.BusinessCode.CLUB_JOIN_APPLY);
                        // 从业务单中获取俱乐部信息
                        AtomicBoolean isExist = new AtomicBoolean(false);
                        businessRecords.forEach(businessRecord -> {
                            String jsonString = MapValueUtils.getString(businessRecord, "business_data");
                            // 转化为对象
                            JoinClub businessData = JSONObject.parseObject(jsonString, JoinClub.class);
                            // 判断是否已经有申请，同一个俱乐部只能有一个申请
                            if (businessData.getClubId().equals(Long.valueOf(dBo.getId()))) {
                                isExist.set(true);
                            }
                        });

                        if (isExist.get()) {
                            dBo.setType(5);// 2-普通成员 4-管理员 5-待审核
                        } else {
                            ClubMemberPo clubMember = clubDao.getClubMemberInfo(dBo.getId(), userId);
                            if (null == clubMember) {
                                dBo.setType(EClubIdentityCode.NOT_MEMBER.getCode()); // 非成员
                            } else {
                                dBo.setType(clubMember.getType());// 2-普通成员 4-管理员 5-待审核
                            }
                        }
                    }

                    List<TribeJoinOrCreatedPo> tribeList = tribeDao.getJoinAndCreatedTribe(dBo.getId());
                    dBo.setTribeId(0);
                    dBo.setTribeName("");
                    dBo.setTribeHead("");
                    if (!tribeList.isEmpty()) {
                        dBo.setTribeId(tribeList.get(0).getTribeId());
                        dBo.setTribeName(tribeList.get(0).getTribeName());
                        dBo.setTribeHead(tribeList.get(0).getTribeHead());
                    }
                }
                result.setData(dataList);
                result.setCode(RespCode.SUCCEED.getCode());
                result.setMsg(RespCode.SUCCEED.getDesc());
            }
        } catch (Exception e) {
            log.error("get group error", e);
        }

        return result;
    }

    @Autowired
    private TribeRecordPoMapper tribeRecordPoMapper;

    @Override
    public InvokedResult<ClubDetailResultBo> getClubDetail(int userId, String clubId) {
        InvokedResult<ClubDetailResultBo> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        try {
            ClubDetailPo club = clubDao.getClubDetail(clubId);
            if (null == club) {
                result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                return result;
            }
            ClubDetailResultBo resultBo = this.beanUtil.map(club, ClubDetailResultBo.class);
            resultBo.setRoomCount(clubDao.getClubRoomCount(Integer.parseInt(clubId)));
            resultBo.setMembersChipTotal(getClubMembersChipTotal(club.getId()));
            // 最低转款配置，默认1
            resultBo.setTransferMin(100L);
            boolean needContact = false;// 是否需要返回联系方式
            ClubMemberPo clubMemberPo = clubDao.getClubMemberInfo(Integer.parseInt(clubId), userId);
            if (club.getCreatorId() != userId) {
                // 不是创建者
                if (null == clubMemberPo) {
                    // 不是成员
                    resultBo.setType(EClubIdentityCode.NOT_MEMBER.getCode());
                } else {
                    // 是成员
                    needContact = true;
                    resultBo.setType(clubMemberPo.getType());
                    List<ClubMemberListPo> clubMemberListData = clubDao.getLimitClubMemebers(Integer.parseInt(clubId),
                            4);
                    List<Integer> limitMemberIds = clubMemberListData.stream().map(ClubMemberListPo::getUserId)
                            .collect(Collectors.toList());
                    List<UserDetailsInfoBo> userDetailList = userService.findByIds(limitMemberIds);
                    List<String> userHead = new ArrayList<>();
                    for (UserDetailsInfoBo d : userDetailList) {
                        userHead.add(d.getHead());
                    }
                    // 俱乐部简略成员头像
                    resultBo.setUserHeads(userHead);
                    // 管理员设置和会长一样的角色
                    // if (clubMemberPo.getType() == 4) {
                    // resultBo.setType(EClubIdentityCode.CREATOR.getCode());
                    // }
                    resultBo.setCanTransferChip(clubMemberPo.getTransferCoinType() == 1);
                }
                result.setData(resultBo);
            } else {
                needContact = true;
                List<ClubMemberListPo> clubMemberListData = clubDao.getLimitClubMemebers(Integer.parseInt(clubId), 4);
                List<Integer> limitMemberIds = clubMemberListData.stream().map(ClubMemberListPo::getUserId)
                        .collect(Collectors.toList());
                List<UserDetailsInfoBo> userDetailList = userService.findByIds(limitMemberIds);
                List<String> userHead = new ArrayList<>();
                for (UserDetailsInfoBo d : userDetailList) {
                    userHead.add(d.getHead());
                }
                // 俱乐部简略成员头像
                resultBo.setUserHeads(userHead);
                resultBo.setType(EClubIdentityCode.CREATOR.getCode());
                resultBo.setCanTransferChip(clubMemberPo.getTransferCoinType() == 1);
                result.setData(resultBo);
            }

            TribeMemberPoExample tribeMemberPoExample = new TribeMemberPoExample();
            tribeMemberPoExample.or()
                    .andClubIdEqualTo(Integer.parseInt(clubId))
                    .andStatusEqualTo((short) 1);
            List<TribeMemberPo> tribeMemberPos = tribeMemberPoMapper.selectByExample(tribeMemberPoExample);
            if (!tribeMemberPos.isEmpty()) {
                TribeMemberPo tribeMemberPo = tribeMemberPos.get(0);
                resultBo.setChip(club.getChip());
                resultBo.setTribeId(tribeMemberPo.getTribeId());
                TribeRecordPo tribe = tribeRecordPoMapper.selectByPrimaryKey(tribeMemberPo.getTribeId());
                resultBo.setTribeName(tribe.getTribeName());
                resultBo.setTribeClubStatus(Integer.valueOf(tribeMemberPo.getStatus()));
                resultBo.setTribeOwner(tribe.getCreator().equals(String.valueOf(userId)));
                resultBo.setTribeRandomId(tribe.getRandomId());
                resultBo.setTransferMin(tribe.getTransferMin());
            }
            if (needContact) {
                ClubContactInfoPo clubContact = clubContactDao.selectByPrimaryKey(Integer.parseInt(clubId));
                if (null != clubContact
                        && (clubContact.getFirstContactType() > 0 || clubContact.getSecondContactType() > 0)) {
                    List<ClubContactInfoBo> dataList = new ArrayList<>();
                    if (clubContact.getFirstContactType() > 0) {
                        dataList.add(ClubContactInfoBo.builder().type(clubContact.getFirstContactType())
                                .content(clubContact.getFirstContactInfo()).build());
                    }
                    if (clubContact.getSecondContactType() > 0) {
                        dataList.add(ClubContactInfoBo.builder().type(clubContact.getSecondContactType())
                                .content(clubContact.getSecondContactInfo()).build());
                    }
                    resultBo.setContactList(dataList);
                }
            }
            result.setData(resultBo);

            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());
        } catch (Exception e) {
            log.error("get group error", e);
        }

        return result;
    }

    @Override
    public InvokedResult modify(int userId, int clubId, String content, int modifyType) {
        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        try {
            ClubRecordBo club = getClub(clubId);
            if (null == club) {
                // 俱乐部不存在
                result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                return result;
            }
            //
            // if (Integer.parseInt(club.getCreator()) != userId) {
            // //没有合法权限
            // result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
            // result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
            // return result;
            // }
            int change;
            if (modifyType == 1) {// 先判断是否有重名的社区
                int check = clubDao.checkNameInClub(content);
                if (check > 0) {// 俱乐部重名
                    result.setCode(RespCode.CLUB_NAME_EXIST.getCode());
                    result.setMsg(RespCode.CLUB_NAME_EXIST.getDesc());
                    return result;
                }

                //判断是否为第一次修改
                if(club.getModifyNameTimes() > 0){
                    //扣除改名使用的鑽石
                    EChipConsumeFunction consumeFunction = EChipConsumeFunction.RENAME_CLUB;
                    PlatformConsumptionConfigPo configPo = platformConsumptionConfigDao.findById(consumeFunction.getId());
                    UserAccountPo userAccountPo = userAccountPoMapper.selectByPrimaryKey(userId);
                    Integer cp = userAccountPo.getChip();
                    Integer fee = configPo.getQuantity() * 100;
        
                    if(fee != 0 && fee > cp){
                        log.error("用户：{}，钻石数量不够，俱乐部無法改名！", userId);
                        result.setCode(RespCode.USER_CHIP_ILLEGAL.getCode());
                        result.setMsg(RespCode.USER_CHIP_ILLEGAL.getDesc());
                        return result;
                    }
    
                    walletService.consumeChip(userId, ConsumeChipBo.builder()
                            .chip(fee)
                            .consume(consumeFunction.getConsumeType())
                            .description(consumeFunction.getConsumeType().getDesc())
                            .opId(userId)
                            .source(EChipSource.API)
                            .build(), ETransType.NULL.getCode());
                }
              
                // 更新俱乐部名字
                change = clubTransation.updateClubName(clubId, content);
                if (change <= 0) {
                    return result;
                }
            } else if (modifyType == 2) {// 更新俱乐部描述信息
                change = clubTransation.updateClubDesc(clubId, content);
                if (change <= 0) {// 更新俱乐部简介失败
                    return result;
                }
            } else if (modifyType == 3) {// 更新俱乐部头像
                change = clubTransation.updateClubHead(clubId, content);
                if (change <= 0) {// 更新俱乐部头像失败
                    return result;
                }
            }
        } catch (Exception e) {
            log.error("modify error", e);
        }
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        return result;
    }

    @Override
    public InvokedResult updateClubCustomHead(int userId, int clubId, String customUrl) {
        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        try {
            ClubRecordBo club = getClub(clubId);
            if (null == club) {
                // 俱乐部不存在
                result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                return result;
            }
            if (club.getUseCustom().equals(1)) {
                if (!StringUtils.isEmpty(club.getCustomUrl())) {
                    // 删除旧资源
//                    ossService.deleteFile(club.getCustomUrl());
                }
            }
            // 更新俱乐部头像
            int change = clubTransation.updateClubCustomHead(clubId, customUrl);
            if (change <= 0) {// 更新俱乐部头像失败
                return result;
            }
            club.setUseCustom(1);
            club.setCustomUrl(customUrl);
            result.setData(club);
            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());

            return result;
        } catch (Exception e) {
            log.error("modify error", e);
        }

        return result;
    }

    @Override
    public InvokedResult setClubHead(int userId, int clubId, String head) {
        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        try {
            ClubRecordBo club = getClub(clubId);
            if (null == club) {
                // 俱乐部不存在
                result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                return result;
            }
            if (club.getUseCustom().equals(1)) {
                if (!StringUtils.isEmpty(club.getCustomUrl())) {
                    // 删除旧资源
//                    ossService.deleteFile(club.getCustomUrl());
                }
            }
            // 更新俱乐部头像
            int change = clubTransation.setClubHead(clubId, head);
            if (change <= 0) {// 更新俱乐部头像失败
                return result;
            }
            club.setUseCustom(0);
            club.setHeader(head);
            club.setCustomUrl(null);
            result.setData(club);
            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());

            return result;
        } catch (Exception e) {
            log.error("modify error", e);
        }

        return result;
    }

    @Override
    public InvokedResult<ClubHotBo> getHotClubs(int userId, int reqType) {
        InvokedResult<ClubHotBo> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        ClubHotBo data = new ClubHotBo();
        int clubNums = clubDao.getClubNums(userId);
        if (clubNums > 0)
            data.setJoinStatus(1);
        List<ClubRecordPo> clubList = clubDao.getHotClubs(reqType == 1 ? 5 : 30);
        List<ClubHotListBo> dataList = this.beanUtil.map(clubList, ClubHotListBo.class);
        for (ClubHotListBo bo : dataList) {
            ClubMemberPo clubMember = clubDao.getClubMemberInfo(bo.getId(), userId);
            if (null == clubMember) {
                bo.setType(EClubIdentityCode.NOT_MEMBER.getCode()); // 非成员
            } else {
                bo.setType(clubMember.getType());// 1-創建者 2-普通成员 4-管理员 5-待审核
            }
            List<TribeJoinOrCreatedPo> tribeList = tribeDao.getJoinAndCreatedTribe(bo.getId());
            bo.setTribeId(0);
            bo.setTribeName("");
            bo.setTribeHead("");
            bo.setTribeUseCustom(0);
            bo.setTribeCustomUrl("");
            if (!tribeList.isEmpty()) {
                bo.setTribeId(tribeList.get(0).getTribeId());
                bo.setTribeName(tribeList.get(0).getTribeName());
                bo.setTribeHead(tribeList.get(0).getTribeHead());
                bo.setTribeUseCustom(tribeList.get(0).getUseCustom());
                bo.setTribeCustomUrl(tribeList.get(0).getCustomUrl());
            }
        }
        data.setList(dataList);

        result.setData(data);
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        return result;
    }

    @Override
    public InvokedResult<List<ClubMemberResultBo>> getClubMembersList(int userId, int clubId) {
        InvokedResult<List<ClubMemberResultBo>> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        ClubRecordBo club = getClub(clubId);
        if (null == club) {// 俱乐部不存在
            result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
            result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
            return result;
        }

        try {

            List<ClubMemberDetailListPo> clubMemberList = clubDao.getClubMemberDetails(clubId);
            if (null == clubMemberList || clubMemberList.isEmpty()) {
                return result;
            }

            // 重新进行一次排序 (4-管理员 > 1-创建者 > 2-普通成员)
            int[] ordering = { 99, 30, 40, 99, 20 };
            clubMemberList.sort(Comparator.comparingInt(m -> m.getType() < ordering.length ? ordering[m.getType()] : ordering[0]));

            List<Integer> memberIdList = clubMemberList.stream().map(ClubMemberListPo::getUserId)
                    .collect(Collectors.toList());

            // 暂时屏蔽
            // List<Friend> remarkList = friendMapper.batchGetRemarks(userId, memberIdList);
            // Map<Integer, String> remarksMap =
            // remarkList.stream().collect(Collectors.toMap(Friend::getFriendId,
            // Friend::getRealRemarks));

            List<UserDetailsInfoBo> listUserinfo = userService.findByIds(memberIdList);
            //log.trace("-------listUserinfo-------: {}", JSONObject.toJSONString(listUserinfo));
            Map<Integer, UserDetailsInfoBo> userInfoMaps = listUserinfo.stream()
                    .collect(Collectors.toMap(UserDetailsInfoBo::getUserId, Function.identity()));

            List<ClubMemberResultBo> clubMemberResultBoList = this.beanUtil.map(clubMemberList,
                    ClubMemberResultBo.class);

            List<UserVipBaseBo> userVipBos = vipService.findUserVipBaseBoByUserIds(memberIdList);
            Map<Integer, UserVipBaseBo> userVipMaps = userVipBos.stream()
                    .collect(Collectors.toMap(UserVipBaseBo::getUserId, Function.identity()));

            for (ClubMemberResultBo clubMember : clubMemberResultBoList) {
                int memberUserId = clubMember.getUserId();
                UserDetailsInfoBo userInfo = userInfoMaps.get(memberUserId);
                if (null == userInfo) {
                    log.info("db error: why userId=" + memberUserId + " doesn't exist ??");
                    continue;
                }
                //log.trace("-------userInfo-------: {}", JSONObject.toJSONString(userInfo));
                clubMember.setUid(userInfo.getRandomNum());
                clubMember.setUserHead(userInfo.getHead());
                clubMember.setUseCustom(userInfo.getUseCustom());
                clubMember.setCustomUrl(userInfo.getCustomUrl());
                clubMember.setSex(userInfo.getSex());

                String nick = userInfo.getNickName();
                // 暂不返回
                // if (remarksMap.get(userInfo.getUserId()) != null
                // && !remarksMap.get(userInfo.getUserId()).equals("")) {
                // nick = remarksMap.get(userInfo.getUserId());
                // }
                clubMember.setNickName(nick);

                // vip等级
                UserVipBaseBo vipBaseBo = userVipMaps.get(memberUserId);
                clubMember.setVipLevel(vipBaseBo == null ? 0 : vipBaseBo.getVipCode());// 0无VIP 1侯爵 2伯爵 3王公
            }
            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());
            result.setData(clubMemberResultBoList);
        } catch (Exception e) {
            log.error("get group member error", e);
        }
        return result;
    }

    @Override
    public InvokedResult modifyContact(int userId, int clubId, int fType, String fContact, int sType, String sContact) {
        InvokedResult result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        boolean checkStaus = checkClubPermisson(userId, clubId);
        if (!checkStaus) {// 无操作权限
            result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
            return result;
        }

        ClubContactInfoPo clubContactInfoPo = clubContactDao.selectByPrimaryKey(clubId);
        ClubContactInfoPo record = new ClubContactInfoPo();
        record.setClubId(clubId);
        record.setFirstContactType(fType);
        record.setFirstContactInfo(fType == 0 ? "" : fContact);
        record.setSecondContactType(sType);
        record.setSecondContactInfo(sType == 0 ? "" : sContact);
        Integer change = 0;
        if (null == clubContactInfoPo) {// 新增
            change = clubContactDao.insertSelective(record);
        } else {// 更新
            change = clubContactDao.updateByPrimaryKeySelective(record);
        }
        if (change > 0) {
            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());
        }

        return result;
    }

    @Override
    public InvokedResult<ClubFundDetailResultBo> getClubFundDetail(int userId, int clubId) {
        InvokedResult<ClubFundDetailResultBo> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        try {

            ClubDetailPo club = clubDao.getClubDetail(String.valueOf(clubId));
            if (null == club) {
                result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                return result;
            }

            if (club.getCreatorId() != userId) {// 只有创建者有权限查询
                result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
                result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
                return result;
            }

            ClubFundDetailResultBo respone = new ClubFundDetailResultBo();
            UserAccountBo userInfo = this.userAccountService.findById(userId);
            if (null != userInfo) {
                respone.setUserChip(userInfo.getChip());// 该地方只返回可提豆
            } else {
                respone.setUserChip(0);
            }
            respone.setFund(club.getFund());
            // 对赌仓
            LinkRebateConfigPo link = linkRebateConfigDao.getClubLinkRebateConfig(clubId);

            respone.setIsOpen(0);
            respone.setBetChip(0);
            respone.setIsBurst(0);
            if (link != null) {
                respone.setBetChip(link.getChip());
                respone.setIsOpen(link.isOpen() ? 1 : 0);
                respone.setIsBurst(link.isBurst_status() ? 1 : 0);
            }

            result.setData(respone);
            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());

        } catch (Exception e) {
            log.error("get group error", e);
        }

        return result;
    }

    @Override
    public InvokedResult<ClubFundHistoryBo> getClubFundHistory(int userId, int clubId, long time, int direction) {
        InvokedResult<ClubFundHistoryBo> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        try {

            ClubDetailPo club = clubDao.getClubDetail(String.valueOf(clubId));
            if (null == club) {
                result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                return result;
            }

            if (club.getCreatorId() != userId) {// 只有创建者有权限查询
                result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
                result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
                return result;
            }

            ClubFundHistoryBo data = new ClubFundHistoryBo();
            List<ClubFundHistoryListBo> dataList = new ArrayList<>();
            data.setDirection(direction);

            List<ClubFundHistoryPo> list = new ArrayList<ClubFundHistoryPo>();
            long minTime = ((System.currentTimeMillis() - MAX_TIME) / 1000);// 最长查询14天
            if (time != 0) {
                // 下拉刷新
                if (1 == direction) {
                    list = clubDao.getFundListGtByTime(clubId, (time / 1000), minTime, PAGE_SIZE);
                } else if (0 == direction) {
                    // 上拉加载更多历史数据
                    list = clubDao.getFundListLtByTime(clubId, (time / 1000), minTime, PAGE_SIZE);
                }
            } else {
                // 首次获取数据
                list = clubDao.getFundList(clubId, minTime, PAGE_SIZE);
            }
            if (!list.isEmpty()) {
                dataList = this.beanUtil.map(list, ClubFundHistoryListBo.class);
                for (ClubFundHistoryListBo d : dataList) {
                    d.setTime(d.getCreateTime().getTime());
                }
            }
            data.setList(dataList);
            result.setData(data);
            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());

        } catch (Exception e) {
            log.error("get group error", e);
        }

        return result;
    }

    @Override
    public InvokedResult<ClubLinkRebateBo> getClubFundBetHistory(int userId, int clubId, long time, int direction) {
        InvokedResult<ClubLinkRebateBo> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        try {

            ClubDetailPo club = clubDao.getClubDetail(String.valueOf(clubId));
            if (null == club) {
                result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                return result;
            }

            if (club.getCreatorId() != userId) {// 只有创建者有权限查询
                result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
                result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
                return result;
            }

            ClubLinkRebateBo data = new ClubLinkRebateBo();
            List<ClubLinkRebateListBo> dataList = new ArrayList<>();
            data.setDirection(direction);
            LinkRebateConfigPo linkRebateConfig = linkRebateConfigDao.getClubLinkRebateConfig(clubId);
            LinkRebateConfigPo clubConfig = linkRebateConfigDao.getClubConfig(clubId);
            List<linkOperateLog> list = new ArrayList<>();

            long minTime = ((System.currentTimeMillis() - MAX_TIME) / 1000);// 最长查询14天
            if (time != 0) {
                // 下拉刷新
                if (1 == direction) {
                    if (linkRebateConfig != null && clubConfig != null) {
                        list = linkOperateLogDao.getRecordBetXiaLog(clubId, linkRebateConfig.getRecord_id(),
                                (time / 1000), minTime, PAGE_SIZE);
                    } else {
                        list = linkOperateLogDao.getClubBetXiaLog(clubId, (time / 1000), minTime, PAGE_SIZE);
                    }
                } else if (0 == direction) {
                    if (linkRebateConfig != null && clubConfig != null) {
                        list = linkOperateLogDao.getRecordBetShangLog(clubId, linkRebateConfig.getRecord_id(),
                                (time / 1000), minTime, PAGE_SIZE);
                    } else {
                        list = linkOperateLogDao.getClubBetShangLog(clubId, (time / 1000), minTime, PAGE_SIZE);
                    }
                }
            } else {
                // 首次获取数据
                if (linkRebateConfig != null && clubConfig != null) {
                    list = linkOperateLogDao.getRecordBetInitLog(clubId, linkRebateConfig.getRecord_id(), minTime,
                            PAGE_SIZE);
                } else {
                    list = linkOperateLogDao.getClubBetInitLog(clubId, minTime, PAGE_SIZE);
                }
            }
            if (!list.isEmpty()) {
                for (linkOperateLog link : list) {
                    ClubLinkRebateListBo clubList = new ClubLinkRebateListBo();
                    clubList.setAmount(link.getChip());
                    clubList.setTime(link.getCreate_time().getTime());
                    clubList.setCreateTime(link.getCreate_time());
                    clubList.setType(link.getOperator_type());
                    clubList.setUserId(link.getClub_id() + "");
                    clubList.setUserName(link.getClub_id() + "");
                    clubList.setOperatorId(link.getOperator_id() + "");
                    clubList.setOperatorName(link.getOperator_name());
                    dataList.add(clubList);
                }
            }
            data.setList(dataList);
            result.setData(data);
            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());

        } catch (Exception e) {
            log.error("get group error", e);
        }

        return result;
    }

    @Override
    public InvokedResult giveFundToUser(int optId, int clubId, String memberId, int amount, int type) {
        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        try {
            // 当前操作玩家为部长
            ClubDetailPo club = clubDao.getClubDetail(String.valueOf(clubId));

            if (null == club) {
                result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                return result;
            }

            if (club.getCreatorId() != optId) {// 无操作权限
                result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
                result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
                return result;
            }

            if (club.getFrozen() == 1) {// 基金冻结
                result.setCode(RespCode.CLUB_FUND_INVALID.getCode());
                result.setMsg(RespCode.CLUB_FUND_INVALID.getDesc());
                return result;
            } else if (club.getFund() < amount) {// 基金不足
                result.setCode(RespCode.CLUB_FUND_NOT_ENOUGH.getCode());
                result.setMsg(RespCode.CLUB_FUND_NOT_ENOUGH.getDesc());
                return result;
            } else {
                // 审计操作
                auditService.auditOperation(optId, AuditOperationCode.RECHARGE_CLUB_FUND.getCode());

                // 增加基金发放奖励记录
                UserDetailsInfoBo userInfo = userService.findById(optId);
                String optName = "";
                String optRandomId = "";
                String optHeader = "";
                if (null != userInfo) {
                    optName = userInfo.getNickName();
                    optRandomId = userInfo.getRandomNum();
                    optHeader = userInfo.getHead();
                }
                String userName = "";
                int memberUserId = 0;
                UserDetailsInfoBo memberUserInfo = userService.findByRandomNum(memberId);
                if (null != memberUserInfo) {
                    memberUserId = memberUserInfo.getUserId();
                    userName = memberUserInfo.getNickName();
                }

                // 扣除基金，下发金豆，插入基金下放历史
                ClubFundHistoryPo history = new ClubFundHistoryPo();
                history.setClubId(clubId);
                history.setOperatorName(optName);
                history.setUserId(memberId);
                history.setUserName(userName);
                history.setAmount(amount);
                history.setType(type == 1 ? EClubFundHistoryCode.GIVEN_FUND.getCode()
                        : EClubFundHistoryCode.GIVEN_FUND_TO_EXTRACT_CHIP.getCode());
                history.setReqMsgId(0);
                history.setOperatorId(optRandomId);
                boolean status = clubTransation.giveFundToUser(optId, memberUserId, clubId, amount, history);
                EMessageCode msgCode = type == 1 ? EMessageCode.MONEY_GIVE_CLUB_FUND
                        : EMessageCode.MONEY_GIVE_CLUB_FUND_TO_NO_EX_CHIP;
                if (status) {
                    // 1、插入发放记录
                    // 插入到钱包消息记录
                    String insertMsgId = createClubGiveFundMessageRecord(String.valueOf(optId),
                            String.valueOf(memberUserId), optHeader, "", club.getClubName(), String.valueOf(amount),
                            msgCode.getCode(), EMessageStatusCode.SUCCESS.getCode());
                    if (TextUtils.isEmpty(insertMsgId)) {// 插入失败
                        return result;
                    }

                    // 2、插入未读消息数量 msg的参数 需要json序列化的数据 取出返回是json反序列化出去的
                    MessageUnreadDetailBo messageBo = MessageUnreadDetailBo.builder().time(System.currentTimeMillis())
                            .type(msgCode.getCode()).content(club.getClubName()).remark(String.valueOf(amount)).build();
                    clubTransation.updateMoneyUnreadMsg(1, JsonUtil.toJson(messageBo, false), memberUserId);

                    // 3、提示被发放玩家
                    List<String> reciverIds = new ArrayList<>();
                    reciverIds.add(String.valueOf(memberUserId));
                    MoneyMessage moneyMessage = MoneyMessage.builder().content(club.getClubName())
                            .remark(String.valueOf(amount / 100.00)).type(msgCode.getCode()).reciverUserIds(reciverIds)
                            .pushChannel(EMessageChannelCode.TIM.getCode()).build();
                    String mqMsgId = messageSender.sendMoneyMessage(moneyMessage);
                    log.info(String.format("giveFundToUser => insertMsgId:%s => mqMsgId:%s", insertMsgId, mqMsgId));

                    result.setCode(RespCode.SUCCEED.getCode());
                    result.setMsg(RespCode.SUCCEED.getDesc());
                }

            }
        } catch (Exception e) {
            log.error("giveFundToUser error", e);
        }

        return result;
    }

    @Override
    public InvokedResult rechargeFund(int userId, int clubId, int fund) {
        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        try {
            ClubDetailPo club = clubDao.getClubDetail(String.valueOf(clubId));

            if (null == club) {
                result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                return result;
            }

            if (club.getCreatorId() != userId) {// 无操作权限
                result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
                result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
                return result;
            }

            boolean auditStatus = auditService.auditUserChip(userId);
            if (!auditStatus) {
                result.setCode(RespCode.USER_AUDIT_ILLEGAL.getCode());
                result.setMsg(RespCode.USER_AUDIT_ILLEGAL.getDesc());
                return result;
            }

            // 审计操作
            auditService.auditOperation(userId, AuditOperationCode.RECHARGE_CLUB_FUND.getCode());

            // 扣除操作人金豆 增加俱乐部基金
            String optName = "";
            String optRandomId = "";
            UserDetailsInfoBo uInfo = userService.findById(userId);
            if (null != uInfo) {
                optName = uInfo.getNickName();
                optRandomId = uInfo.getRandomNum();
            }

            // 判断创建者的钱包是否停用

            if (!userAccountService.checkUserAccountValidByUserId(userId)) {
                RespCode respCode = RespCode.WALLET_DISABLE;
                result.setCode(respCode.getCode());
                result.setMsg(respCode.getDesc());
                return result;
            }

            ClubFundHistoryPo history = new ClubFundHistoryPo();
            history.setClubId(clubId);
            history.setOperatorName(optName);
            history.setUserId(optRandomId);
            history.setUserName(optName);
            history.setAmount(fund);
            history.setType(EClubFundHistoryCode.RECHARGE_FUND.getCode());
            history.setReqMsgId(0);
            history.setOperatorId(optRandomId);
            boolean status = clubTransation.rechargeFund(userId, history);
            if (status) {
                result.setCode(RespCode.SUCCEED.getCode());
                result.setMsg(RespCode.SUCCEED.getDesc());
            }
        } catch (ServiceException e) {
            log.error("recharge fund error", e);
            result.setCode(e.getCode());
            result.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("recharge fund error", e);
        }

        return result;
    }

    @Override
    public ClubRecordBo getClub(int clubId) {
        ClubRecordPoExample example = new ClubRecordPoExample();
        example.or().andIdEqualTo(clubId);
        List<ClubRecordPo> clubList = clubRecordDao.selectByExample(example);
        return clubList == null ? null
                : clubList.size() == 0 ? null : this.beanUtil.map(clubList.get(0), ClubRecordBo.class);
    }

    @Override
    public List<ClubRecordBo> getClubs(Collection<Integer> clubIds) {
        List<Integer> distinctIds = clubIds.stream().distinct().collect(Collectors.toList());
        ClubRecordPoExample example = new ClubRecordPoExample();
        example.or().andIdIn(distinctIds);
        List<ClubRecordPo> clubList = clubRecordDao.selectByExample(example);
        return this.beanUtil.map(clubList, ClubRecordBo.class);
    }

    @Override
    public boolean checkClubPermisson(int userId, int clubId) {
        return clubDao.getClubByClubIdAndUserId(userId, clubId) > 0;
    }

    @Override
    public ClubRecordBo getUserClub(int userId) {
        List<ClubRecordPo> clubList = clubDao.getUserClub(String.valueOf(userId));
        return clubList == null ? null
                : clubList.size() > 0 ? this.beanUtil.map(clubList.get(0), ClubRecordBo.class) : null;
    }

    @Override
    public List<ClubRecordBo> getUserClubs(int userId) {
        List<ClubRecordPo> clubList = clubDao.getUserClub(String.valueOf(userId));
        return clubList.stream().map(it -> this.beanUtil.map(it, ClubRecordBo.class)).collect(Collectors.toList());
    }

    /**
     * 根据创建者ID获取该俱乐部信息,目前逻辑是一个人只能加入一个俱乐部
     *
     * @param creatorId
     * @return
     */
    @Override
    public ClubRecordBo findByCreatorId(Integer creatorId) {
        if (creatorId == null) {
            return null;
        }
        ClubRecordPoExample example = new ClubRecordPoExample();
        example.or().andCreatorEqualTo(creatorId.toString());
        List<ClubRecordPo> clubList = clubRecordDao.selectByExample(example);

        return clubList.size() == 0 ? null : beanUtil.map(clubList.get(0), ClubRecordBo.class);
    }

    /**
     * 批量获取俱乐部实例，暴露给外部调用。
     *
     * @param clubIds
     * @return
     */
    @Override
    public List<ClubRecordBo> findClubByIds(Collection<Integer> clubIds) {
        if (clubIds == null || clubIds.isEmpty()) {
            return new ArrayList<>(0);
        }
        List<Integer> disClubId = clubIds.stream().distinct().collect(Collectors.toList());
        ClubRecordPoExample example = new ClubRecordPoExample();
        example.or().andIdIn(disClubId);
        return beanUtil.map(clubRecordDao.selectByExample(example), ClubRecordBo.class);
    }

    @Override
    public List<ClubRecordBo> findClubByUserIds(Integer userId) {
        ClubMemberPoExample example = new ClubMemberPoExample();
        ClubMemberPoExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(String.valueOf(userId));
        List<ClubMemberPo> clubMemberPos = clubMemberPoMapper.selectByExample(example);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(clubMemberPos)) {
            // 获取到俱乐部id
            Set<Integer> collect = clubMemberPos.stream().map(e -> e.getClubId()).collect(Collectors.toSet());
            return findClubByIds(collect);
        }
        return null;
    }

    @Override
    public void modifyTransType(Integer userId, Integer transType) {
        RespCode respCode;

        if (!Integer.valueOf(0).equals(transType)
                && !Integer.valueOf(1).equals(transType)) {
            respCode = RespCode.PARAM_INVALID;
            throw new ServiceException(respCode.getCode(), respCode.getDesc());
        }

        ClubRecordBo clubRecordBo = findByCreatorId(userId);
        if (clubRecordBo == null) {
            respCode = RespCode.PARAM_UNKNOWN;
            throw new ServiceException(respCode.getCode(), respCode.getDesc());
        }

        // 判断创建者的钱包是否停用

        if (!userAccountService.checkUserAccountValidByUserId(userId)) {
            respCode = RespCode.WALLET_DISABLE;
            throw new ServiceException(respCode.getCode(), respCode.getDesc());
        }

        if (!checkModTransTypeTimeLimit(clubRecordBo)) {
            respCode = RespCode.CLUB_PAY_CHANNEL_TIME_LIMIT;
            throw new ServiceException(respCode.getCode(), respCode.getDesc());
        }

        if (clubRecordBo.getTransType().equals(transType)) {
            // 没发生改变,返回成功
            return;
        }

        clubTransation.modifyTransType(clubRecordBo, transType);

        return;
    }

    /**
     * 检查俱乐部本身是否代充渠道
     *
     * @param bo
     * @return
     */
    private boolean checkIsPayChannel(ClubRecordBo bo) {
        return Boolean.TRUE.equals(bo.getPayChannelFlag());
    }

    /**
     * 检查俱乐部本身修改渠道时间是否在30天内
     *
     * @param bo
     * @return
     */
    private boolean checkModTransTypeTimeLimit(ClubRecordBo bo) {
        if (bo.getModTransTypeTime() == null
                || (System.currentTimeMillis()
                        - bo.getModTransTypeTime() > clubProperties.getModTransLimitTime())) {
            return true;
        }
        return false;
    }

    /**
     * 根据用户id,返回其所在俱乐部基本信息,如果没有加入俱乐部,抛出没有加入俱乐部异常
     *
     * @param userId
     * @return
     */
    @Override
    public ClubRecordBo findClubRecordByUserId(int userId) {
        // 从用户成员表中获取俱乐部id
        ClubMemberPoExample example = new ClubMemberPoExample();
        example.or().andUserIdEqualTo(userId + "");
        List<ClubMemberPo> list = clubMemberPoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException(RespCode.CLUB_NOT_HAS_USER.getCode(), RespCode.CLUB_NOT_HAS_USER.getDesc());
        }

        ClubRecordPo po = clubRecordDao.selectByPrimaryKey(list.get(0).getClubId());

        if (po == null) {
            // 俱乐部不存在
            throw new ServiceException(RespCode.CLUB_NOT_EXIST.getCode(), RespCode.CLUB_NOT_EXIST.getDesc());
        }

        return beanUtil.map(po, ClubRecordBo.class);
    }

    @Override
    public PayChannelBo findMyPayChannelByClubIdAnsIsNormal(Integer clubId) {
        PayChannelPo po = clubDao.selectMyPayChannelPoByClubIdAndIsNormal(clubId);
        // 查看一下支持的渠道信息

        return beanUtil.map(po, PayChannelBo.class);
    }

    // 俱乐部消息
    public String createClubMessageRecord(String clubId, String senderId, String reciverId, String header, String title,
            String content, String remark, Integer type, Integer msgStatus) {
        String msgId = UUID.randomUUID().toString();// 消息id
        return clubTransation.createClubMessageRecord(msgId, clubId, senderId, reciverId, header, title, content,
                remark, type, msgStatus) ? msgId : "";
    }

    // （基金）钱包消息
    public String createClubGiveFundMessageRecord(String senderId, String reciverId, String header, String title,
            String content, String remark, Integer type, Integer msgStatus) {
        String msgId = UUID.randomUUID().toString();// 消息id
        return clubTransation.createClubGiveFundMessageRecord(msgId, senderId, reciverId, header, title, content,
                remark, type, msgStatus) ? msgId : "";
    }

    /**
     * 查找合适的渠道支付,返还其信息
     * 1.根据权值查找合适的sdk
     *
     * @param channelBo
     * @return
     */
    @Override
    public ClubChannelPaymentBo findBestChannelPayment(PayChannelBo channelBo, Integer type) {
        return beanUtil.map(findBestPaymentCodeByPayChannel(channelBo, type), ClubChannelPaymentBo.class);
    }

    /**
     * 查找合适的渠道支付,返还其信息
     * 1.根据权值查找合适的sdk
     *
     * @param channelBo
     * @return
     */
    private ClubChannelPaymentPo findBestPaymentCodeByPayChannel(PayChannelBo channelBo, Integer type) {
        List<ClubChannelPaymentPo> paymentPos = clubDao.selectBestPaymentCodeByPayChannelId(channelBo.getId(), type);
        if (paymentPos == null) {
            return null;
        }

        Map<Integer, List<ClubChannelPaymentPo>> paymentPoMap = paymentPos.stream()
                .collect(Collectors.groupingBy(ClubChannelPaymentPo::getPriority));

        Integer priority = paymentPoMap.keySet().stream().min(Integer::compareTo).get();

        List<ClubChannelPaymentPo> target = paymentPoMap.get(priority);
        Random random = new Random(System.currentTimeMillis());
        int index = Math.abs(random.nextInt() % target.size());
        return target.get(index);

    }

    @Override
    public InvokedResult<ClubBetChipBo> getClubFundTrade(Integer userId, Integer clubId, Integer fund,
            Integer fundType) {
        InvokedResult<ClubBetChipBo> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        try {
            log.info("俱乐部[{}] 玩家[{}] 转入转出[{}] 金额[{}]", clubId, userId, fundType, fund);
            ClubDetailPo club = clubDao.getClubDetail(String.valueOf(clubId));
            if (null == club) {
                result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                return result;
            }

            if (club.getCreatorId() != userId) {// 只有创建者有权限查询
                result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
                result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
                return result;
            }
            LinkRebateConfigPo linkRebateConfig = linkRebateConfigDao.getClubLinkRebateConfig(clubId);
            // 转入准出标示 0转入 1转出
            if (fundType == 0) {// Integer clubId,Integer betChip,Integer id ,Integer chip
                if (club.getFund() < fund) {
                    result.setCode(RespCode.CLUB_FUND_NOT_ENOUGH.getCode());
                    result.setMsg(RespCode.CLUB_FUND_NOT_ENOUGH.getDesc());
                    return result;
                }
                if (linkRebateConfig == null) {
                    Integer tribeId = linkRebateConfigDao.getTribeId(clubId.toString()); // 联盟ID
                    linkRebateConfigDao.addClubRebateConfig(clubId, tribeId);
                }

                linkRebateConfigDao.updateChip(clubId, fund);
                linkRebateConfigDao.updateClubChip(club.getId(), -1 * fund);
            } else {
                if (linkRebateConfig == null || fund + linkRebateConfig.getChip() < 0) {
                    result.setCode(RespCode.CLUB_FUND_NOT_ENOUGH.getCode());
                    result.setMsg(RespCode.CLUB_FUND_NOT_ENOUGH.getDesc());
                    return result;
                }

                linkRebateConfigDao.updateChip(clubId, -1 * fund);
                linkRebateConfigDao.updateClubChip(club.getId(), fund);
            }

            UserDetailsInfoBo uInfo = userService.findById(userId);
            String nickName = "";
            if (uInfo != null) {
                nickName = uInfo.getNickName();
            }

            Integer chip = fundType == 0 ? fund : -1 * fund;
            // 记录日志
            linkOperateLogDao.addLinkOperateLog(userId, clubId, chip, fundType, nickName);

            ClubBetChipBo respone = new ClubBetChipBo();

            club = clubDao.getClubDetail(String.valueOf(clubId));

            respone.setFund(club.getFund());
            // 对赌仓
            Integer betChip = linkRebateConfigDao.getClubLinkRebateChip(clubId);
            respone.setBetChip(betChip);

            result.setData(respone);
            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());

        } catch (Exception e) {
            log.error("get group error", e);
        }

        return result;
    }

    /**
     * 新增或者扣减积分
     * 这里不能直接对积分进行新增或者扣减，必须先放到消息里面去，然后通过性消息的审批在进行添加到
     * 用户积分表当中
     *
     * @param userId   用户id
     * @param clubId   俱乐部id
     * @param integral 积分数据
     * @param type     新增或者扣减类型
     * @return
     */
    @Override
    public InvokedResult<Integer> selfApplyIntegral(int userId, String clubId, Integer integral, Integer type) {
        InvokedResult result = new InvokedResult();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        ClubRecordPoExample example = new ClubRecordPoExample();
        example.or().andIdEqualTo(Integer.parseInt(clubId));
        List<ClubRecordPo> clubList = clubRecordDao.selectByExample(example);
        if (null == clubList || clubList.isEmpty()) {
            result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
            result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
            return result;
        }
        log.info("申请带入或者收回俱乐部积分，俱乐部数据查询正常");
        ClubRecordPo club = clubList.get(0);
        ClubMemberPoExample clubMemberPoExample = new ClubMemberPoExample();
        ClubMemberPoExample.Criteria criteria = clubMemberPoExample.createCriteria();
        criteria.andClubIdEqualTo(club.getId()).andUserIdEqualTo(String.valueOf(userId));
        List<ClubMemberPo> clubMemberPos = clubMemberPoMapper.selectByExample(clubMemberPoExample);
        if (null == clubMemberPos || clubMemberPos.isEmpty()) {
            result.setCode(RespCode.CLUB_MEMBERS_NOT_EXIST.getCode());
            result.setMsg(RespCode.CLUB_MEMBERS_NOT_EXIST.getDesc());
            return result;
        }
        UserDetailsInfoBo userinfo = userService.findById(userId);
        if (userinfo == null) {
            result.setCode(RespCode.CLUB_NOT_HAS_USER.getCode());
            result.setMsg(RespCode.CLUB_NOT_HAS_USER.getDesc());
            return result;
        }
        // 积分数据
        ClubMemberPo clubMemberPo = clubMemberPos.get(0);
        if (type == 1) {
            // 申请
            log.info("新增积分：integral:{},coutn:{}", clubMemberPo.getIntegral(), integral);
            // clubMemberPo.setIntegral(clubMemberPo.getIntegral() + integral);
        } else {
            // 扣减
            log.info("扣减积分：integral:{},coutn:{}", clubMemberPo.getIntegral(), integral);
            clubMemberPo.setIntegral(clubMemberPo.getIntegral() - integral);
            if (clubMemberPo.getIntegral() < 0) {
                log.info("扣减积分，小于0，设置为0：integral:{},coutn:{}", clubMemberPo.getIntegral(), integral);
                // 直接返回失败
                result.setCode(RespCode.USER_INTEGRAL_ILLEGAL.getCode());
                result.setMsg(RespCode.USER_INTEGRAL_ILLEGAL.getDesc());
                return result;
            }
        }
        // 保存积分
        clubMemberPoMapper.updateByPrimaryKey(clubMemberPo);
        log.info("申请带入或者收回俱乐部积分，俱乐部成员信息正常");
        ClubMessagePo clubMessagePo = new ClubMessagePo();
        clubMessagePo.setMsgId(UUID.randomUUID().toString());
        clubMessagePo.setClubId(String.valueOf(club.getId()));
        // 消息的接受者，这里肯定只能是俱乐部的创建者
        clubMessagePo.setReciverId(String.valueOf(club.getCreator()));
        clubMessagePo.setSenderId(String.valueOf(userId));
        clubMessagePo.setHeader("-1");
        clubMessagePo.setTitle((type == 1 ? "申请发放" : "申请收回") + "俱乐部积分");
        // 俱乐部的内容，需要进行明确的定义，这样在遇到1017的时候才能正确的进行解析 clubId,俱乐部名称,type,积分
        String content = clubId + "," + club.getName() + "," + type + "," + integral + "," + userId + ","
                + userinfo.getNickName();
        clubMessagePo.setContent(content);

        clubMessagePo.setType(1017);
        clubMessagePo.setDel(1);
        clubMessagePo.setMsgStatus(0);
        clubMessagePo.setMsgStatusType(0);
        clubMessagePo.setRemark("个人申请");
        log.info("申请带入或者收回俱乐部积分，数据:{}", JsonUtil.toJson(clubMessagePo, true));
        try {
            clubRecordDao.insertClubMessage(clubMessagePo);
        } catch (Exception e) {
            log.info("申请带入积分数据库插入数据报错.....");
        }
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        return result;
    }

    /**
     * 积分审批
     *
     * @param msgId
     * @param handleType
     * @return
     */
    @Override
    public int handleMessage(String msgId, Integer handleType) {
        try {
            ClubMessagePo message = clubRecordDao.selectMessageById(msgId);
            if (message == null) {
                log.info("消息为空，没有查询到数据，直接返回");
                return 0;
            }
            // 获取到值进行更新
            String content = message.getContent();
            // 这样在遇到1017的时候才能正确的进行解析 clubId,俱乐部名称,type,积分
            String[] split = content.split(",");
            if (split.length == 0) {
                return 0;
            }
            Integer count = Integer.parseInt(split[3]);
            log.info("消息为:msgId:{},content:{},count:{}", msgId, content, count);
            // 拼接查询积分数据
            ClubMemberPoExample poExample = new ClubMemberPoExample();
            ClubMemberPoExample.Criteria criteria = poExample.createCriteria();
            criteria.andUserIdEqualTo(split[4]);
            criteria.andClubIdEqualTo(Integer.parseInt(split[0]));
            // 查询俱乐部成员积分信息
            List<ClubMemberPo> clubMemberPos = clubMemberPoMapper.selectByExample(poExample);
            if (CollectionUtils.isEmpty(clubMemberPos)) {
                return 0;
            }
            ClubMemberPo clubMemberPo = clubMemberPos.get(0);

            // 消息审批通过的情况下
            if (handleType == 2) {
                log.info("审批不通过，消息数据为:{}", JsonUtil.toJson(message, true));
                log.info("成员信息:{}", JsonUtil.toJson(clubMemberPo, true));
                if (Integer.parseInt(split[2]) == 2) {
                    // 补回
                    log.info("扣减积分：integral:{},coutn:{}", clubMemberPo.getIntegral(), count);
                    clubMemberPo.setIntegral(clubMemberPo.getIntegral() + count);
                }
                log.info("计算积分之后成员信息:{}", JsonUtil.toJson(clubMemberPo, true));
                // 更新俱乐部成员信息
                clubMemberPoMapper.updateByPrimaryKey(clubMemberPo);
            } else {
                // 审批通过
                log.info("消息审批通过：{}", msgId);
                if (Integer.parseInt(split[2]) == 1) {
                    // 申请
                    log.info("新增积分：integral:{},coutn:{}", clubMemberPo.getIntegral(), count);
                    clubMemberPo.setIntegral(clubMemberPo.getIntegral() + count);
                }
                log.info("计算积分之后成员信息:{}", JsonUtil.toJson(clubMemberPo, true));
                // 更新俱乐部成员信息
                clubMemberPoMapper.updateByPrimaryKey(clubMemberPo);
            }
            clubRecordDao.updateClubMessageType(msgId, handleType);
            return 1;
        } catch (Exception e) {
            log.info("更新俱乐部成员积分数据失败,{}", e.getMessage());
            e.printStackTrace();
            return 0;
        }
    }

    @Override
    public InvokedResult<Object> grantGold(int userId, int clubId, String memberId, long amount) {
        InvokedResult<Object> result = new InvokedResult<>();
        ClubRecordPo club = clubRecordDao.selectByPrimaryKey(clubId);

        if (!club.getCreator().equals(userId + "")) {// 无操作权限
            result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
            return result;
        }

        long userBalanceBefore = userAccountService.getUserGold(userId);
        long balanceChange = amount;

        if (userBalanceBefore < balanceChange) {
            result.setCode(RespCode.CLUB_FUND_NOT_ENOUGH.getCode());
            result.setMsg("您的金币不足，发放失败");
            return result;
        }

        UserDetailsInfoBo member = userService.findByRandomNum(memberId);

        if (member == null) {
            result.setCode(RespCode.CLUB_MEMBERS_NOT_EXIST.getCode());
            result.setMsg("用户不存在");
            return result;
        }

        ClubMemberPoExample example = new ClubMemberPoExample();
        example.or().andClubIdEqualTo(club.getId()).andUserIdEqualTo(String.valueOf(member.getUserId()));
        List<ClubMemberPo> clubMemberPos = clubMemberPoMapper.selectByExample(example);

        if (clubMemberPos.isEmpty()) {
            result.setCode(RespCode.CLUB_NOT_HAS_USER.getCode());
            result.setMsg("目标用户未加入您的俱乐部，发放失败");
            return result;
        }

        long memberBalanceBefore = userAccountService.getUserGold(member.getUserId());

        log.info("新增金币：amount:{}, uid:{}", amount, member.getUserId());

        String txUuid = UUID.randomUUID().toString();
        UserBalanceAuditLog debitLog = UserBalanceAuditLog.builder()
                .txUuid(txUuid)
                .userId(userId)
                .source(UserBalanceAuditLog.Source.API.getValue())
                .operatorId(userId)
                .clubId(clubId)
                .type(UserBalanceAuditLog.Type.RECHARGE.getValue())
                .balanceType(UserBalanceAuditLog.BalanceType.GOLD.getValue())
                .balanceBefore(userBalanceBefore)
                .balanceChange(-balanceChange)
                .build();
        UserBalanceAuditLog creditLog = UserBalanceAuditLog.builderFrom(debitLog)
                .userId(member.getUserId())
                .balanceBefore(memberBalanceBefore)
                .balanceChange(balanceChange)
                .build();
        userBalanceAuditDao.addUserBalanceAuditLog(Arrays.asList(debitLog, creditLog));

        userGoldAccountDao.updateUserGold(userId, debitLog.getBalanceChange());
        userGoldAccountDao.updateUserGold(member.getUserId(), creditLog.getBalanceChange());

        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        // 发送app消息
        appBusinessMessageSender.notifySendOrRecycleGold(SendOrRecycleGold.builder()
                .tplCode(AppMessageConstants.TplCode.WALLET0005)
                .playerId(member.getRandomNum())
                .userId(member.getUserId().longValue())
                .playerName(member.getNickName())
                .clubName(club.getName())
                .clubId(club.getId().longValue())
                .goldAmount(amount)
                .build());

        return result;
    }

    @Transactional
    @Override
    public InvokedResult grantIntegral(int userId, String clubId, String memberId, Integer integral) {
        InvokedResult result = new InvokedResult();
        ClubRecordPo club = clubRecordDao.selectByPrimaryKey(Integer.valueOf(clubId));

        if (!club.getCreator().equals(userId + "")) {// 无操作权限
            result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
            return result;
        }

        if (club.getFund() < integral * 100) {
            result.setCode(RespCode.CLUB_FUND_NOT_ENOUGH.getCode());
            result.setMsg("您的俱乐部币不足，发放失败");
            return result;
        }

        UserDetailsInfoBo user = userService.findByRandomNum(memberId);

        if (user == null) {
            result.setCode(RespCode.CLUB_MEMBERS_NOT_EXIST.getCode());
            result.setMsg("用户不存在");
            return result;
        }

        ClubMemberPoExample example = new ClubMemberPoExample();
        example.or().andClubIdEqualTo(club.getId()).andUserIdEqualTo(String.valueOf(user.getUserId()));
        List<ClubMemberPo> clubMemberPos = clubMemberPoMapper.selectByExample(example);

        if (clubMemberPos.size() == 0) {
            result.setCode(RespCode.CLUB_NOT_HAS_USER.getCode());
            result.setMsg("目标用户未加入您的俱乐部，发放失败");
            return result;
        }

        if (clubDao.updateClubFund(club.getId(), -integral * 100) == 0) {
            log.error("俱乐部币发放失败!!! updateClubFund==0");
            result.setCode(RespCode.FAILED.getCode());
            result.setMsg("发放失败");
            return result;
        }

        UserDetailsInfoBo optInfo = userService.findById(userId);
        ClubFundHistoryPo history = new ClubFundHistoryPo();
        history.setClubId(club.getId());
        history.setOperatorName(optInfo.getNickName());
        history.setUserId(user.getRandomNum());
        history.setUserName(user.getNickName());
        history.setAmount(integral * 100);
        history.setType(EClubFundHistoryCode.GRANT_INTEGRAL.getCode());
        history.setReqMsgId(0);
        history.setOperatorId(optInfo.getRandomNum());

        if (clubDao.createFundHistory(history) <= 0) {
            log.error("俱乐部币发放失败!!! createFundHistory<=0");
            result.setCode(RespCode.FAILED.getCode());
            result.setMsg("发放失败");
            return result;
        }

        int integralBalanceBefore = clubDao.getUserClubIntegral(club.getId(), user.getUserId());
        int updated = clubDao.addUserClubIntegral(club.getId(), user.getUserId(), integral * 100);
        log.info("新增俱乐部币：integral:{}, uid:{}, updated:{}", integral, user.getUserId(), updated);

        String txUuid = UUID.randomUUID().toString();
        UserBalanceAuditLog fundLog = UserBalanceAuditLog.builder()
                .txUuid(txUuid)
                .userId(Integer.parseInt(club.getCreator()))
                .source(UserBalanceAuditLog.Source.API.getValue())
                .operatorId(userId)
                .clubId(history.getClubId())
                .type(UserBalanceAuditLog.Type.RECHARGE.getValue())
                .balanceType(UserBalanceAuditLog.BalanceType.CLUB_CHIP.getValue())
                .balanceBefore(club.getFund())
                .balanceChange(-history.getAmount())
                .build();
        UserBalanceAuditLog integralLog = UserBalanceAuditLog.builder()
                .txUuid(txUuid)
                .userId(user.getUserId())
                .source(UserBalanceAuditLog.Source.API.getValue())
                .operatorId(userId)
                .clubId(history.getClubId())
                .type(UserBalanceAuditLog.Type.RECHARGE.getValue())
                .balanceType(UserBalanceAuditLog.BalanceType.CLUB_CHIP.getValue())
                .balanceBefore(integralBalanceBefore)
                .balanceChange(history.getAmount())
                .build();
        userBalanceAuditDao.addUserBalanceAuditLog(Arrays.asList(fundLog, integralLog));

        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        return result;
    }

    @Transactional
    @Override
    public InvokedResult retrieveIntegral(int userId, String clubId, String memberId, Integer integral) {
        InvokedResult result = new InvokedResult();
        ClubRecordPo club = clubRecordDao.selectByPrimaryKey(Integer.valueOf(clubId));

        if (!club.getCreator().equals(userId + "")) {// 无操作权限
            result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
            return result;
        }

        UserDetailsInfoBo user = userService.findByRandomNum(memberId);

        if (user == null) {
            result.setCode(RespCode.CLUB_MEMBERS_NOT_EXIST.getCode());
            result.setMsg("用户不存在");
            return result;
        }

        ClubMemberPoExample example = new ClubMemberPoExample();
        example.or().andClubIdEqualTo(club.getId()).andUserIdEqualTo(String.valueOf(user.getUserId()));
        List<ClubMemberPo> clubMemberPos = clubMemberPoMapper.selectByExample(example);

        if (clubMemberPos.size() == 0) {
            result.setCode(RespCode.CLUB_NOT_HAS_USER.getCode());
            result.setMsg("目标用户未加入您的俱乐部，回收失败");
            return result;
        }

        int beforeIntegral = clubMemberPos.get(0).getIntegral();
        if (beforeIntegral < integral * 100) {
            result.setCode(RespCode.CLUB_FUND_NOT_ENOUGH.getCode());
            result.setMsg("用户俱乐部币不足，回收失败");
            return result;
        }

        if (clubDao.updateClubFund(club.getId(), integral * 100) == 0) {
            log.error("俱乐部币回收失败!!! updateClubFund==0");
            result.setCode(RespCode.FAILED.getCode());
            result.setMsg("回收失败");
            return result;
        }

        UserDetailsInfoBo optInfo = userService.findById(userId);
        ClubFundHistoryPo history = new ClubFundHistoryPo();
        history.setClubId(club.getId());
        history.setOperatorName(optInfo.getNickName());
        history.setUserId(user.getRandomNum());
        history.setUserName(user.getNickName());
        history.setAmount(integral * 100);
        history.setType(EClubFundHistoryCode.RETRIEVE_INTEGRAL.getCode());
        history.setReqMsgId(0);
        history.setOperatorId(optInfo.getRandomNum());

        if (clubDao.createFundHistory(history) <= 0) {
            log.error("俱乐部币回收失败!!! createFundHistory<=0");
            result.setCode(RespCode.FAILED.getCode());
            result.setMsg("回收失败");
            return result;
        }

        int integralBalanceBefore = clubDao.getUserClubIntegral(club.getId(), user.getUserId());
        int updated = clubDao.addUserClubIntegral(club.getId(), user.getUserId(), -integral * 100);
        log.info("回收俱乐部币：integral:{}, uid:{}, updated:{}", integral, user.getUserId(), updated);

        String txUuid = UUID.randomUUID().toString();
        UserBalanceAuditLog fundLog = UserBalanceAuditLog.builder()
                .txUuid(txUuid)
                .userId(Integer.parseInt(club.getCreator()))
                .source(UserBalanceAuditLog.Source.API.getValue())
                .operatorId(userId)
                .clubId(history.getClubId())
                .type(UserBalanceAuditLog.Type.XIAFEN.getValue())
                .balanceType(UserBalanceAuditLog.BalanceType.CLUB_CHIP.getValue())
                .balanceBefore(club.getFund())
                .balanceChange(history.getAmount())
                .build();
        UserBalanceAuditLog integralLog = UserBalanceAuditLog.builder()
                .txUuid(txUuid)
                .userId(user.getUserId())
                .source(UserBalanceAuditLog.Source.API.getValue())
                .operatorId(userId)
                .clubId(history.getClubId())
                .type(UserBalanceAuditLog.Type.XIAFEN.getValue())
                .balanceType(UserBalanceAuditLog.BalanceType.CLUB_CHIP.getValue())
                .balanceBefore(integralBalanceBefore)
                .balanceChange(-history.getAmount())
                .build();
        userBalanceAuditDao.addUserBalanceAuditLog(Arrays.asList(fundLog, integralLog));

        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        return result;
    }

    @Transactional
    @Override
    public InvokedResult<Object> grant(int userId, ClubChipReq request) {
        InvokedResult<Object> result = new InvokedResult<>();
        UserDetailsInfoBo user = userService.findByRandomNum(request.getUserId());
        LockedActuator.withLock(() -> {
            result.setCode(RespCode.FAILED.getCode());
            result.setMsg(RespCode.FAILED.getDesc());
            ClubRecordPo club = clubRecordDao.selectByPrimaryKey(request.getClubId());

            if (!club.getCreator().equals(userId + "")) {// 无操作权限
                result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
                result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
                return;
            }
            long tribeChipRequested = request.getTribeChip();
            if (club.getChip() < tribeChipRequested) {
                result.setCode(RespCode.TRIBE_CHIP_NOT_ENOUGH.getCode());
                result.setMsg("您的联盟币不足，发放失败");
                return;
            }
            if (user == null) {
                result.setCode(RespCode.USER_NOT_EXIST.getCode());
                result.setMsg("用户不存在");
                return;
            }
            ClubMemberPoExample example = new ClubMemberPoExample();
            example.or().andClubIdEqualTo(club.getId()).andUserIdEqualTo(String.valueOf(user.getUserId()));
            List<ClubMemberPo> clubMemberPos = clubMemberPoMapper.selectByExample(example);
            if (clubMemberPos.isEmpty()) {
                result.setCode(RespCode.CLUB_MEMBERS_NOT_EXIST.getCode());
                result.setMsg("目标用户未加入您的俱乐部，发放失败");
                return;
            }

            ClubRecordPo cPo = new ClubRecordPo();
            cPo.setId(club.getId());
            cPo.setChip(club.getChip() - tribeChipRequested);
            clubRecordDao.updateByPrimaryKeySelective(cPo);

            TribeMemberPoExample tribeMemberPoExample = new TribeMemberPoExample();
            tribeMemberPoExample.or().andClubIdEqualTo(request.getClubId());
            List<TribeMemberPo> tribeMemberPos = tribeMemberPoMapper.selectByExample(tribeMemberPoExample);
            TribeMemberPo tribeMemberPo = tribeMemberPos.get(0);

            UserTribeAccountPo userTribeChip = clubDao.findUserTribeChip(user.getUserId(), tribeMemberPo.getTribeId(), tribeMemberPo.getClubId());
            long chipsBefore = 0;
            if (userTribeChip == null) {
                // 先初始化
                UserTribeAccountPo po = new UserTribeAccountPo();
                po.setChips(tribeChipRequested);
                po.setTribeId(tribeMemberPo.getTribeId());
                po.setClubId(tribeMemberPo.getClubId());
                po.setUserId(user.getUserId());
                po.setCreateTime(new Date());
                clubDao.insertTribeAccount(po);
            } else {
                chipsBefore = userTribeChip.getChips();
                clubDao.addUserChip(tribeChipRequested, user.getUserId(), tribeMemberPo.getTribeId(), tribeMemberPo.getClubId());
            }

            // 记录
            ClubChipLog log = new ClubChipLog();
            log.setClubId(request.getClubId());
            log.setType(ClubChipLog.Type.GRANT.getValue());
            log.setChip(-tribeChipRequested);
            log.setChipBefore(club.getChip());
            log.setCreateTime(new Date());
            log.setOtherId(user.getUserId().toString());
            log.setUserRandomId(user.getRandomNum());
            clubDao.insertLog(log);

            // 玩家充值记录
            String txUuid = UUID.randomUUID().toString();
            UserBalanceAuditLog userBalanceAuditLog = UserBalanceAuditLog.builder()
                    .txUuid(txUuid)
                    .userId(user.getUserId())
                    .source(UserBalanceAuditLog.Source.API.getValue())
                    .operatorId(Integer.parseInt(club.getCreator()))
                    .clubId(request.getClubId())
                    .tribeId(tribeMemberPo.getTribeId())
                    .type(UserBalanceAuditLog.Type.RECHARGE.getValue())
                    .balanceType(UserBalanceAuditLog.BalanceType.TRIBE_CHIP.getValue())
                    .balanceBefore(chipsBefore)
                    .balanceChange(tribeChipRequested)
                    .build();
            userBalanceAuditDao.addUserBalanceAuditLog(Collections.singletonList(userBalanceAuditLog));

            // 通過 res 服務異步通知前端帳戶變化
            messageSender.sendUserBalanceSyncMessage(UserBalanceSyncMessage.builder()
                    .userId(userId)
                    .clubId(request.getClubId())
                    .balance(chipsBefore + tribeChipRequested)
                    .coinType(UserBalanceSyncMessage.CoinType.TRIBE_CHIP)
                    .timestamp(System.currentTimeMillis())
                    .build());

            // 发送app消息
            appBusinessMessageSender.notifySendOrRecycleTribeChip(SendOrRecycleTribeChip.builder()
                    .tplCode(AppMessageConstants.TplCode.WALLET0007)
                    .playerId(user.getRandomNum())
                    .userId(user.getUserId().longValue())
                    .userName(user.getNickName())
                    .clubName(club.getName())
                    .clubId(club.getId().longValue())
                    .clubOwnerId(Long.valueOf(club.getCreator()))
                    .tribeId(tribeMemberPo.getTribeId().longValue())
                    .tribeCoinAmount(tribeChipRequested)
                    .build());

            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());

            // 发送交易计数消息
            businessMessageSender.sendTransactionCountMessage(TransactionCountMessage.builder()
                    .autoAssignTier(1)
                    .tribeId(tribeMemberPo.getTribeId())
                    .clubId(club.getId())
                    .userId(userId)
                    .build());

            businessMessageSender.sendTransactionCountMessage(TransactionCountMessage.builder()
                    .autoAssignTier(1)
                    .tribeId(tribeMemberPo.getTribeId())
                    .clubId(club.getId())
                    .userId(user.getUserId())
                    .build());

        }, RedisDistributedLockGenerator.generate(
                RedisLockConfigCode.CLUB_GRANT_CHIP_CLUB, RedisLockKeyGenerator.generateClubKey(request.getClubId())
        ), RedisDistributedLockGenerator.generate(
                RedisLockConfigCode.CLUB_GRANT_CHIP_USER,
                RedisLockKeyGenerator.generateUserKey(user.getUserId())
        ));

        return result;
    }

    @Transactional
    @Override
    public InvokedResult<Object> retrieve(int userId, ClubChipReq request) {
        InvokedResult<Object> result = new InvokedResult<>();
        UserDetailsInfoBo user = userService.findByRandomNum(request.getUserId());
        LockedActuator.withLock(() -> {
            result.setCode(RespCode.FAILED.getCode());
            result.setMsg(RespCode.FAILED.getDesc());
            ClubRecordPo club = clubRecordDao.selectByPrimaryKey(request.getClubId());

            if (!club.getCreator().equals(userId + "")) {// 无操作权限
                result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
                result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
                return;
            }

            if (user == null) {
                result.setCode(RespCode.USER_NOT_EXIST.getCode());
                result.setMsg("用户不存在");
                return;
            }
            ClubMemberPoExample example = new ClubMemberPoExample();
            example.or().andClubIdEqualTo(club.getId()).andUserIdEqualTo(user.getUserId() + "");
            List<ClubMemberPo> clubMemberPos = clubMemberPoMapper.selectByExample(example);
            if (clubMemberPos.isEmpty()) {
                result.setCode(RespCode.CLUB_MEMBERS_NOT_EXIST.getCode());
                result.setMsg("目标用户未加入您的俱乐部，回收失败");
                return;
            }

            TribeMemberPoExample tribeMemberPoExample = new TribeMemberPoExample();
            tribeMemberPoExample.or().andClubIdEqualTo(request.getClubId());
            List<TribeMemberPo> tribeMemberPos = tribeMemberPoMapper.selectByExample(tribeMemberPoExample);
            TribeMemberPo tribeMemberPo = tribeMemberPos.get(0);
            UserTribeAccountPo userTribeChip = clubDao.findUserTribeChip(user.getUserId(), tribeMemberPo.getTribeId(), tribeMemberPo.getClubId());
            long tribeChipRequested = request.getTribeChip();
            if (userTribeChip == null || (userTribeChip.getChips() < tribeChipRequested)) {
                result.setCode(RespCode.TRIBE_CHIP_NOT_ENOUGH.getCode());
                result.setMsg("用户联盟币不足，回收失败");
                return;
            }

            ClubRecordPo cPo = new ClubRecordPo();
            cPo.setId(club.getId());
            cPo.setChip(club.getChip() + tribeChipRequested);
            clubRecordDao.updateByPrimaryKeySelective(cPo);

            clubDao.addUserChip(-tribeChipRequested, user.getUserId(), tribeMemberPo.getTribeId(), tribeMemberPo.getClubId());

            // 回收记录
            ClubChipLog log = new ClubChipLog();
            log.setClubId(request.getClubId());
            log.setType(ClubChipLog.Type.RECYCLE.getValue());
            log.setChip(tribeChipRequested);
            log.setChipBefore(club.getChip());
            log.setCreateTime(new Date());
            log.setOtherId(user.getUserId().toString());
            log.setUserRandomId(user.getRandomNum());
            clubDao.insertLog(log);

            // 玩家回收记录
            String txUuid = UUID.randomUUID().toString();
            UserBalanceAuditLog userBalanceAuditLog = UserBalanceAuditLog.builder()
                    .txUuid(txUuid)
                    .userId(user.getUserId())
                    .source(UserBalanceAuditLog.Source.API.getValue())
                    .operatorId(Integer.parseInt(club.getCreator()))
                    .clubId(request.getClubId())
                    .tribeId(tribeMemberPo.getTribeId())
                    .type(UserBalanceAuditLog.Type.XIAFEN.getValue())
                    .balanceType(UserBalanceAuditLog.BalanceType.TRIBE_CHIP.getValue())
                    .balanceBefore(userTribeChip.getChips())
                    .balanceChange(-tribeChipRequested)
                    .build();
            userBalanceAuditDao.addUserBalanceAuditLog(Collections.singletonList(userBalanceAuditLog));

            // 通過 res 服務異步通知前端帳戶變化
            messageSender.sendUserBalanceSyncMessage(UserBalanceSyncMessage.builder()
                    .userId(userId)
                    .clubId(request.getClubId())
                    .balance(userTribeChip.getChips() - tribeChipRequested)
                    .coinType(UserBalanceSyncMessage.CoinType.TRIBE_CHIP)
                    .timestamp(System.currentTimeMillis())
                    .build());

            // 发送app消息
            appBusinessMessageSender.notifySendOrRecycleTribeChip(SendOrRecycleTribeChip.builder()
                    .tplCode(AppMessageConstants.TplCode.WALLET0008)
                    .playerId(user.getRandomNum())
                    .userId(user.getUserId().longValue())
                    .userName(user.getNickName())
                    .clubName(club.getName())
                    .clubId(club.getId().longValue())
                    .clubOwnerId(Long.valueOf(club.getCreator()))
                    .tribeId(tribeMemberPo.getTribeId().longValue())
                    .tribeCoinAmount(tribeChipRequested)
                    .build());

            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());

            // 发送交易计数消息
            businessMessageSender.sendTransactionCountMessage(TransactionCountMessage.builder()
                    .autoAssignTier(1)
                    .tribeId(tribeMemberPo.getTribeId())
                    .clubId(club.getId())
                    .userId(userId)
                    .build());

            // 发送交易计数消息
            businessMessageSender.sendTransactionCountMessage(TransactionCountMessage.builder()
                    .autoAssignTier(1)
                    .tribeId(tribeMemberPo.getTribeId())
                    .clubId(club.getId())
                    .userId(user.getUserId())
                    .build());

        }, RedisDistributedLockGenerator.generate(
                RedisLockConfigCode.CLUB_RETRIEVE_CHIP_CLUB, RedisLockKeyGenerator.generateClubKey(request.getClubId())
        ), RedisDistributedLockGenerator.generate(
                RedisLockConfigCode.CLUB_RETRIEVE_CHIP_USER,
                RedisLockKeyGenerator.generateUserKey(user.getUserId())
        ));
        return result;
    }

    @Autowired
    private ITribeDao tribeDao;

    @Override
    public InvokedResult<List<ClubChipLog>> chipLog(int userId, ClubChipReq request) {
        InvokedResult<List<ClubChipLog>> result = new InvokedResult<>();
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        ClubRecordPo club = clubRecordDao.selectByPrimaryKey(request.getClubId());

        if (!club.getCreator().equals(userId + "")) {// 无操作权限
            result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
            return result;
        }
        int limit = request.getLimit() == null ? 10 : request.getLimit();
        result.setData(tribeDao.findClubChipLog(request.getClubId(), limit, request.getNextPage()));
        return result;
    }

    @Override
    public InvokedResult<List<ClubGoldLog>> goldLog(int userId, ClubGoldLogReq request) {
        InvokedResult<List<ClubGoldLog>> result = new InvokedResult<>();
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        ClubRecordPo club = clubRecordDao.selectByPrimaryKey(request.getClubId());

        if (!club.getCreator().equals(userId + "")) {// 无操作权限
            result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
            return result;
        }
        int limit = request.getLimit() == null ? 10 : request.getLimit();
        List<UserBalanceAuditLog> auditLogs = userBalanceAuditDao.findClubGoldLog(userId, limit, request.getNextPage());
        result.setData(auditLogs.stream().map(log -> {
            ClubGoldLog goldLog = new ClubGoldLog();
            goldLog.setId(log.getId());
            goldLog.setClubId(log.getClubId());
            goldLog.setGold(log.getBalanceChange());
            goldLog.setGoldBefore(log.getBalanceBefore());
            goldLog.setRoomId(String.valueOf(log.getRoomId()));
            goldLog.setCreateTime(log.getCreateTime());
            goldLog.setType(ClubGoldLog.Type.PRIVATE_USE.getValue());

            if (club.getId().equals(log.getClubId())) {
                switch (UserBalanceAuditLog.Type.valueOf(log.getType())) {
                    case ROOM_FEE:
                        goldLog.setType(ClubGoldLog.Type.ROOM_FEE.getValue());
                        break;
                    case RECHARGE:
                        goldLog.setType(ClubGoldLog.Type.RECHARGE.getValue());
                        break;
                    case AWARD:
                        goldLog.setType(ClubGoldLog.Type.AWARD.getValue());
                        break;
                }
            } else {
                switch (UserBalanceAuditLog.Type.valueOf(log.getType())) {
                    case DIAMOND_BUY_GOLD:
                        goldLog.setType(ClubGoldLog.Type.DIAMOND_BUY.getValue());
                        break;
                }
            }

            return goldLog;
        }).collect(Collectors.toList()));
        return result;
    }

    @Override
    public InvokedResult<Long> findUserChip(int userId, ClubChipReq request) {
        InvokedResult<Long> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        ClubRecordPo club = clubRecordDao.selectByPrimaryKey(request.getClubId());

        if (!club.getCreator().equals(userId + "")) {// 无操作权限
            result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
            return result;
        }
        UserDetailsInfoBo user = userService.findByRandomNum(request.getUserId());
        if (user == null) {
            result.setCode(RespCode.USER_NOT_EXIST.getCode());
            result.setMsg("用户不存在");
            return result;
        }

        ClubMemberPoExample example = new ClubMemberPoExample();
        example.or().andClubIdEqualTo(request.getClubId()).andUserIdEqualTo(user.getUserId() + "");
        List<ClubMemberPo> clubMemberPos = clubMemberPoMapper.selectByExample(example);
        if (clubMemberPos.isEmpty()) {
            result.setCode(RespCode.CLUB_MEMBERS_NOT_EXIST.getCode());
            result.setMsg("目标用户未加入您的俱乐部");
            return result;
        }

        TribeMemberPoExample tribeMemberPoExample = new TribeMemberPoExample();
        tribeMemberPoExample.or().andClubIdEqualTo(request.getClubId());
        List<TribeMemberPo> tribeMemberPos = tribeMemberPoMapper.selectByExample(tribeMemberPoExample);
        TribeMemberPo tribeMemberPo = tribeMemberPos.get(0);
        UserTribeAccountPo userTribeChip = clubDao.findUserTribeChip(user.getUserId(), tribeMemberPo.getTribeId(), tribeMemberPo.getClubId());

        result.setData(userTribeChip == null ? 0 : userTribeChip.getChips());
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        return result;
    }

    @Override
    public InvokedResult<Long> findUserMyChip(int userId, ClubChipReq request) {
        InvokedResult<Long> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        TribeMemberPoExample tribeMemberPoExample = new TribeMemberPoExample();
        tribeMemberPoExample.or().andClubIdEqualTo(request.getClubId());
        List<TribeMemberPo> tribeMemberPos = tribeMemberPoMapper.selectByExample(tribeMemberPoExample);
        if (!tribeMemberPos.isEmpty()) {
            TribeMemberPo tribeMemberPo = tribeMemberPos.get(0);
            UserTribeAccountPo userTribeChip = clubDao.findUserTribeChip(userId, tribeMemberPo.getTribeId(), tribeMemberPo.getClubId());
            result.setData(userTribeChip == null ? 0 : userTribeChip.getChips());
        } else {
            result.setData(0L);
        }
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        return result;
    }

    @Override
    public InvokedResult<Long> findClubChip(int userId, ClubChipReq request) {

        InvokedResult<Long> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        ClubRecordPo club = clubRecordDao.selectByPrimaryKey(request.getClubId());
        if (!club.getCreator().equals(userId + "")) {// 无操作权限
            result.setCode(RespCode.CLUB_PERMISSION_ERROR.getCode());
            result.setMsg(RespCode.CLUB_PERMISSION_ERROR.getDesc());
            return result;
        }

        result.setData(club.getChip());
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        return result;
    }

    @Override
    public Map<Integer, Long> getClubMembersChipTotal(Collection<Integer> clubIdList) {
        List<IClubDao.ClubMembersChipTotal> result = clubDao.getClubMembersChipTotal(clubIdList);
        return result.stream().collect(Collectors.toMap(IClubDao.ClubMembersChipTotal::getClubId, IClubDao.ClubMembersChipTotal::getChipTotal));
    }

    @Transactional
    @Override
    public InvokedResult<Object> userChipRequest(int userId, ClubChipReq request) {
        InvokedResult<Object> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());
        int clubId = request.getClubId();
        long tribeChip = request.getTribeChip();
        int type = request.getType();
        // check amount not negative or zero
        if (tribeChip <= 0) {
            log.error("Invalid amount: {}", tribeChip);
            result.setCode(RespCode.PARAM_INVALID.getCode());
            result.setMsg(RespCode.PARAM_INVALID.getDesc());
            return result;
        }
        ClubDetailPo club = clubDao.getClubDetail(String.valueOf(clubId));
        // check club exist
        if (null == club) {
            result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
            result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
            return result;
        }
        // check club status normal
        if (club.getClubStatus() != 0) {
            result.setCode(RespCode.ROOM_ENTER_CLUBCLOSED.getCode());
            result.setMsg(RespCode.ROOM_ENTER_CLUBCLOSED.getDesc());
            return result;
        }
        // check user is member
        ClubJoinedPo joinedClub = clubDao.getJoinedClubs(userId).stream()
                .filter(c -> c.getRoleType() != EClubIdentityCode.APPLICANT.getCode())
                .filter(c -> c.getId() == clubId)
                .findFirst().orElse(null);
        if (null == joinedClub) {
            result.setCode(RespCode.CLUB_MEMBERS_NOT_EXIST.getCode());
            result.setMsg(RespCode.CLUB_MEMBERS_NOT_EXIST.getDesc());
            return result;
        }
        // check tribe membership and tribe status normal
        TribeStatusPo tribeStatus = tribeDao.findTribeStatusByClubId(club.getId());
        if (tribeStatus == null || tribeStatus.getStatus() != 1 || tribeStatus.getTribeStatus() != 0) {
            result.setCode(RespCode.TRIBE_NOT_JOIN.getCode());
            result.setMsg(RespCode.TRIBE_NOT_JOIN.getDesc());
            return result;
        }
        // check user not frozen
        if (userService.isFreezeByUserId(userId)) {
            result.setCode(RespCode.ROOM_ENTER_USERFREEZE.getCode());
            result.setMsg(RespCode.ROOM_ENTER_USERFREEZE.getDesc());
            return result;
        }
        UserDetailsInfoBo userInfo = userService.findById(userId);
        String title = "申请发放联盟币";
        if (type == ClubChipReq.Type.RETRIEVE.getValue()) {
            title = "申请回收联盟币";
            // check balance not less than requested amount
            if (joinedClub.getTribeChips() < tribeChip) {
                log.error("联盟币不足, clubId: {}, tribeChip: {}, required: {}", joinedClub.getId(), joinedClub.getTribeChips(), tribeChip);
                result.setCode(RespCode.TRIBE_CHIP_NOT_ENOUGH.getCode());
                result.setMsg(RespCode.TRIBE_CHIP_NOT_ENOUGH.getDesc());
                return result;
            }
        } else if (type != ClubChipReq.Type.GRANT.getValue()) {
            log.error("Invalid type: {}", type);
            result.setCode(RespCode.PARAM_INVALID.getCode());
            result.setMsg(RespCode.PARAM_INVALID.getDesc());
            return result;
        }
        // check pending request of same type
        /*
        20241128：注释原消息发送代码 <AUTHOR>

        List<ClubMsgBo> unhandledMsg = msgService.findUnhandledUserMsgByType(userId, clubId, EMessageCode.CLUB_CHIP_REQUEST);
        for (ClubMsgBo msg : unhandledMsg) {
            String content = msg.getParams()[0];
            String[] fields = content.split(",");
            if (fields.length >= 3 && fields[2].equals(String.valueOf(type))) {
                result.setCode(RespCode.CLUB_CHIP_REQUEST_PENDING.getCode());
                result.setMsg(RespCode.CLUB_CHIP_REQUEST_PENDING.getDesc());
                return result;
            }
        }
        String content = String.join(",", new String[] {
                String.valueOf(club.getId()),
                club.getClubName(),
                String.valueOf(type),
                String.valueOf(tribeChip),
                String.valueOf(userInfo.getRandomNum()),
                userInfo.getNickName(),
                joinedClub.getTribeName()
        });
        ClubMsgBo clubMsgBo = ClubMsgBo.builder()
                .msgId(UUID.randomUUID().toString())
                .clubId(club.getId())
                .type(EMessageCode.CLUB_CHIP_REQUEST)
                .senderId(userId)
                .reciverId(club.getCreatorId())
                .header("-1")
                .params(new String[] {content, "个人申请", title})
                .build();
        msgService.saveMsg(clubMsgBo);
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
         */

        Integer queryStatus = type == ClubChipReq.Type.RETRIEVE.getValue() ?
                AppMessageConstants.BusinessStatus.RECYCLE_TRIBE_CHIP.PENDING : AppMessageConstants.BusinessStatus.SEND_TRIBE_CHIP.PENDING;

        String businessCode = type == ClubChipReq.Type.RETRIEVE.getValue() ? AppMessageConstants.BusinessCode.RECYCLE_TRIBE_CHIP : AppMessageConstants.BusinessCode.SEND_TRIBE_CHIP;

        // 保持原判断逻辑：判断玩家只能存在一条未处理的申请（回收/发放联盟币申请）
        List<Map<String, Object>> businessRecords = appBusinessMessageDao.getBusinessRecordByInitiatorIdAndStatus((long) userId, queryStatus, businessCode);


        // 从业务单中获取俱乐部信息
        AtomicBoolean isExist = new AtomicBoolean(false);

        businessRecords.forEach(businessRecord -> {
            String jsonString = MapValueUtils.getString(businessRecord, "business_data");
            // 转化为对象
            SendOrRecycleTribeChip businessData = JSONObject.parseObject(jsonString, SendOrRecycleTribeChip.class);
            // 同一个俱乐部同一个联盟只能有一个申请
            if (businessData.getClubId() == club.getId() && businessData.getTribeId() == joinedClub.getTribeId().longValue()) {
                isExist.set(true);
            }
        });


        if (isExist.get()) {
            result.setCode(RespCode.CLUB_CHIP_REQUEST_PENDING.getCode());
            result.setMsg(RespCode.CLUB_CHIP_REQUEST_PENDING.getDesc());
            return result;
        }


        // 发送申请或回收消息
        appBusinessMessageSender.notifySendOrRecycleTribeChipApply(SendOrRecycleTribeChip.builder()
                .businessCode(businessCode)
                .userId((long) userId)
                .userName(userInfo.getNickName())
                .clubId((long) club.getId())
                .clubName(club.getClubName())
                .clubOwnerId((long) club.getCreatorId())
                .tribeId((long) joinedClub.getTribeId())
                .tribeName(joinedClub.getTribeName())
                .tribeOwnerId((long)joinedClub.getTribeCreator())
                .tribeCoinAmount(tribeChip)
                .build());

        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        return result;
    }

    /**
     *
     * @param userId
     * @param msgId
     * @param handleType 1-同意 2-拒绝
     * @return
     */
    @Transactional
    @Override
    public InvokedResult<Object> handleUserChipRequest(int userId, String msgId, int handleType) {
        InvokedResult<Object> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        ClubMessagePo message = clubRecordDao.selectMessageById(msgId);
        if (message == null || message.getType() != EMessageCode.CLUB_CHIP_REQUEST.getCode() || message.getMsgStatusType() != 0) {
            log.error("Invalid message id: {}", msgId);
            result.setCode(RespCode.PARAM_INVALID.getCode());
            result.setMsg(RespCode.PARAM_INVALID.getDesc());
            return result;
        }

        // clubId,俱乐部名称,type,联盟币,用戶id
        String[] fields = message.getContent().split(",");
        if (fields.length < 4) {
            log.error("Message content is broken: {}", message.getContent());
            return result;
        }

        int clubId = Integer.parseInt(fields[0]);
        String clubName = fields[1];
        int type = Integer.parseInt(fields[2]);
        long tribeChip = Long.parseLong(fields[3]);
        String memberId = fields[4];

        if (type != 1 && type != 2) {
            log.error("Invalid type: {}", type);
            return result;
        }

        if (handleType != 1 && handleType != 2) {
            log.error("Invalid handle type: {}", handleType);
            return result;
        }

        ClubMsgBo msgBo = ClubMsgBo.builder()
                .msgId(UUID.randomUUID().toString())
                .clubId(clubId)
                .senderId(userId)
                .reciverId(Integer.parseInt(message.getSenderId()))
                .header("-1")
                .build();

        DecimalFormat fmt = new DecimalFormat("#.##");
        String remark = fmt.format(tribeChip / 100.0);
        EMessageCode msgType;

        if (handleType == 1) {
            ClubChipReq request = new ClubChipReq();
            request.setClubId(clubId);
            request.setUserId(memberId);
            if (type == 1) {
                request.setTribeChip(tribeChip);
                result = grant(userId, request);
                if (result.getCode() == 0) {
                    msgType = EMessageCode.CLUB_CHIP_REQUEST_GRANT_S;
                } else {
                    msgType = EMessageCode.CLUB_CHIP_REQUEST_GRANT_F;
                }
            } else {
                request.setTribeChip(tribeChip);
                result = retrieve(userId, request);
                if (result.getCode() == 0) {
                    msgType = EMessageCode.CLUB_CHIP_REQUEST_RETRIEVE_S;
                } else {
                    msgType = EMessageCode.CLUB_CHIP_REQUEST_RETRIEVE_F;
                }
            }
        } else {
            if (type == 1) {
                msgType = EMessageCode.CLUB_CHIP_REQUEST_GRANT_F;
            } else {
                msgType = EMessageCode.CLUB_CHIP_REQUEST_RETRIEVE_F;
            }
            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());
        }

        msgBo.setType(msgType);
        msgBo.setParams(new String[] {clubName, remark, msgType.getDesc()});

        clubRecordDao.updateClubMessageType(msgId, handleType);
        msgService.saveMsg(msgBo);
        return result;
    }

    @Transactional
    @Override
    public InvokedResult<Object> transferUserChip(int userId, ClubChipReq request) {
        InvokedResult<Object> result = new InvokedResult<>();
        // check counterparty exists
        UserDetailsInfoBo cpUserInfo = userService.findByRandomNum(request.getUserId());
        if (null == cpUserInfo) {
            result.setCode(RespCode.USER_NOT_EXIST.getCode());
            result.setMsg(RespCode.USER_NOT_EXIST.getDesc());
            return result;
        }

        LockedActuator.withLock(() -> {
            int clubId = request.getClubId();
            long tribeChip = request.getTribeChip();
            int cpUserId = cpUserInfo.getUserId();
            // check amount not negative or zero
            if (tribeChip <= 0) {
                log.error("Invalid amount: {}", tribeChip);
                result.setCode(RespCode.PARAM_INVALID.getCode());
                result.setMsg(RespCode.PARAM_INVALID.getDesc());
                return;
            }
            ClubDetailPo club = clubDao.getClubDetail(String.valueOf(clubId));
            // check club exist
            if (null == club) {
                result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                return;
            }
            // check club status normal
            if (club.getClubStatus() != 0) {
                result.setCode(RespCode.ROOM_ENTER_CLUBCLOSED.getCode());
                result.setMsg(RespCode.ROOM_ENTER_CLUBCLOSED.getDesc());
                return;
            }
            // check user is member
            ClubJoinedPo joinedClub = clubDao.getJoinedClubs(userId).stream()
                    .filter(c -> c.getRoleType() != EClubIdentityCode.APPLICANT.getCode())
                    .filter(c -> club.getId() == clubId)
                    .findFirst().orElse(null);
            if (null == joinedClub) {
                log.error("User is not club member: {}", userId);
                result.setCode(RespCode.CLUB_MEMBERS_NOT_EXIST.getCode());
                result.setMsg(RespCode.CLUB_MEMBERS_NOT_EXIST.getDesc());
                return;
            }
            // check counterparty is member
            ClubJoinedPo cpJoinedClub = clubDao.getJoinedClubs(cpUserId).stream()
                    .filter(c -> c.getRoleType() != EClubIdentityCode.APPLICANT.getCode())
                    .filter(c -> club.getId() == clubId)
                    .findFirst().orElse(null);
            if (null == cpJoinedClub) {
                log.error("Counterparty is not club member: {}", cpUserId);
                result.setCode(RespCode.CLUB_MEMBERS_NOT_EXIST.getCode());
                result.setMsg(RespCode.CLUB_MEMBERS_NOT_EXIST.getDesc());
                return;
            }
            // check tribe membership and tribe status normal
            TribeStatusPo tribeStatus = tribeDao.findTribeStatusByClubId(club.getId());
            if (tribeStatus == null || tribeStatus.getStatus() != 1 || tribeStatus.getTribeStatus() != 0) {
                result.setCode(RespCode.TRIBE_NOT_JOIN.getCode());
                result.setMsg(RespCode.TRIBE_NOT_JOIN.getDesc());
                return;
            }
            // check user not frozen
            if (userService.isFreezeByUserId(userId)) {
                log.error("User account is frozen: {}", userId);
                result.setCode(RespCode.ROOM_ENTER_USERFREEZE.getCode());
                result.setMsg(RespCode.ROOM_ENTER_USERFREEZE.getDesc());
                return;
            }
            // check counterparty not frozen
            if (userService.isFreezeByUserId(cpUserId)) {
                log.error("Counterparty account is frozen: {}", cpUserId);
                result.setCode(RespCode.ROOM_ENTER_USERFREEZE.getCode());
                result.setMsg(RespCode.ROOM_ENTER_USERFREEZE.getDesc());
                return;
            }
            // check tribe transfer min
            if (tribeStatus.getTribeId() != null) {
                long transferMin = tribeDao.getTribeTransferMin(tribeStatus.getTribeId());
                if (tribeChip < transferMin) {
                    // 低于最低转款
                    result.setData(transferMin);
                    result.setCode(RespCode.TRIBE_CHIP_LESS_TRANSFER_MIN.getCode());
                    result.setMsg(RespCode.TRIBE_CHIP_LESS_TRANSFER_MIN.getDesc());
                    return;
                }
            }

            UserTribeAccountPo userTribeChip = clubDao.findUserTribeChip(userId, tribeStatus.getTribeId(), clubId);
            UserTribeAccountPo cpTribeChip = clubDao.findUserTribeChip(cpUserId, tribeStatus.getTribeId(), clubId);
            if (userTribeChip == null || userTribeChip.getChips() < tribeChip) {
                result.setCode(RespCode.TRIBE_CHIP_NOT_ENOUGH.getCode());
                result.setMsg(RespCode.TRIBE_CHIP_NOT_ENOUGH.getDesc());
                return;
            }
            long myChipsBefore = userTribeChip.getChips();
            long cpChipsBefore = 0;
            if (cpTribeChip == null) {
                // 先初始化
                UserTribeAccountPo po = new UserTribeAccountPo();
                po.setChips(tribeChip);
                po.setTribeId(tribeStatus.getTribeId());
                po.setClubId(clubId);
                po.setUserId(cpUserId);
                po.setCreateTime(new Date());
                clubDao.insertTribeAccount(po);
            } else {
                cpChipsBefore = cpTribeChip.getChips();
                clubDao.addUserChip(tribeChip, cpUserId, tribeStatus.getTribeId(), clubId);
            }
            clubDao.addUserChip(-tribeChip, userId, tribeStatus.getTribeId(), clubId);

            String txUuid = UUID.randomUUID().toString();
            // sender log
            UserBalanceAuditLog senderLog = UserBalanceAuditLog.builder()
                    .txUuid(txUuid)
                    .userId(userId)
                    .counterpartyId(cpUserId)
                    .source(UserBalanceAuditLog.Source.API.getValue())
                    .operatorId(userId)
                    .clubId(clubId)
                    .tribeId(tribeStatus.getTribeId())
                    .type(UserBalanceAuditLog.Type.TRANSFER.getValue())
                    .balanceType(UserBalanceAuditLog.BalanceType.TRIBE_CHIP.getValue())
                    .balanceBefore(myChipsBefore)
                    .balanceChange(-tribeChip)
                    .build();
            // recipient log
            UserBalanceAuditLog recipientLog = UserBalanceAuditLog.builderFrom(senderLog)
                    .userId(cpUserId)
                    .counterpartyId(userId)
                    .balanceBefore(cpChipsBefore)
                    .balanceChange(tribeChip)
                    .build();
            // save logs
            userBalanceAuditDao.addUserBalanceAuditLog(Arrays.asList(senderLog, recipientLog));

            // 通過 res 服務異步通知前端帳戶變化
            messageSender.sendUserBalanceSyncMessage(UserBalanceSyncMessage.builder()
                    .userId(userId)
                    .clubId(clubId)
                    .balance(myChipsBefore - tribeChip)
                    .coinType(UserBalanceSyncMessage.CoinType.TRIBE_CHIP)
                    .timestamp(System.currentTimeMillis())
                    .build());
            messageSender.sendUserBalanceSyncMessage(UserBalanceSyncMessage.builder()
                    .userId(cpUserId)
                    .clubId(clubId)
                    .balance(cpChipsBefore + tribeChip)
                    .coinType(UserBalanceSyncMessage.CoinType.TRIBE_CHIP)
                    .timestamp(System.currentTimeMillis())
                    .build());


            UserDetailsInfoBo optInfo = userService.findById(userId);

            // 发送app消息
            appBusinessMessageSender.transferTribeChip(TransferTribeChip.builder()
                    .clubId((long) clubId)
                    .clubOwnerId(optInfo.getUserId().longValue())
                    .playerName(optInfo.getNickName())
                    .tribeCoinAmount(tribeChip)
                    .userId(cpUserInfo.getUserId().longValue())
                    .build());

            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());
        }, RedisDistributedLockGenerator.generate(
                RedisLockConfigCode.USER_TRANSFER_CHIP_USER, RedisLockKeyGenerator.generateUserKey(userId)
        ), RedisDistributedLockGenerator.generate(
                RedisLockConfigCode.USER_TRANSFER_CHIP_USER,
                RedisLockKeyGenerator.generateUserKey(cpUserInfo.getUserId())
        ));

        return result;
    }

    @Transactional
    @Override
    public InvokedResult<List<ClubChipTransferLog>> transferUserChipLog(int userId, ClubChipReq request) {
        InvokedResult<List<ClubChipTransferLog>> result = new InvokedResult<>();
        List<UserBalanceAuditLog> logs = userBalanceAuditDao.findTransferUserChipLog(userId, request.getClubId(), request.getLimit(), request.getNextPage());
        List<Integer> cpIdList = logs.stream().map(UserBalanceAuditLog::getCounterpartyId).collect(Collectors.toList());
        Map<Integer, UserDetailsInfoBo> cpInfoMap = userService.findByIds(cpIdList).stream()
                .collect(Collectors.toMap(UserDetailsInfoBo::getUserId, Function.identity()));
        List<ClubChipTransferLog> data = logs.stream().map(po -> {
            ClubChipTransferLog bo = new ClubChipTransferLog();
            bo.setId(po.getId());
            bo.setUserId(po.getUserId());
            bo.setTribeChip(po.getBalanceChange());
            bo.setCreateTime(po.getCreateTime());
            bo.setMemberId(po.getCounterpartyId());
            bo.setMemberName(cpInfoMap.get(po.getCounterpartyId()).getNickName());
            bo.setMemberRandomId(cpInfoMap.get(po.getCounterpartyId()).getRandomNum());
            return bo;
        }).collect(Collectors.toList());
        result.setData(data);
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        return result;
    }

    /**
     * 获取最近转账對象 (只計最近二十笔转出)
     *
     * @param userId
     * @param request
     * @return
     */
    @Override
    public InvokedResult<List<ClubChipTransferRecipient>> transferUserChipRecipients(int userId, ClubChipReq request) {
        InvokedResult<List<ClubChipTransferRecipient>> result = new InvokedResult<>();
        List<IClubDao.ClubChipTransferRecipientPo> recipients = clubDao.getTransferUserChipRecipients(userId, request.getClubId());
        result.setCode(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());
        result.setData(beanUtil.map(recipients, ClubChipTransferRecipient.class));
        return result;
    }

    /**
     * 查询用户是否在俱乐部内
     * @param userId
     * @param clubId
     * @return
     */
    @Override
    public long checkUserInClub(Integer userId, Integer clubId) {
        ClubMemberPoExample clubMemberPoExample = new ClubMemberPoExample();
        clubMemberPoExample.or().andClubIdEqualTo(clubId).andUserIdEqualTo(userId.toString());
        return clubMemberPoMapper.countByExample(clubMemberPoExample);
    }

    @Override
    public InvokedResult<Object> getClubCustomerServiceConfig(int userId, int clubId) {
        InvokedResult<Object> result = new InvokedResult<>();
        result.setCode(RespCode.FAILED.getCode());
        result.setMsg(RespCode.FAILED.getDesc());

        ClubRecordBo clubRecordBo = getClub(clubId);
        if (null == clubRecordBo) {// 俱乐部不存在
            result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
            result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
            return result;
        }
        CustomerServiceConfigPo customerServiceConfigPo = customerServiceConfigDao.getConfigByClubId(clubId);
        if (customerServiceConfigPo != null) {
            Map<String, Object> map = new HashMap<>();
            map.put("type", 0);
            if (customerServiceConfigPo.getTawkStatus() == 1) {
                UserDetailsInfoPo userDetailsInfoPo = userDetailsInfoDao.getUserById(userId);
                if (userDetailsInfoPo == null) {
                    result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                    result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                    return result;
                }
                UserTribeAccountPo userTribeAccountPo = clubDao.findUserTribeChipAndRandomNum(userId, clubId);
                if (userTribeAccountPo == null) {
                    result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                    result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                    return result;
                }
                String content = "<!--Start of Tawk.to Script-->\n" +
                        "<script type=\"text/javascript\">\n" +
                        "var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();\n" +
                        "(function(){\n" +
                        "var s1=document.createElement(\"script\"),s0=document.getElementsByTagName(\"script\")[0];\n" +
                        "s1.async=true;\n" +
                        "s1.src='https://embed.tawk.to/{property_id}/{widget_id}';\n" +
                        "s1.charset='UTF-8';\n" +
                        "s1.setAttribute('crossorigin','*');\n" +
                        "s0.parentNode.insertBefore(s1,s0);\n" +
                        "})();\n" +
                        "window.Tawk_API.onStatusChange = function(status){\n" +
                        "    if (status === 'online') {\n" +
                        "      window.Tawk_API.start();\n" +
                        "    }\n" +
                        "};\n" +
                        "window.Tawk_API.onLoad = function(){\n" +
                        "    window.Tawk_API.setAttributes({\n" +
                        "        'name'  : '{user_nickname}',\n" +
                        "        'userId': '{user_random_number}',\n" +
                        "        'tribeChipAmount': '{tribe_chip_amount}',\n" +
                        "        'clubId': '{club_random_number}',\n" +
                        "        'tribeId': '{tribe_random_number}'\n" +
                        "    }, function(error){});\n" +
                        "    window.Tawk_API.maximize();\n" +
                        "}\n" +
                        "</script>\n" +
                        "<!--End of Tawk.to Script-->";
                // {0}property_id {1}widget_id {2}user_nickname {3}user_random_number
                content = content.replace("{property_id}", customerServiceConfigPo.getPropertyId());
                content = content.replace("{widget_id}", customerServiceConfigPo.getWidgetId());
                content = content.replace("{user_nickname}", userDetailsInfoPo.getNikeName());
                content = content.replace("{user_random_number}", userDetailsInfoPo.getRandomNum());
                content = content.replace("{tribe_chip_amount}", userTribeAccountPo.getChips() + "");
                content = content.replace("{club_random_number}", userTribeAccountPo.getClubRandomNum() + "");
                content = content.replace("{tribe_random_number}", userTribeAccountPo.getTribeRandomNum() + "");
                map.put("type", 1);
                map.put("content", content);
            } else if (customerServiceConfigPo.getLinkStatus() == 1) {
                map.put("type", 2);
                map.put("content", customerServiceConfigPo.getLinkUrl());
            }
            result.setData(map);
            result.setCode(RespCode.SUCCEED.getCode());
            result.setMsg(RespCode.SUCCEED.getDesc());
        }
        return result;
    }

    @Override
    public String getClubCustomerServiceConfigTawkTo(int userId, int clubId) {

        ClubRecordBo clubRecordBo = getClub(clubId);
        if (null == clubRecordBo) {// 俱乐部不存在
            return null;
        }
        UserTribeAccountPo userTribeAccountPo = clubDao.findUserTribeChipAndRandomNum(userId, clubId);
        if (userTribeAccountPo == null) {
            return null;
        }
        CustomerServiceConfigPo customerServiceConfigPo = customerServiceConfigDao.getConfigByClubId(clubId);
        if (customerServiceConfigPo != null) {
            UserDetailsInfoPo userDetailsInfoPo = userDetailsInfoDao.getUserById(userId);
            if (userDetailsInfoPo == null) {
                return null;
            }
            String content = "<!--Start of Tawk.to Script-->\n" +
                    "<script type=\"text/javascript\">\n" +
                    "var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();\n" +
                    "(function(){\n" +
                    "var s1=document.createElement(\"script\"),s0=document.getElementsByTagName(\"script\")[0];\n" +
                    "s1.async=true;\n" +
                    "s1.src='https://embed.tawk.to/{property_id}/{widget_id}';\n" +
                    "s1.charset='UTF-8';\n" +
                    "s1.setAttribute('crossorigin','*');\n" +
                    "s0.parentNode.insertBefore(s1,s0);\n" +
                    "})();\n" +
                    "window.Tawk_API.onStatusChange = function(status){\n" +
                    "    if (status === 'online') {\n" +
                    "      window.Tawk_API.start();\n" +
                    "    }\n" +
                    "};\n" +
                    "window.Tawk_API.onLoad = function(){\n" +
                    "    window.Tawk_API.setAttributes({\n" +
                    "        'name'  : '{user_nickname}',\n" +
                    "        'userId': '{user_random_number}',\n" +
                    "        'tribeChipAmount': '{tribe_chip_amount}',\n" +
                    "        'clubId': '{club_random_number}',\n" +
                    "        'tribeId': '{tribe_random_number}'\n" +
                    "    }, function(error){});\n" +
                    "    window.Tawk_API.maximize();\n" +
                    "}\n" +
                    "</script>\n" +
                    "<!--End of Tawk.to Script-->";
            content = content.replace("{property_id}", customerServiceConfigPo.getPropertyId());
            content = content.replace("{widget_id}", customerServiceConfigPo.getWidgetId());
            content = content.replace("{user_nickname}", userDetailsInfoPo.getNikeName());
            content = content.replace("{user_random_number}", userDetailsInfoPo.getRandomNum());
            content = content.replace("{tribe_chip_amount}", userTribeAccountPo.getChips() + "");
            content = content.replace("{club_random_number}", userTribeAccountPo.getClubRandomNum() + "");
            content = content.replace("{tribe_random_number}", userTribeAccountPo.getTribeRandomNum() + "");
            return content;
        }
        return null;
    }

    private Long getClubMembersChipTotal(int clubId) {
        List<IClubDao.ClubMembersChipTotal> result = clubDao.getClubMembersChipTotal(Collections.singletonList(clubId));
        return result.stream().filter(t -> t.getClubId() == clubId)
                .findFirst()
                .map(IClubDao.ClubMembersChipTotal::getChipTotal)
                .orElse(0L);
    }

    public RespCode checkBlackList(int clubId, int userId, String ip, String deviceCode){
        List<Integer> tribeIds = new ArrayList<>();
        //如有被聯盟加入黑名單
        TribeMemberPoExample tribeMemberPoExample = new TribeMemberPoExample();
        tribeMemberPoExample.or().andClubIdEqualTo(clubId);
        List<TribeMemberPo> tribeMemberPos = tribeMemberPoMapper.selectByExample(tribeMemberPoExample);
        if (tribeMemberPos != null && !tribeMemberPos.isEmpty()) {
            tribeIds = tribeMemberPos.stream().map(TribeMemberPo::getTribeId).collect(Collectors.toList());
        }

        // 判断用户是否加入黑名單
        ClubBlacklistExample clubBlacklistExample = new ClubBlacklistExample();
        clubBlacklistExample.or().andClubIdEqualTo(clubId)
                    .andTypeEqualTo((byte)0)
                    .andContenttypeEqualTo((byte)0)
                    .andUserIdEqualTo(userId);
        List<ClubBlacklist> clubBlacklist = clubBlacklistMapper.selectByExample(clubBlacklistExample);
        if (clubBlacklist != null && !clubBlacklist.isEmpty()) {
            return RespCode.CLUB_BLACKLISTED_USER;
        }
        if(tribeIds != null && !tribeIds.isEmpty()){
            ClubBlacklistExample clubBlacklistExampleTribe = new ClubBlacklistExample();
            clubBlacklistExampleTribe.or().andTribeIdIn(tribeIds)
                        .andTypeEqualTo((byte)1)
                        .andContenttypeEqualTo((byte)0)
                        .andUserIdEqualTo(userId);
            List<ClubBlacklist> clubBlacklistTribe = clubBlacklistMapper.selectByExample(clubBlacklistExampleTribe);
            if(clubBlacklistTribe != null && !clubBlacklistTribe.isEmpty()){
                return RespCode.TRIBE_BLACKLISTED_USER;
            }
        }

        // 判断IP是否加入黑名單
        if(ip != null && !ip.isEmpty()){
            ClubBlacklistExample clubBlacklistExampleIp = new ClubBlacklistExample();
            clubBlacklistExampleIp.or().andClubIdEqualTo(clubId)
                    .andTypeEqualTo((byte)0)
                    .andContenttypeEqualTo((byte)1)
                    .andIpEqualTo(ip);
            List<ClubBlacklist> clubBlacklistip = clubBlacklistMapper.selectByExample(clubBlacklistExampleIp);
            if (clubBlacklistip != null && !clubBlacklistip.isEmpty()) {
                return RespCode.CLUB_BLACKLISTED_IP;
            }
            if(tribeIds != null && !tribeIds.isEmpty()){
                ClubBlacklistExample clubBlacklistExampleTribeip = new ClubBlacklistExample();
                clubBlacklistExampleTribeip.or().andTribeIdIn(tribeIds)
                            .andTypeEqualTo((byte)1)
                            .andContenttypeEqualTo((byte)1)
                            .andIpEqualTo(ip);
                List<ClubBlacklist> clubBlacklistTribeip = clubBlacklistMapper.selectByExample(clubBlacklistExampleTribeip);
                if(clubBlacklistTribeip != null && !clubBlacklistTribeip.isEmpty()){
                    return RespCode.TRIBE_BLACKLISTED_IP;
                }
            }
        }

        // 判断裝置是否加入黑名單
        if(deviceCode != null && !deviceCode.isEmpty()){
            ClubBlacklistExample clubBlacklistExampleDc = new ClubBlacklistExample();
            clubBlacklistExampleDc.or().andClubIdEqualTo(clubId)
                    .andTypeEqualTo((byte)0)
                    .andContenttypeEqualTo((byte)2)
                    .andDeviceCodeEqualTo(deviceCode);
            List<ClubBlacklist> clubBlacklistdc = clubBlacklistMapper.selectByExample(clubBlacklistExampleDc);
            if (clubBlacklistdc != null && !clubBlacklistdc.isEmpty()) {
                return RespCode.CLUB_BLACKLISTED_DEVICE;
            }
            if(tribeIds != null && !tribeIds.isEmpty()){
                ClubBlacklistExample clubBlacklistExampleTribedc = new ClubBlacklistExample();
                clubBlacklistExampleTribedc.or().andTribeIdIn(tribeIds)
                            .andTypeEqualTo((byte)1)
                            .andContenttypeEqualTo((byte)2)
                            .andDeviceCodeEqualTo(deviceCode);
                List<ClubBlacklist> clubBlacklistTribedc = clubBlacklistMapper.selectByExample(clubBlacklistExampleTribedc);
                if(clubBlacklistTribedc != null && !clubBlacklistTribedc.isEmpty()){
                    return RespCode.TRIBE_BLACKLISTED_DEVICE;
                }
            }
        }

        return null;
    }
}
