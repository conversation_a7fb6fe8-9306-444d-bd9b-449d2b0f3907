package com.dzpk.crazypoker.club.repositories.mysql;

import com.dzpk.crazypoker.club.api.vo.ClubChipLog;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.model.ClubFundHistoryPo;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.model.ClubMemberPo;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.model.ClubRecordPo;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.model.PayChannelPo;
import com.dzpk.crazypoker.club.repositories.mysql.model.*;
import lombok.Data;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Created by jayce on 2019/2/25
 *
 * <AUTHOR>
 */
@Repository
public interface IClubDao {

    // 查询是否有重名的社区
    @Select("select count(1) from club_record where name=#{name}")
    int checkNameInClub(@Param(value = "name") String name);

    @Select("select count(*) from club_members where user_id=#{userId}")
    int getClubNums(@Param(value = "userId") int userId);

    @Select("SELECT COUNT(*) FROM message_club_request WHERE user_id=#{userId} and type = 1")
    int getCreateClubRequestNums(@Param(value = "userId")int userId);

    @Delete("delete from message_club_request where user_id=#{userId}")
    int deleteClubRequest(@Param(value = "userId") int userId);

    @Insert("INSERT INTO message_club_request (club_id,user_id,msg_id,param,type,create_time)" +
            " VALUES(#{clubId},#{userId},#{msgId},#{param},#{type},NOW())")
    int addCreateClubRequest(@Param("clubId")String clubId,@Param("userId")String userId,@Param("msgId")String msgId,
                             @Param("param")String param,@Param("type")Integer type);

    // 获取俱乐部成员的信息
    @Select("select user_id as userId, type, initial_credit as initialCredit, "
            + "current_credit as currentCredit, credit_status as creditStatus, clear_request_status as clearRequestStatus, "
            + "push_status as pushStatus ,ratio ,ratiostatus, transfer_coin_type as transferCoinType from club_members where club_id=#{clubId} and user_id=#{userId}")
    ClubMemberPo getClubMemberInfo(@Param(value = "clubId") int clubId, @Param(value = "userId") int userId);

    @Select("SELECT cr.id as id,cr.random_id as randomId,cr.name as name,cr.header as header,cr.club_members as clubMembers," +
            "cr.area_id as areaId,cr.upper_limit as upperLimit, cr.use_custom as useCustom, cr.custom_url as customUrl " +
            "FROM club_hot_list as chl,club_record as cr WHERE chl.club_id = cr.id " +
            " AND chl.status = 1 ORDER BY chl.weight desc,cr.club_members desc limit #{limitSize}")
    List<ClubRecordPo> getHotClubs(@Param(value = "limitSize")int limitSize);

    // 搜索社区
    @Select("select id, random_id as randomId, name as clubName, header as clubHead, creator, "
            + "upper_limit as upperLimit, club_members as clubMembers, area_id as areaId, use_custom as useCustom, custom_url as customUrl "
            + "from club_record where random_id=#{key} and name not like #{name}")
    List<ClubSearchPo> searchClubByKey(@Param(value = "key")String key,@Param(value = "name")String name);

    // 搜索某个地区的俱乐部
    @Select("select id, random_id as randomId, name as clubName, header as clubHead, creator, "
            + "upper_limit as upperLimit, club_members as clubMembers,area_id as areaId, use_custom as useCustom, custom_url as customUrl "
            + "from club_record where area_id=#{areaId} and name not like #{name}")
    List<ClubSearchPo> searchClubByArea(@Param(value = "areaId")String areaId,@Param(value = "name")String name);

    // 更新社区成员数
    @Update("update club_record set club_members=(case when cast(club_members as signed)+#{num}>=0 then club_members+#{num} else 0 end) where id=#{clubId}")
    int updateClubMembers(@Param(value = "clubId") int clubId, @Param(value = "num") int num);

    // 获取玩家加入的社区列表
    @Select("select club.id, club.random_id as randomId, club.name as clubName, club.header as clubHead,club.creator, tr.creator as tribeCreator, club.use_custom as useCustom, club.custom_url as customUrl,"
            + "club.upper_limit as upperLimit, club.club_members as clubMembers, members.type as roleType, "
            + "club.create_time as time,club.area_id as areaId,integral ,club.officail_club as officialClub, "
            + "tm.tribe_id as tribeId, tr.tribe_name as tribeName, tr.random_id as tribeShowId, ifnull(uta.chips, 0) as tribeChips "
            + "from club_members members inner join club_record club on members.club_id=club.id "
            + "left join tribe_members tm on tm.club_id = club.id "
            + "left join tribe_record tr on tr.id = tm.tribe_id "
            + "left join user_tribe_account uta on uta.user_id = members.user_id and uta.tribe_id = tm.tribe_id and uta.club_id = members.club_id "
            + "where members.user_id=#{userId} and club.club_status = 0 "
            + "order by UNIX_TIMESTAMP(members.create_time) desc")
    List<ClubJoinedPo> getJoinedClubs(@Param(value = "userId") int userId);

    /**
     * @param clubId
     * @return
     */

    @Select({"SELECT id,name as clubName,random_id as randomId,creator as creatorId,club_members as clubMembers,upper_limit as membersLimit, header as clubHeader,area_id as areaId,description,fund,frozen, ",
            " trans_type as transType, pay_channel_flag as payChannelFlag, mod_trans_type_time as modTransTypeTime, club_status as clubStatus, cr.chip, recharge_fee_rate as rechargeFeeRate, create_time as createTime, udi.nike_name as creator, cr.MODIFY_NAME_TIMES as modifyNameTimes, " +
            " cr.use_custom as useCustom, cr.custom_url as customUrl ",
            " FROM club_record cr INNER JOIN user_details_info udi on udi.user_id = cr.creator WHERE id =#{clubId} "})
    ClubDetailPo getClubDetail(@Param(value = "clubId") String clubId);

    // 获取俱乐部成员列表
    @Select("select user_id as userId, type from club_members as cm" +
            " where club_id=#{clubId} order by type asc,UNIX_TIMESTAMP(cm.create_time)")
    List<ClubMemberListPo> getClubMembers(@Param(value = "clubId") int clubId);

    @Select({"select cm.user_id as userId, cm.type, ifnull(uga.gold, 0) as gold, ifnull(uta.chips, 0) as tribeChip from club_members cm",
            "left join tribe_members tm on tm.club_id = cm.club_id",
            "left join user_gold_account uga on uga.user_id = cm.user_id",
            "left join user_tribe_account uta on uta.user_id = cm.user_id and uta.tribe_id = tm.tribe_id and uta.club_id = cm.club_id",
            "where cm.club_id=#{clubId} and cm.type != 5 order by cm.type asc, UNIX_TIMESTAMP(cm.create_time)"})
    List<ClubMemberDetailListPo> getClubMemberDetails(@Param(value = "clubId") int clubId);

    @Select("SELECT cr.id,cr.random_id as randomId,cr.name,cr.header,cr.description,cr.creator,cr.club_members as clubMembers," +
            "cr.upper_limit as upperLimit,cr.create_time as createTime,cr.only_chief as onlyChief,cr.area_id as areaId," +
            "cr.club_status as clubStatus,cr.fund,cr.initial_credit as initialCredit,cr.credit_status as creditStatus," +
            "cr.total_insure as totalInsure,cr.tribe_status as tribeStatus,cr.tribe_count as tribeCount," +
            "cr.ratio,cr.ratio_time as ratioTime,cr.officail_club as officailClub,cr.frozen,cr.profit, cr.use_custom as useCustom, cr.custom_url as customUrl " +
            "FROM club_members as cm," +
            "club_record as cr WHERE cm.user_id = #{userId} and cm.club_id = cr.id AND cm.type <> 5")
    List<ClubRecordPo> getUserClub(@Param(value = "userId")String userId);

    // 获取俱乐部成员列表
    @Select("select user_id as userId, type from club_members as cm" +
            " where club_id=#{clubId} order by type asc,UNIX_TIMESTAMP(cm.create_time) limit #{size}")
    List<ClubMemberListPo> getLimitClubMemebers(@Param(value = "clubId") int clubId,
                                                       @Param(value = "size") int size);
    // 获取房間俱乐部
    @Select("select club_id from club_room_extend where room_id= #{roomId}")
    Integer getClubByRoom(@Param(value = "roomId") int roomId);

    // 获取俱乐部房间数 (包括聯盟房)
    @Select("select count(1) from club_room_extend extend inner join room_search room on extend.room_id=room.room_id" +
            " and room.status!=0 and room.club_room_type=1 where extend.club_id=#{clubId} " +
            "  or exists(select 1 from tribe_record tr inner join tribe_members tm on tm.tribe_id = tr.id " +
            "  where tr.status=0 and tm.status=1 and tr.id = extend.tribe_id and room.tribe_room_type=1 and tm.club_id=#{clubId})")
    Integer getClubRoomCount(@Param(value = "clubId") int clubId);

    // 获取俱乐部積分
    @Select("select integral from club_members where user_id=#{userId} and club_id=#{clubId}")
    Integer getUserClubIntegral(@Param(value = "clubId") int clubId,@Param(value = "userId") int userId);

    // 更新俱乐部積分
    @Update("update club_members set integral=integral+#{integral} where user_id=#{userId} and club_id=#{clubId}")
    int addUserClubIntegral(@Param(value = "clubId") int clubId,
                            @Param(value = "userId") int userId,
                            @Param(value = "integral") int integral);

    // 获取社区的的基金历史记录（分页）
    @Select("select id, club_id as clubId, operator_name as operatorName, "
            + "user_id as userId, user_name as userName, amount, create_time as createTime, "
            + "type, req_msg_id as reqMsgId ,operator_id as operatorId from club_fund_history where club_id=#{clubId} "
            + "and UNIX_TIMESTAMP(create_time) > #{time} and UNIX_TIMESTAMP(create_time) >#{minTime} and type > 0 "
            + "order by create_time desc limit #{size}")
    List<ClubFundHistoryPo> getFundListGtByTime(@Param(value = "clubId") int clubId,
                                                     @Param(value = "time") Long time,
                                                     @Param(value ="minTime")long minTime,
                                                     @Param(value = "size") int size);

    // 获取社区的的基金历史记录（分页）
    @Select("select id, club_id as clubId, operator_name as operatorName, "
            + "user_id as userId, user_name as userName, amount, create_time as createTime, "
            + "type, req_msg_id as reqMsgId,operator_id as operatorId from club_fund_history where club_id=#{clubId} "
            + "and UNIX_TIMESTAMP(create_time) < #{time} and UNIX_TIMESTAMP(create_time) >#{minTime} and type > 0 "
            + "order by create_time desc limit #{size}")
    List<ClubFundHistoryPo> getFundListLtByTime(@Param(value = "clubId") int clubId,
                                                     @Param(value = "time") Long time,
                                                     @Param(value ="minTime")long minTime,
                                                     @Param(value = "size") int size);

    // 获取社区的的基金历史记录（分页）
    @Select("select id, club_id as clubId, operator_name as operatorName,user_id as userId, user_name as userName, amount, " +
            "create_time as createTime,type, req_msg_id as reqMsgId,operator_id as operatorId from club_fund_history where " +
            "club_id= #{clubId} and UNIX_TIMESTAMP(create_time) >#{minTime} and type > 0  order by create_time desc limit #{size}")
    List<ClubFundHistoryPo> getFundList(@Param(value = "clubId") int clubId,
                                             @Param(value ="minTime")long minTime,
                                             @Param(value = "size") int size);

    // 更新社区基金总额
    @Update("update club_record set fund=fund+#{fund} where id=#{clubId}")
    int updateClubFund(@Param(value = "clubId") int clubId, @Param(value = "fund") int fund);

    // 新建社区基金历史记录
    @Insert("insert into club_fund_history (club_id, operator_name, user_id, "
            + "user_name, amount, type, req_msg_id ,operator_id) values (#{clubId}, #{operatorName}, "
            + "#{userId}, #{userName}, #{amount}, #{type}, #{reqMsgId},#{operatorId})")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int createFundHistory(ClubFundHistoryPo history);

    @Select("select count(*) from club_record where id=#{clubId} and creator=#{userId}")
    int getClubByClubIdAndUserId(@Param(value = "userId")int userId,@Param(value = "clubId")int clubId);

    @Update("update club_record set name =#{name}, MODIFY_NAME_TIMES = MODIFY_NAME_TIMES + 1 where id=#{clubId}")
    int updateClubName(@Param(value = "clubId")int clubId,@Param(value = "name")String name);

    @Update("update club_record set description =#{desc} where id=#{clubId}")
    int updateClubDesc(@Param(value = "clubId")int clubId,@Param(value = "desc")String desc);

    @Update("update club_record set header =#{headFileName}, use_custom = 0 where id=#{clubId}")
    int updateClubHead(@Param(value = "clubId")int clubId,@Param(value = "headFileName")String headFileName);

    @Update("update club_record set custom_url =#{headCustomUrl}, use_custom = 1 where id=#{clubId}")
    int updateClubCustomHead(@Param(value = "clubId")int clubId,@Param(value = "headCustomUrl")String headCustomUrl);

    @Update("update club_record set header =#{head}, use_custom = 0, custom_url = null where id=#{clubId}")
    int setClubHead(@Param(value = "clubId")int clubId,@Param(value = "head")String head);

    @Update("INSERT INTO message_club_record (msg_id,club_id,sender_id,reciver_id,header,title,content,remark,type,msg_status,create_time)" +
            " VALUES(#{msgId},#{clubId},#{senderId},#{reciverId},#{header},#{title},#{content},#{remark},#{type},#{msgStatus},NOW())")
    int createClubMessageRecord(@Param("msgId")String msgId,@Param("clubId")String clubId,
                                @Param("senderId")String senderId,@Param("reciverId")String reciverId,
                                @Param("header")String header,@Param("title")String title,
                                @Param("content")String content,@Param("remark")String remark,
                                @Param("type")Integer type,@Param("msgStatus")Integer msgStatus);


    @Update("INSERT INTO message_money_record (msg_id,sender_id,reciver_id,header,title,content,remark,type,msg_status,create_time)" +
            " VALUES(#{msgId},#{senderId},#{reciverId},#{header},#{title},#{content},#{remark},#{type},#{msgStatus},NOW())")
    int createClubGiveFundMessageRecord(@Param("msgId")String msgId,
                                @Param("senderId")String senderId,@Param("reciverId")String reciverId,
                                @Param("header")String header,@Param("title")String title,
                                @Param("content")String content,@Param("remark")String remark,
                                @Param("type")Integer type,@Param("msgStatus")Integer msgStatus);

    @Update("UPDATE message_unread SET club_num = club_num+#{num},club_msg = #{msg} WHERE user_id=#{userId}")
    Integer updateClubUnreadMsg(@Param(value = "num")Integer num,@Param(value = "msg")String msg,@Param(value = "userId")Integer userId);

    @Update("UPDATE message_unread SET money_num = money_num+#{num},money_msg = #{msg} WHERE user_id=#{userId}")
    Integer updateMoneyUnreadMsg(@Param(value = "num")Integer num,@Param(value = "msg")String msg,@Param(value = "userId")Integer userId);


    @Select("SELECT p.* FROM  pay_channel p  JOIN  club_pay_channel cp ON cp.channel_id =  p.id  WHERE cp.club_id = #{clubId} and p.status = 0")
    @ResultMap("com.dzpk.crazypoker.club.repositories.mysql.autogen.mapper.PayChannelPoMapper.BaseResultMap")
    PayChannelPo selectMyPayChannelPoByClubIdAndIsNormal(Integer clubId);


    @Select("SELECT p.payment_code as paymentCode, e.config as  escrowConfig, p.priority  FROM `pay_channel_escrow_payment` p  JOIN escrow_support e ON p.`payment_code` = e.code AND e.`status` = 1 AND p.`status` = 1 AND p.pay_channel_id = #{payChannelId} and `type` = #{type}")
    List<ClubChannelPaymentPo> selectBestPaymentCodeByPayChannelId(@Param("payChannelId") Integer payChannelId, @Param("type") Integer type);

    /**
     * 查询用户是管理员或者创建者的同盟
     * @param userId
     * @return
     */
    @Select("select tribe.id from club_members club, tribe_record tribe " +
            "where club.club_id = tribe.club_id  and club.type in (1,4)  and club.user_id= #{userId}")
    List<Integer> getManageTribe(int userId);

    @Update("update user_tribe_account set chips = chips + #{chip} where user_id = #{userId} and tribe_id = #{tribeId} and club_id = #{clubId}")
    void addUserChip(@Param("chip") long chip,@Param("userId") int userId,@Param("tribeId") int tribeId,@Param("clubId") int clubId);

    @Select("select user_id userId,tribe_id tribeId,club_id clubId, chips from user_tribe_account where user_id = #{userId} and tribe_id = #{tribeId} and club_id = #{clubId}")
    UserTribeAccountPo findUserTribeChip(@Param("userId") int userId,@Param("tribeId") int tribeId,@Param("clubId") int clubId);

    @Insert("insert into user_tribe_account (user_id,tribe_id,club_id,chips,create_time) values (#{userId},#{tribeId},#{clubId},#{chips},#{createTime})")
    void insertTribeAccount(UserTribeAccountPo po);

    /**
     * 获取俱乐部的充值费率
     * @param clubId
     * @return
     */
    @Select("select recharge_fee_rate from club_record where id=#{clubId}")
    Integer getRechargeFeeRate(@Param(value = "clubId") int clubId);

    /**
     * 获取俱乐部的基金餘額
     * @param clubId
     * @return
     */
    @Select("select fund from club_record where id=#{clubId}")
    Integer getClubFund(@Param(value = "clubId") int clubId);

    /**
     * 获取俱乐部的筹码餘額
     * @param clubId
     * @return
     */
    @Select("select chip from club_record where id=#{clubId}")
    Long getTribeChip(int clubId);

    /**
     * 获取俱乐部的保险賠付限额
     * @param clubId
     * @return
     */
    @Select("select insurance_loss_limit from club_record where id=#{clubId}")
    Integer getClubInsuranceLossLimit(@Param(value = "clubId") int clubId);

    @Insert("insert into club_chip_log (club_id,type,chip,chip_before,create_time,other_id,room_id,user_random_id,tribe_random_id) values ( #{clubId},#{type},#{chip},#{chipBefore},#{createTime},#{otherId},#{roomId},#{userRandomId},#{tribeRandomId})")
    void insertLog(ClubChipLog log);

    @Select({"<script> select club_id as clubId, sum(chips) as chipTotal from user_tribe_account uta",
            "where club_id in <foreach collection='clubIdList' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "group by club_id </script>"})
    List<ClubMembersChipTotal> getClubMembersChipTotal(@Param(value = "clubIdList") Collection<Integer> clubIdList);

    @Select({"select counterparty_id as memberId, udi.random_num as memberRandomId, udi.nike_name as memberName, udi.head as memberHead, udi.use_custom as useCustom, udi.custom_url as customUrl",
            "from (select * from user_balance_audit_log ubal",
            " where ubal.user_id = #{userId} and ubal.club_id = #{clubId} and ubal.type = 13 and ubal.balance_type = 2 and ubal.balance_change < 0",
            " order by ubal.create_time desc limit 20) ubal",
            "inner join user_details_info udi on udi.user_id = ubal.counterparty_id",
            "group by ubal.counterparty_id, udi.random_num, udi.nike_name",
            "order by max(ubal.create_time) desc"})
    List<ClubChipTransferRecipientPo> getTransferUserChipRecipients(int userId, int clubId);

    @Select({
            "SELECT COUNT(*) FROM club_record cr ",
            "WHERE cr.id=#{clubId} AND cr.creator = #{userId} "
    })
    int checkClubPermission(@Param(value = "userId")int userId,@Param(value = "clubId")int clubId);

    /**
     * 获取俱乐部成员的状态
     * @param clubId
     * @param userId
     * @return
     */
    @Select("select user_status from club_members where club_id=#{clubId} and user_id = #{userId}")
    Integer getClubUserStatus(int clubId, int userId);

    @Select({
            "select cr.chip + ifnull(m.member_chips, 0) as total_supply from club_record cr ",
            "left join (select club_id, sum(chips) as member_chips from user_tribe_account group by club_id) m on m.club_id = cr.id",
            "where cr.id = #{clubId}"
    })
    Long getTribeChipTotalSupply(int clubId);

    @Select({
            "select ifnull(sum(amount),0) from (",
            "select rs.room_id, rs.name, ubal.user_id, ubal.club_id, -sum(ubal.balance_change) amount, sum(if(ubal.type = 3, 1, 0)) bringout",
            "from room_search rs join user_balance_audit_log ubal on ubal.room_id = rs.room_id",
            "where rs.status >= 3 and ubal.type in (2,3) and ubal.balance_type = 2",
            "and ubal.id > (select ifnull(max(id),0) from user_balance_audit_log y where y.user_id=ubal.user_id and y.room_id=ubal.room_id and y.type=3)",
            "group by rs.room_id, rs.name, ubal.user_id, ubal.club_id",
            "having bringout = 0) x where club_id = #{clubId}",
    })
    Integer getTribeChipUnsettled(int clubId);

    @Select("select total_amount - insurance_loss_limit from club_record where id=#{clubId}")
    Long getTribeChipMinTotalSupply(int clubId);

    @Select({"select utc.user_id userId,utc.tribe_id tribeId,utc.club_id clubId, utc.chips chips, tr.random_id tribeRandomNum, cr.random_id clubRandomNum ",
            "from user_tribe_account utc",
            "left join tribe_record tr on tr.id = utc.tribe_id",
            "left join club_record cr on cr.id = utc.club_id",
            "left join tribe_members tm on tm.club_id = utc.club_id",
            "where utc.user_id = #{userId} and utc.tribe_id = tm.tribe_id and utc.club_id = #{clubId}"})
    UserTribeAccountPo findUserTribeChipAndRandomNum(@Param("userId") int userId,@Param("clubId") int clubId);

    @Data
    class ClubMembersChipTotal {
        private Integer clubId;
        private Long chipTotal;
    }

    @Data
    class ClubChipTransferRecipientPo {
        private int memberId;

        private String memberRandomId;

        private String memberName;

        private String memberHead;

        private Integer useCustom;

        private String customUrl;
    }
}
