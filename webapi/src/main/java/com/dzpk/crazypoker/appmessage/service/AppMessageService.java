package com.dzpk.crazypoker.appmessage.service;


import com.dzpk.crazypoker.appmessage.bean.AppMessageBean;
import com.dzpk.crazypoker.common.web.resp.CommonResponse;

/**
 * AppMessageService
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
public interface AppMessageService {

    /**
     * 处理审批
     * @param param 审批信息
     * @return CommonResponse<Object>
     */
    CommonResponse<Object> handleApply(AppMessageBean param);

}
