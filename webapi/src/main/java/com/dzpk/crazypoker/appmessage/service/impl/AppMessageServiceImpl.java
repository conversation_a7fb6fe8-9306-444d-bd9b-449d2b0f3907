package com.dzpk.crazypoker.appmessage.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.dzpk.crazypoker.appmessage.bean.AppMessageBean;
import com.dzpk.crazypoker.appmessage.dao.AppBusinessMessageDao;
import com.dzpk.crazypoker.appmessage.send.AppBusinessMessageSender;
import com.dzpk.crazypoker.appmessage.send.bean.JoinClub;
import com.dzpk.crazypoker.appmessage.send.bean.JoinTribe;
import com.dzpk.crazypoker.appmessage.send.bean.SendOrRecycleTribeChip;
import com.dzpk.crazypoker.appmessage.service.AppMessageService;
import com.dzpk.crazypoker.appmessage.utils.AppMessageConstants;
import com.dzpk.crazypoker.appmessage.utils.MapValueUtils;
import com.dzpk.crazypoker.club.api.req.ClubChipReq;
import com.dzpk.crazypoker.club.constant.EClubIdentityCode;
import com.dzpk.crazypoker.club.repositories.mysql.IClubDao;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.mapper.ClubMemberPoMapper;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.mapper.ClubRecordPoMapper;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.model.ClubMemberPo;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.model.ClubRecordPo;
import com.dzpk.crazypoker.club.repositories.mysql.autogen.model.ClubRecordPoExample;
import com.dzpk.crazypoker.club.service.IClubService;
import com.dzpk.crazypoker.common.constant.RespCode;
import com.dzpk.crazypoker.common.rabbitmq.constant.EMessageCode;
import com.dzpk.crazypoker.common.service.InvokedResult;
import com.dzpk.crazypoker.common.web.resp.CommonResponse;
import com.dzpk.crazypoker.tribe.constant.ETribeMembersType;
import com.dzpk.crazypoker.tribe.service.ITribeService;
import com.dzpk.crazypoker.user.service.bo.UserDetailsInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;

/**
 * AppMessageServiceImpl
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@Slf4j
@Service
public class AppMessageServiceImpl implements AppMessageService {


    @Resource
    private ClubRecordPoMapper clubRecordDao;

    @Resource
    AppBusinessMessageDao appBusinessMessageDao;

    @Resource
    private ClubMemberPoMapper clubMemberMapper;

    @Autowired
    private IClubDao clubDao;

    @Autowired
    private ITribeService tribeService;

    @Autowired
    private AppBusinessMessageSender appBusinessMessageSender;

    @Autowired
    private IClubService clubService;


    @Override
    public CommonResponse<Object> handleApply(AppMessageBean param) {

        // 默认成功
        CommonResponse<Object> result = new CommonResponse<>();
        result.setStatus(RespCode.SUCCEED.getCode());
        result.setMsg(RespCode.SUCCEED.getDesc());

        // 先查询整个消息与业务记录的详情
        Map<String, Object> detail = appBusinessMessageDao.getBusinessMessageDetail((long) param.getUserId(), param.getBusinessRecordId());

        if (detail == null) {
            log.error("消息记录不存在，businessRecordId:{}", param.getBusinessRecordId());
            result.setStatus(RespCode.FAILED.getCode());
            result.setMsg("消息记录不存在");
            return result;
        }

        InvokedResult<Object> message = null;

        // 获取业务编码
        String businessCode = MapValueUtils.getString(detail, "business_code");

        // 根据业务编码判断处理何种业务
        switch (businessCode) {
            case AppMessageConstants.BusinessCode.CLUB_JOIN_APPLY:
                // 俱乐部加入申请
                message = handleClubJoinApply(detail, param.getBusinessStatus());
                break;
            case AppMessageConstants.BusinessCode.TRIBE_JOIN_APPLY:
                // 联盟加入申请
                message = handleTribeJoinApply(detail, param.getBusinessStatus());
                break;
            case AppMessageConstants.BusinessCode.RECYCLE_TRIBE_CHIP:
                // 回收联盟币
                message = handleRecycleTribeChip(detail, param.getBusinessStatus());
                break;
            case AppMessageConstants.BusinessCode.SEND_TRIBE_CHIP:
                // 发放联盟币
                message = handleSendTribeChip(detail, param.getBusinessStatus());
                break;
            default:
                log.error("未知的业务编码，businessCode:{}", businessCode);
                result.setStatus(RespCode.FAILED.getCode());
                result.setMsg("未知的业务编码");
                return result;
        }

        if (message != null) {
            result.setStatus(message.getCode());
            result.setMsg(message.getMsg());
        }

        return result;
    }


    /**
     * 处理俱乐部加入申请
     * @param detail 业务消息详情
     * @param businessStatus 业务状态
     * @return 错误消息
     */
    private InvokedResult<Object> handleClubJoinApply(Map<String, Object> detail, Integer businessStatus) {
        InvokedResult<Object> result = new InvokedResult<>();
        // 获取工单参数
        String businessDataString = MapValueUtils.getString(detail, "business_data");

        // 转换成JSON
        JSONObject businessData = null;
        try {
            businessData = JSONObject.parseObject(businessDataString);
        } catch (Exception e) {
            log.error("解析业务数据失败，businessDataString:{}", businessDataString);
            result.setCode(RespCode.FAILED.getCode());
            result.setMsg("解析业务数据失败");
            return result;
        }

        Integer userId = businessData.getInteger("userId");
        String playerName = businessData.getString("playerName");
        Integer clubId = businessData.getInteger("clubId");
        String clubName = businessData.getString("clubName");
        Integer clubOwnerId = businessData.getInteger("clubOwnerId");

        switch (businessStatus) {
            // 同意
            case AppMessageConstants.BusinessStatus.CLUB_JOIN_APPLY.AGREE:
                // 1. 查询俱乐部
                ClubRecordPoExample example = new ClubRecordPoExample();
                example.or().andIdEqualTo(clubId);
                List<ClubRecordPo> clubList = clubRecordDao.selectByExample(example);
                if (CollectionUtils.isEmpty(clubList)) {
                    // 1.1. 俱乐部不存在
                    result.setCode(RespCode.CLUB_NOT_EXIST.getCode());
                    result.setMsg(RespCode.CLUB_NOT_EXIST.getDesc());
                    return result;
                }
                ClubRecordPo club = clubList.get(0);
                // 2. 先判断是否满员
                if (club.getUpperLimit() <= club.getClubMembers()) {
                    // 2.1. 俱乐部已满员
                    result.setCode(EMessageCode.CLUB_MEMBER_LIMIT_NOTIFY.getCode());
                    result.setMsg(EMessageCode.CLUB_MEMBER_LIMIT_NOTIFY.getDesc());
                    return result;
                }
                // 3. update 业务状态
                appBusinessMessageDao.updateBusinessStatus(MapValueUtils.getLong(detail, "record_id"), AppMessageConstants.BusinessStatus.CLUB_JOIN_APPLY.AGREE);
                // 4. 将玩家加入到俱乐部中
                ClubMemberPo clubMember = new ClubMemberPo();
                clubMember.setClubId(clubId);
                clubMember.setUserId(String.valueOf(userId));
                clubMember.setType(EClubIdentityCode.MEMBER.getCode());
                clubMember.setIntegral(0);
                int change = clubMemberMapper.insertSelective(clubMember);
                if (change <= 0) {
                    // 4.1. 插入失败
                    result.setCode(RespCode.FAILED.getCode());
                    result.setMsg("加入俱乐部失败");
                    return result;
                }
                // 4.1. 增加俱乐部当前成员的人数
                clubDao.updateClubMembers(clubId, 1);
                // 4.2. 需要更新联盟成员数 这条语句不阻塞加入流程
                tribeService.updateTribeMemberCount(1, clubId);
                // 4.3. 删除俱乐部申请记录
                clubDao.deleteClubRequest(userId);

                // 新增分层数据
                joinClubSaveTier(JoinClub.builder()
                        .userId(userId.longValue())
                        .clubId(clubId.longValue())
                        .build());

                // 5. 发送玩家加入俱乐部消息
                appBusinessMessageSender.notifyJoinClubSuccessful(JoinClub.builder()
                        .userId(Long.valueOf(userId))
                        .userName(playerName)
                        .clubId(Long.valueOf(clubId))
                        .clubName(clubName)
                        .clubOwnerId(Long.valueOf(clubOwnerId))
                        .build());

                break;
            // 拒绝
            case AppMessageConstants.BusinessStatus.CLUB_JOIN_APPLY.REJECT:

                // 1. 删除俱乐部申请记录
                clubDao.deleteClubRequest(userId);

                // 2. update 业务状态
                appBusinessMessageDao.updateBusinessStatus(MapValueUtils.getLong(detail, "record_id"), AppMessageConstants.BusinessStatus.CLUB_JOIN_APPLY.REJECT);

                // 3. 发送玩家加入俱乐部失败消息
                appBusinessMessageSender.notifyJoinClubFailed(JoinClub.builder()
                        .userId(Long.valueOf(userId))
                        .userName(playerName)
                        .clubId(Long.valueOf(clubId))
                        .clubName(clubName)
                        .clubOwnerId(Long.valueOf(clubOwnerId))
                        .build());

                break;
            default:
                result.setCode(RespCode.FAILED.getCode());
                result.setMsg("未知的业务状态");
                return result;
        }

        return null;
    }




    /**
     * 处理联盟加入申请
     * @param detail 业务消息详情
     * @param businessStatus 业务状态
     * @return 错误消息
     */
    private InvokedResult<Object> handleTribeJoinApply(Map<String, Object> detail, Integer businessStatus) {
        InvokedResult<Object> result = new InvokedResult<>();
        // 获取工单参数
        String businessDataString = MapValueUtils.getString(detail, "business_data");

        // 转换成JSON
        JSONObject businessData = null;
        try {
            businessData = JSONObject.parseObject(businessDataString);
        } catch (Exception e) {
            log.error("解析业务数据失败，businessDataString:{}", businessDataString);
            result.setCode(RespCode.FAILED.getCode());
            result.setMsg("解析业务数据失败");
            return result;
        }

        Integer clubId = businessData.getInteger("clubId");
        String clubName = businessData.getString("clubName");
        Integer clubOwnerId = businessData.getInteger("clubOwnerId");
        Integer tribeId = businessData.getInteger("tribeId");
        String tribeName = businessData.getString("tribeName");
        Integer tribeOwnerId = businessData.getInteger("tribeOwnerId");

        switch (businessStatus) {
            // 同意
            case AppMessageConstants.BusinessStatus.TRIBE_JOIN_APPLY.AGREE:

                // 1. 添加联盟成员tribe_members
                appBusinessMessageDao.insertTribeMember(tribeId, clubId, ETribeMembersType.MEMBER.getCode());

                // 2. 删除加入联盟请求message_tribe_request
                appBusinessMessageDao.deleteMessageTribeRequest(clubId, tribeId);

                // 新增分层数据
                joinTribeSaveTier(JoinTribe.builder()
                        .tribeId(tribeId.longValue())
                        .clubId(clubId.longValue())
                        .build());

                // 3. update 业务状态
                appBusinessMessageDao.updateBusinessStatus(MapValueUtils.getLong(detail, "record_id"), AppMessageConstants.BusinessStatus.TRIBE_JOIN_APPLY.AGREE);

                // 4. 发送联盟加入成功消息
                appBusinessMessageSender.notifyJoinTribeSuccessful(JoinTribe.builder()
                        .clubId(Long.valueOf(clubId))
                        .clubName(clubName)
                        .clubOwnerId(Long.valueOf(clubOwnerId))
                        .tribeId(Long.valueOf(tribeId))
                        .tribeName(tribeName)
                        .tribeOwnerId(Long.valueOf(tribeOwnerId))
                        .build());

                break;
            case AppMessageConstants.BusinessStatus.TRIBE_JOIN_APPLY.REJECT:

                // 1. 删除加入联盟请求 message_tribe_request
                appBusinessMessageDao.deleteMessageTribeRequest(clubId, tribeId);

                // 2. update 业务状态
                appBusinessMessageDao.updateBusinessStatus(MapValueUtils.getLong(detail, "record_id"), AppMessageConstants.BusinessStatus.TRIBE_JOIN_APPLY.REJECT);

                // 3. 发送联盟加入失败消息
                appBusinessMessageSender.notifyJoinTribeFailed(JoinTribe.builder()
                        .clubId(Long.valueOf(clubId))
                        .clubName(clubName)
                        .clubOwnerId(Long.valueOf(clubOwnerId))
                        .tribeId(Long.valueOf(tribeId))
                        .tribeName(tribeName)
                        .tribeOwnerId(Long.valueOf(tribeOwnerId))
                        .build());
                break;

            default:
                result.setCode(RespCode.FAILED.getCode());
                result.setMsg("未知的业务状态");
                return result;

        }
        return null;
    }

    /**
     * 处理回收联盟币
     * @param detail 业务消息详情
     * @param businessStatus 业务状态
     * @return 错误消息
     */
    private InvokedResult<Object> handleRecycleTribeChip(Map<String, Object> detail, Integer businessStatus) {
        InvokedResult<Object> result = new InvokedResult<>();
        // 获取工单参数
        String businessDataString = MapValueUtils.getString(detail, "business_data");

        // 转换成JSON
        JSONObject businessData = null;
        try {
            businessData = JSONObject.parseObject(businessDataString);
        } catch (Exception e) {
            log.error("解析业务数据失败，businessDataString:{}", businessDataString);
            result.setCode(RespCode.FAILED.getCode());
            result.setMsg("解析业务数据失败");
            return result;
        }
        String playerId = businessData.getString("playerId");
        Integer userId = businessData.getInteger("userId");
        String playerName = businessData.getString("playerName");
        Integer clubId = businessData.getInteger("clubId");
        String clubName = businessData.getString("clubName");
        Integer clubOwnerId = businessData.getInteger("clubOwnerId");
        Integer tribeId = businessData.getInteger("tribeId");
        String tribeName = businessData.getString("tribeName");
        Integer tribeOwnerId = businessData.getInteger("tribeOwnerId");
        Long tribeCoinAmount = businessData.getLong("tribeCoinAmount");

        switch (businessStatus) {
            // 通过
            case AppMessageConstants.BusinessStatus.RECYCLE_TRIBE_CHIP.PASS:

                ClubChipReq clubChipReq = new ClubChipReq();
                clubChipReq.setClubId(clubId);
                // 这里乘以100是因为前端传过来的是*100的值，但是消息模板显示的是除100的值，因此实际金额要*100
                // 例如：用户输入10，前端传后台是1000，后台处理也要用1000，但是消息模板显示的是10
                // tribeCoinAmount 是从消息参数中取出来的，所以需要*100
                clubChipReq.setTribeChip(tribeCoinAmount * 100);
                clubChipReq.setUserId(playerId);

                // 1. 调用原来逻辑处理
                result = clubService.retrieve(clubOwnerId, clubChipReq);
                if (result.getCode() != RespCode.SUCCEED.getCode()) {
                    // 1.1. 如果失败，则直接返回
                    return result;
                }
                // 2. 如果是成功，则更新业务状态
                appBusinessMessageDao.updateBusinessStatus(MapValueUtils.getLong(detail, "record_id"), AppMessageConstants.BusinessStatus.RECYCLE_TRIBE_CHIP.PASS);

                // 3. 发送联盟回收成功消息
                appBusinessMessageSender.notifySendOrRecycleTribeChipSuccessful(SendOrRecycleTribeChip.builder()
                        .userId(Long.valueOf(userId))
                        .userName(playerName)
                        .clubId(Long.valueOf(clubId))
                        .clubName(clubName)
                        .clubOwnerId(Long.valueOf(clubOwnerId))
                        .tribeId(Long.valueOf(tribeId))
                        .tribeName(tribeName)
                        .tribeOwnerId(Long.valueOf(tribeOwnerId))
                        .businessCode(AppMessageConstants.BusinessCode.RECYCLE_TRIBE_CHIP)
                        .tribeCoinAmount(tribeCoinAmount)
                        .build());

                break;
            // 拒绝
            case AppMessageConstants.BusinessStatus.RECYCLE_TRIBE_CHIP.REJECT:

                // 直接修改业务状态即可
                appBusinessMessageDao.updateBusinessStatus(MapValueUtils.getLong(detail, "record_id"), AppMessageConstants.BusinessStatus.RECYCLE_TRIBE_CHIP.REJECT);

                break;
            default:
                result.setCode(RespCode.FAILED.getCode());
                result.setMsg("未知的业务状态");

        }
        return result;
    }

    /**
     * 处理发放联盟币
     * @param detail 业务消息详情
     * @param businessStatus 业务状态
     * @return 错误消息
     */
    private InvokedResult<Object> handleSendTribeChip(Map<String, Object> detail, Integer businessStatus) {
        InvokedResult<Object> result = new InvokedResult<>();
        // 获取工单参数
        String businessDataString = MapValueUtils.getString(detail, "business_data");

        // 转换成JSON
        JSONObject businessData = null;
        try {
            businessData = JSONObject.parseObject(businessDataString);
        } catch (Exception e) {
            log.error("解析业务数据失败，businessDataString:{}", businessDataString);
            result.setCode(RespCode.FAILED.getCode());
            result.setMsg("解析业务数据失败");
            return result;
        }
        Integer userId = businessData.getInteger("userId");
        String playerId = businessData.getString("playerId");
        String playerName = businessData.getString("playerName");
        Integer clubId = businessData.getInteger("clubId");
        String clubName = businessData.getString("clubName");
        Integer clubOwnerId = businessData.getInteger("clubOwnerId");
        Integer tribeId = businessData.getInteger("tribeId");
        String tribeName = businessData.getString("tribeName");
        Integer tribeOwnerId = businessData.getInteger("tribeOwnerId");
        Long tribeCoinAmount = businessData.getLong("tribeCoinAmount");

        switch (businessStatus) {
            // 通过
            case AppMessageConstants.BusinessStatus.SEND_TRIBE_CHIP.PASS:


                ClubChipReq clubChipReq = new ClubChipReq();
                clubChipReq.setClubId(clubId);
                // 这里乘以100是因为前端传过来的是*100的值，但是消息模板显示的是除100的值，因此实际金额要*100
                // 例如：用户输入10，前端传后台是1000，后台处理也要用1000，但是消息模板显示的是10
                // tribeCoinAmount 是从消息参数中取出来的，所以需要*100
                clubChipReq.setTribeChip(tribeCoinAmount * 100);
                clubChipReq.setUserId(String.valueOf(playerId));

                // 1. 调用原来逻辑处理
                result = clubService.grant(clubOwnerId, clubChipReq);

                if (result.getCode() != RespCode.SUCCEED.getCode()) {
                    // 1.1. 如果失败，则直接返回
                    return result;
                }

                // 2. 如果是成功，则更新业务状态
                appBusinessMessageDao.updateBusinessStatus(MapValueUtils.getLong(detail, "record_id"), AppMessageConstants.BusinessStatus.SEND_TRIBE_CHIP.PASS);

                // 3. 发送联盟发送成功消息
                appBusinessMessageSender.notifySendOrRecycleTribeChipSuccessful(SendOrRecycleTribeChip.builder()
                        .userId(Long.valueOf(userId))
                        .userName(playerName)
                        .clubId(Long.valueOf(clubId))
                        .clubName(clubName)
                        .clubOwnerId(Long.valueOf(clubOwnerId))
                        .tribeId(Long.valueOf(tribeId))
                        .tribeName(tribeName)
                        .tribeOwnerId(Long.valueOf(tribeOwnerId))
                        .businessCode(AppMessageConstants.BusinessCode.SEND_TRIBE_CHIP)
                        .tribeCoinAmount(tribeCoinAmount)
                        .build());

                break;
            // 拒绝
            case AppMessageConstants.BusinessStatus.SEND_TRIBE_CHIP.REJECT:
                // 直接修改业务状态即可
                appBusinessMessageDao.updateBusinessStatus(MapValueUtils.getLong(detail, "record_id"), AppMessageConstants.BusinessStatus.SEND_TRIBE_CHIP.REJECT);

                break;
            default:
                result.setCode(RespCode.FAILED.getCode());
                result.setMsg("未知的业务状态");
        }
        return result;
    }


    /**
     * 加入俱乐部后保存联盟房间的最小等级
     * @param joinClub 加入俱乐部信息
     */
    public void joinClubSaveTier(JoinClub joinClub) {
        Integer tribeId = appBusinessMessageDao.getTribeIdByClubId(joinClub.getClubId().intValue());
        // 如果没有加入俱乐部则不处理
        if (tribeId == null) {
            log.warn("俱乐部未加入联盟，userId:{}, clubId:{}", joinClub.getUserId(), joinClub.getClubId());
            return;
        }

        // 先查询用户是否已经有层级了
        Integer tribeUserPaymentActivityTierId = appBusinessMessageDao.findTribeUserPaymentActivityTier(tribeId, joinClub.getClubId().intValue(), joinClub.getUserId().intValue());
        if (tribeUserPaymentActivityTierId == null) {
            Integer tpaTierId = appBusinessMessageDao.getDefaultDefaultPaymentActivityTier(tribeId);
            appBusinessMessageDao.insertUserDefaultPaymentActivityTier(
                    tribeId,
                    joinClub.getClubId().intValue(),
                    joinClub.getUserId().intValue(),
                    tpaTierId
            );
        } else {
            // 如果有分层，则修改状态
            appBusinessMessageDao.updateTribeUserPaymentActivityTierStatus(tribeUserPaymentActivityTierId, 1);
        }

        Long userTribeRoomTierId = appBusinessMessageDao.findUserTribeRoomTier(joinClub.getUserId().intValue(), tribeId);
        // 如果有值，则不处理，如果没值，则插入
        if (userTribeRoomTierId == null || userTribeRoomTierId == 0) {
            Integer tribeMinTierId = appBusinessMessageDao.getTribeMinTier();
            appBusinessMessageDao.insertUserTribeRoomTier(joinClub.getUserId().intValue(), tribeId, tribeMinTierId);
        }
    }

    /**
     * 加入联盟后保存联盟房间的最小等级
     * @param joinTribe 加入联盟信息
     */
    public void joinTribeSaveTier(JoinTribe joinTribe) {
        // 查询底下所有用户
        List<Long> clubMemberIds = appBusinessMessageDao.findClubMemberIds(joinTribe.getClubId().intValue());
        if (CollectionUtils.isEmpty(clubMemberIds)) {
            log.warn("俱乐部没有成员，clubId:{}", joinTribe.getClubId());
            return;
        }

        // 设置俱乐部默认分层
        appBusinessMessageDao.updateClubMembersToDefaultTier(joinTribe.getClubId().intValue());

        // 批量插入
        for (Long userId : clubMemberIds) {
            joinClubSaveTier(JoinClub.builder()
                    .userId(userId)
                    .clubId(joinTribe.getClubId())
                    .build());
        }
    }

}
