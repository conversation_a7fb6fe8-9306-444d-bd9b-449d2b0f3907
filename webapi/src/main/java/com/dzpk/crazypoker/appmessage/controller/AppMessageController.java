package com.dzpk.crazypoker.appmessage.controller;


import com.dzpk.crazypoker.appmessage.bean.AppMessageBean;
import com.dzpk.crazypoker.appmessage.service.AppMessageService;
import com.dzpk.crazypoker.appmessage.utils.MapBuilder;
import com.dzpk.crazypoker.common.web.resp.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * AppMessageController
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@Slf4j
@RestController
@RequestMapping(
        value = "/app-message-business/",
        method = RequestMethod.POST,
        consumes = "application/json;charset=UTF-8",
        produces = "application/json;charset=UTF-8")
public class AppMessageController {

    @Resource
    AppMessageService appMessageService;

    /**
     * 审批消息
     * @param userId
     * @param request
     * @return
     */
    @RequestMapping("apply")
    public CommonResponse<Object> apply(@RequestAttribute("user_id") int userId, @RequestBody AppMessageBean request){
        request.setUserId(userId);
        return appMessageService.handleApply(request);
    }



}
