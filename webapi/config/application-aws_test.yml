###############################################################################################
# spring-boot-starter-parent                                                                  #
# spring-boot为了保护application.yml和application.properties，                                  #
# 修改了默认的占位符${...@为@...@                                                                #
# 使用maven-resources-plugin根据profile替换配置时使用@XXX@代替@{XXX}                              #
###############################################################################################
spring:
  application:
    name: napi
  #rabbit mq设置
  rabbitmq:
      host: 127.0.0.1
      port: 5670
      username: work
      password: YUjV9YbAqwuJJT
      #发布确认
      publisher-confirms: true
      #退回确认
      publisher-returns: true
      virtual-host: /
      cache:
        channel:
          size: 100
  servlet:
    # 设置上传文件大小
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

room:
  logo:
    url: http://www.baidu.com
  list:
    remainSec: 120

server:
  port: 9443
  servlet:
    context-path: /api

web:
  enable: true
  pathPrefixs:
  timezone: UTC+08:00
  dateformat: yyyy-MM-dd HH:mm:ss


signature:
  enable: true
  secretKey: 0fea4d0fbeda6195f1cba0ff6413e16887ee9de46613e9092d659a9fe212e273
  fieldName: sh2sg
  charsetName: UTF-8
  bufferSize: 8192
  whitelistUrls: [
    "/","/swagger*","/webjars/*",
    "/v2/api-docs","/druid/*","/sms/*",
    "/user/mod/pwd","/user/register","/wallet/web/recharge","/data_stat/publish/*",
    "/user/updateHead","/club/updateClubHead","/tribe/updateTribeHead","/upload/updateImage","/payment/createOrder"
  ]

token:
  enable: true
  tokenHeaderName: md5at
  deviceHeaderName: did
  charsetName: UTF-8
  whitelistUrls: [
    "/","/swagger*","/webjars/*",
    "/v2/api-docs","/druid/*","/sms/*","/user/exist_phone",
    "/user/mod/pwd","/user/register","/wallet/web/recharge","/data_stat/publish/*"
  ]
  watch:
    path: /dzpk/session/tokens
  publishUserIp:
    cacheCapacity: 10
    cacheRotateInterval: 30000
    userAgents:
      - BestHTTP
  frequency:
    enable: true
    # 最大访问次数的时间限制,单位：秒
    perSec: 5
    # 时间范围内最大访问次数
    maxNum: 30
    # 达到访问次数后，默认的拒绝时间(秒）
    denySec: 60
    # 白名单
    whitelistUrls: [
      "/","/swagger*","/webjars/*",
      "/v2/api-docs","/druid/*",
      "/user/mod/pwd","/user/register",
      "/expression/use"
    ]

logging:
  config: file:./config/log4j2-local.xml

swagger:
  enable: true
  title: 【疯狂扑克】API
  description:  提供基于Http的接口，由APP客户端发起调用。
  contactName: 020-88888888
  contactUrl: www.xxxx.com
  contactEmail: <EMAIL>

redis:
  enable: true
    instances:
      - {
          name:  ,
          host: 127.0.0.1 ,
          port: 5733 ,
          password: YUjV9YbA%qwuJJT ,
          database: 0
        }
      - {
          name: frequency ,
          host: 127.0.0.1 ,
          port: 5733 ,
          password: YUjV9YbA%qwuJJT ,
          database: 15
        }

  mongo:
    enable: true
    instances:
      - {
          name: ,
          uri: "**************************************************************************************************************************************************************************************" ,
          database: crazy_poker
        }
      - {
          name: mttMongo ,
          uri: "**************************************************************************************************************************************************************************************" ,
          database: crazy_poker
        }

  jdbc:
    enable: true
    ## 连接字符串、用户名、密码
    url: **************************************************************************************************************************
    userName: work
    password: SqL0301myT2016est
  ## 配置初始化大小、最小、最大
  initialSize: 20
  minIdle: 20
  maxActive: 150
  ## 配置获取连接等待超时的时间，单位：毫秒
  maxWait: 60000
  ## 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位: 毫秒
  timeBetweenEvictionRunsMillis: 3000
  ## 配置一个连接在池中最小生存的时间，单位: 毫秒
  minEvictableIdleTimeMillis: 300000
  ## 用来检测连接是否有效的sql，要求是一个查询语句。
  ## 如果validationQuery为null，testOnBorrow、testOnReturn、
  ## testWhileIdle都不会起作用
  validationQuery: SELECT 'x'
  testWhileIdle: true
  testOnBorrow: true
  testOnReturn: true
  ## 打开PSCache，并且指定每个连接上PSCache的大小
  ## 如果用Oracle，则把poolPreparedStatements配置为true，mysql可以配置为false
  cachePStatement: false
  maxSizeOfCachedPStatementPerConn: 20
  ## 配置监控统计拦截的filters，去掉后监控界面sql无法统计
  filters: stat,wall,slf4j
  ## 超过时间限制是否回收
  removeAbandoned: false
  ## 超时时间；单位:秒
  removeAbandonedTimeout: 60
  ## 关闭abanded连接时输出错误日志
  logAbandoned: true

zk:
  enable: true
  ##
  # Zookeeper服务器的地址列表
  # 格式: ip(hostname):port[,ip(hostname):port]*
  # 必须提供
  ##
  connectString: IP:端口,IP:端口,IP:端口
  ##
  # 可选
  # 必须大于0，否则忽略此设置
  # 默认值：60 秒
  ##
  sessionTimeoutMs: 60000
  ##
  # 可选
  # 必须大于0，否则忽略此设置
  # 默认值：15 秒
  ##
  connectionTimeoutMs: 15000
  ##
  # 可选
  # 必须大于0，否则忽略此设置
  # 默认值：1 秒
  ##
  maxCloseWaitMs: 1000
  ##
  # 创建CuratorTempFramework时使用
  # 可选
  # 必须大于0，否则忽略此设置
  # 默认值：3 分
  ##
  inactiveThresholdMs: 180000
  ##
  # 设置当前这个Zookeeper访问的命名空间
  # 如果设置了，通过此实例访问的路径都将自动附加
  # 上此设置作为路径的前缀。
  # null或mepty，忽略此参数
  ##
  namespace:
  ##
  # 1 :  true
  # otherwise false
  ##
  canBeReadOnly:
  useContainerParentsIfAvailable:


# 国内短信配置
sms:
  template: 559670
  templateInternation: 6000005
  uid: qk123456
  pwd: a5202d304211956e86d34b8a32817a47
  smsUrl: http://api.sms.cn/sms/?
  smsCheckUrl: http://api.sms.cn/sms/check?


# 253短信平台(国内)
smscountry:
  # 用户名
  userName: 用户名
  # 密码
  passWord: 密码
  # api地址
  smsUrl: api地址

# 253短信平台(国际)
smsinternational:
  # 用户名
  userName: 用户名
  # 密码
  passWord: 密码
  # api地址
  smsUrl: api地址

user:
  mod-head-fee: 0
  mod-nick-fee: 3000
  mod-head-presented: 300
  reg-chip: 0
  reg-idou: 0

# 扫码注册链接
qrcode:
  register:
    url: https://域名:端口/front/login?invitationCode=

#钱包
wallet:
  #转豆每日限额
  day-limit: 200000000
  #支付web端
  pay-web-url: https://域名/indexPage_pay
  #支付JWT通信密钥
  secret: 通信密钥
  #JWT过期时间/秒
  jwt-express-time: 180
access:
  hyperBcUrl: http://************/hpymarket/api/usd_rate
  rateWithdraw: 0.01

# 战绩统计显示
game:
  record:
    list:

  clubBlindList:

# mtt服务器信息
mttServer:
  ip: **************
  port: 8055

#联盟配置比例
tribe:
  #等级
  levelConfig: 1,10,10;2,20,20;3,30,30
  #联盟币兑换
  chipConfig: 60,6;100,10

#阿里云oss 配置
aliyun:
  oss:
    endpoint: oss-cn-shenzhen.aliyuncs.com
    access-key-id: LTAI5t6UYvAeqQZb6Zb3LRdw
    access-key-secret: ******************************
    bucket-name: qqpoker-icon
