/**
 * $RCSfile: DZPKDao.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-14  $
 * <p/>
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 * <p/>
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.dzpk.component.repositories.mysql.imp;

import java.sql.*;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.dzpk.component.repositories.mysql.dbpool.DBUtil;
import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.component.repositories.mysql.dao.DZPKDao;

/**
 * <p>Title: DZPKDao</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2006</p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class DZPKDaoImp implements DZPKDao {
    private static final Logger logger = LogUtil.getLogger(DZPKDaoImp.class);

    private PreparedStatement createStam(Connection conn, String sql, Object[] params) throws SQLException {
        PreparedStatement stam = conn.prepareStatement(sql);
        int index = 1;
        if (params != null) {
            for (Object param : params) {
                stam.setObject(index, param);
                index++;
            }
        }
        return stam;
    }

    /**
     * 获取一个数据集
     *
     * @param sql
     * @param prams
     * @return
     * @throws SQLException
     */
    @Override
    public List<Object[]> getList(String sql, Object[] prams) throws SQLException {
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stam = createStam(conn, sql, prams);
             ResultSet rs = stam.executeQuery()) {

            List<Object[]> list = new ArrayList<Object[]>();
            while (rs.next()) {
                int count = rs.getMetaData().getColumnCount();
                Object[] values = new Object[count];
                for (int i = 0; i < count; i++) {
                    values[i] = rs.getObject(i + 1);
                }
                list.add(values);
            }
            return list;
        } catch (SQLException e) {
            logger.error("get list error", e);
            logger.debug("sql: {} params: {}", sql, Arrays.toString(prams));
            throw e;
        }
    }

    /**
     * 获取一个Object[]
     *
     * @param sql
     * @param prams
     * @return
     * @throws SQLException
     */
    @Override
    public Object[] getValues(String sql, Object[] prams) throws SQLException {
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stam = createStam(conn, sql, prams);
             ResultSet rs = stam.executeQuery()) {

            Object[] values = null;
            if (rs.next()) {
                int count = rs.getMetaData().getColumnCount();
                values = new Object[count];
                for (int i = 0; i < count; i++) {
                    values[i] = rs.getObject(i + 1);
                }
            }
            return values;
        } catch (SQLException e) {
            logger.error("get values error", e);
            logger.debug("sql: {} params: {}", sql, Arrays.toString(prams));
            throw e;
        }
    }

    /**
     * 获取一个唯一结果
     *
     * @param sql
     * @param prams
     * @return
     * @throws SQLException
     */
    @Override
    public Object getSingleValue(String sql, Object[] prams) throws SQLException {
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stam = createStam(conn, sql, prams);
             ResultSet rs = stam.executeQuery()) {

            Object value = null;
            if (rs != null && rs.next()) {
                value = rs.getObject(1);
            }
            return value;
        } catch (SQLException e) {
            logger.error("get single value error", e);
            logger.debug("sql: {} params: {}", sql, Arrays.toString(prams));
            throw e;
        }
    }

    /**
     * 删、改
     *
     * @param sql
     * @param prams
     * @return
     * @throws SQLException
     */
    @Override
    public int updateOrDel(String sql, Object[] prams) throws SQLException {
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stam = createStam(conn, sql, prams)) {
            int updatedRows = stam.executeUpdate();
            return updatedRows;
        } catch (SQLException e) {
            logger.error("update or del error", e);
            logger.debug("sql: {} params: {}", sql, Arrays.toString(prams));
            throw e;
        }
    }

    /**
     * 增、删、改
     *
     * @param sql
     * @param prams
     * @return
     * @throws SQLException
     */
    @Override
    public void operation(String sql, Object[] prams) throws SQLException {
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stam = createStam(conn, sql, prams)) {
            stam.execute();
        } catch (SQLException e) {
            logger.error("operation error", e);
            logger.debug("sql: {} params: {}", sql, Arrays.toString(prams));
            throw e;
        }
    }

    public <R> R queryOne(String[] sql, Object[] params, Function<Object[], R> mapper, R defaultValue) {
        try {
            Object[] result = getValues(String.join(" ", sql), params);
            return mapper.apply(result);
        } catch (SQLException e) {
            return defaultValue;
        }
    }

    public <R> List<R> queryAll(String[] sql, Object[] params, Function<Object[], R> mapper, List<R> defaultValue) {
        try {
            List<Object[]> result = getList(String.join(" ", sql), params);
            return result.stream().map(mapper).collect(Collectors.toList());
        } catch (SQLException e) {
            return defaultValue;
        }
    }

    public int execute(String[] sql, Object[] params) {
        try {
            return updateOrDel(String.join(" ", sql), params);
        } catch (SQLException e) {
            return 0;
        }
    }
}


