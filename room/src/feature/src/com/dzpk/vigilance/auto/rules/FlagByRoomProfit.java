package com.dzpk.vigilance.auto.rules;

import com.dzpk.vigilance.auto.*;
import com.dzpk.vigilance.model.AtUserLevelTag;
import com.dzpk.vigilance.model.TribeHighRiskActivityLog;
import com.dzpk.vigilance.repositories.mysql.IAtUserLevelTagDao;
import com.dzpk.vigilance.repositories.mysql.IHighRiskPlayerDao;
import com.dzpk.vigilance.repositories.mysql.impl.AtUserLevelTagDaoImpl;
import com.dzpk.vigilance.repositories.mysql.impl.HighRiskPlayerDaoImpl;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class FlagByRoomProfit implements AutoRule {

    private final Config config;
    private final TimeTrigger trigger;

    public FlagByRoomProfit(AutoVigilanceConfig.AutoRuleConfig config) {
        this.config = (Config)config;
        this.trigger = new TimeTrigger(config.getCheckInterval(), TimeUnit.MINUTES);
    }

    @Override
    public void apply(AutoVigilanceExecutor executor, AutoRuleRegistry registry) {
        log.debug("checkInterval={} maxProfitXBB={} comment={} trigger={}", config.getCheckInterval(), config.getMaxProfitXBB(), config.getComment(), trigger);
        config.getAutoUnflagRules().stream()
                .filter(AutoVigilanceConfig.AutoRuleConfig::isEnabled)
                .map(registry::getUnflagRule)
                .forEach(rule -> rule.apply(executor, registry));
        trigger.trigger(() -> {
            IHighRiskPlayerDao highRiskPlayerDao = new HighRiskPlayerDaoImpl();
            IAtUserLevelTagDao atUserLevelTagDao = new AtUserLevelTagDaoImpl();
            HashSet<String> excludes = new HashSet<>();
            if (config.getExcludes().getUserLevelTags() != null) {
                List<AtUserLevelTag> tags = atUserLevelTagDao.findTagsByIds(config.getExcludes().getUserLevelTags());
                tags.stream().map(AtUserLevelTag::getBlind)
                        .flatMap(blind -> Stream.of(blind.split(",")))
                        .forEach(excludes::add);
                log.debug("excluded tags={} excluded blinds={}", config.getExcludes().getUserLevelTags(), excludes);
            }
            List<IHighRiskPlayerDao.RoomUserProfit> result = highRiskPlayerDao.findUsersAboveRoomProfit(config.getMaxProfitXBB());
            // exclude rooms with matching blinds
            Stream<IHighRiskPlayerDao.RoomUserProfit> filtered = result.stream().filter(o -> {
                String sb = String.format("%.1f", o.getBb() / 200.0);
                String bb = String.format("%.1f", o.getBb() / 100.0);
                return !excludes.contains(sb + "/" + bb);
            });
            Timestamp createdTime = new Timestamp(System.currentTimeMillis());
            List<TribeHighRiskActivityLog> flagLogList = filtered.map(o -> {
                String autoFlagReason = String.format(config.getReasonTemplate(), o.getProfit() / 100.0, o.getRoomId(), config.getMaxProfitXBB());
                TribeHighRiskActivityLog activityLog = new TribeHighRiskActivityLog(o.getUserId(), o.getTribeId(), createdTime, 0, TribeHighRiskActivityLog.ActionType.AUTO_ENABLE.getValue());
                activityLog.setAutoFlagType(config.getType());
                activityLog.setAutoFlagReason(autoFlagReason);
                activityLog.setAutoFlagRoomId(o.getRoomId());
                return activityLog;
            }).collect(Collectors.toList());
            executor.flagUsers(flagLogList, config);
        });
    }

    public interface Config extends AutoVigilanceConfig.AutoFlagRuleConfig {
        /**
         * Maximum profit as multiple of big blind.
         * @return
         */
        int getMaxProfitXBB();

        Exclusions getExcludes();
    }

    public interface Exclusions {
        List<Integer> getUserLevelTags();
    }
}
