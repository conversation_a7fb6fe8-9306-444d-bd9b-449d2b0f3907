package com.dzpk.vigilance.repositories.mysql.impl;

import com.dzpk.component.repositories.mysql.imp.DZPKDaoImp;
import com.dzpk.vigilance.model.AtUserLevelTag;
import com.dzpk.vigilance.repositories.mysql.IAtUserLevelTagDao;

import java.util.Collections;
import java.util.List;

public class AtUserLevelTagDaoImpl extends DZPKDaoImp implements IAtUserLevelTagDao {

    @Override
    public List<AtUserLevelTag> findTagsByIds(List<Integer> idList) {
        return Collections.emptyList();
    }
}
