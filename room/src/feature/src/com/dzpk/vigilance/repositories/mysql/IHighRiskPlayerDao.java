package com.dzpk.vigilance.repositories.mysql;

import com.dzpk.vigilance.model.TribeHighRiskActivityLog;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.List;

public interface IHighRiskPlayerDao {

    boolean isHighRisk(int userId, int tribeId);

    /**
     *
     * @param maxProfit Maximum profit with fractional part. (12345 = 123.45)
     * @param minutes
     * @return
     */
    List<TribeUserProfit> findUsersAboveProfitInLastMinutes(int maxProfit, int minutes);

    /**
     *
     * @param maxProfit Maximum profit with fractional part. (12345 = 123.45)
     * @return
     */
    List<TribeUserProfit> findUsersAboveLifeCycleProfit(int maxProfit);

    /**
     *
     * @param maxProfitXBB Maximum profit as multiple of big blind.
     * @return
     */
    List<RoomUserProfit> findUsersAboveRoomProfit(int maxProfitXBB);

    /**
     *
     * @param minProfit Minimum profit with fractional part. (12345 = 123.45)
     * @param minutes
     * @return
     */
    List<TribeUserProfit> findHighRiskUsersBelowProfitInLastMinutes(int minProfit, int minutes);

    /**
     *
     * @param minProfit Minimum profit with fractional part. (12345 = 123.45)
     * @return
     */
    List<TribeUserProfit> findHighRiskUsersBelowLifeCycleProfit(int minProfit);

    int flagUser(boolean autoCreate, int userId, int tribeId, String comment, String type);

    int unflagUser(int userId, int tribeId, String type);

    void saveActivityLog(TribeHighRiskActivityLog log);

    @Getter
    @RequiredArgsConstructor
    class RoomUserProfit {
        private final int userId;
        private final int tribeId;
        private final int roomId;
        private final int profit;
        private final int bb;
    }

    @Getter
    @RequiredArgsConstructor
    class TribeUserProfit {
        private final int userId;
        private final int tribeId;
        private final int profit;
    }
}
