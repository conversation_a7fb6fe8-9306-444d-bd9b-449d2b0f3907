package com.dzpk.vigilance.repositories.mysql.impl;

import com.dzpk.common.utils.GsonHelper;
import com.dzpk.component.repositories.mysql.imp.DZPKDaoImp;
import com.dzpk.vigilance.model.TribeHighRiskActivityLog;
import com.dzpk.vigilance.repositories.mysql.IHighRiskPlayerDao;
import lombok.extern.slf4j.Slf4j;

import java.sql.SQLException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class HighRiskPlayerDaoImpl extends DZPKDaoImp implements IHighRiskPlayerDao {

    @Override
    public boolean isHighRisk(int userId, int tribeId) {
        String[] sql = {
                "SELECT user_random_num, tribe_random_id FROM tribe_high_risk_list t",
                "WHERE t.user_id = ? AND t.tribe_id = ? AND enable_chase = 1",
        };
        return queryOne(sql, new Object[]{userId, tribeId}, Objects::nonNull, false);
    }

    @Override
    public List<TribeUserProfit> findUsersAboveProfitInLastMinutes(int maxProfit, int minutes) {
        String[] sql = {
                "SELECT t.user_id, t.tribe_id, SUM(t.integral) as pnl FROM user_integral_fee_contribution t",
                "WHERE t.create_time > NOW() - INTERVAL ? MINUTE",
                "AND t.ai = 1 AND t.tribe_id > 0",
                "AND NOT EXISTS (SELECT 1 FROM tribe_high_risk_list h WHERE h.user_id = t.user_id AND h.tribe_id = t.tribe_id AND h.enable_chase = 1)",
                "GROUP BY t.user_id, t.tribe_id HAVING pnl > ?",
        };
        return queryAll(sql, new Object[] {minutes, maxProfit}, this::mapToTribeUserProfit);
    }

    @Override
    public List<TribeUserProfit> findUsersAboveLifeCycleProfit(int maxProfit) {
        String[] sql = {
                "SELECT t.user_id, t.tribe_id, sum(t.integral) as pnl",
                "FROM user_integral_fee_contribution t",
                "WHERE t.ai = 1 AND t.tribe_id > 0",
                "AND NOT EXISTS (SELECT 1 FROM tribe_high_risk_list h WHERE h.user_id = t.user_id AND h.tribe_id = t.tribe_id AND h.enable_chase = 1)",
                "GROUP BY t.user_id, t.tribe_id HAVING pnl > ?",
        };
        return queryAll(sql, new Object[] {maxProfit}, this::mapToTribeUserProfit);
    }

    @Override
    public List<RoomUserProfit> findUsersAboveRoomProfit(int maxProfitXBB) {
        String[] sql = {
                "SELECT t.user_id, t.tribe_id, t.room_id, sum(t.integral) as pnl, (rs.sb_chip*2) as bb",
                "FROM user_integral_fee_contribution t",
                "JOIN room_search rs ON rs.room_id = t.room_id AND rs.status = 4",
                "WHERE t.ai = 1 AND t.tribe_id > 0",
                "AND NOT EXISTS (SELECT 1 FROM tribe_high_risk_list h WHERE h.user_id = t.user_id AND h.tribe_id = t.tribe_id AND h.enable_chase = 1)",
                "GROUP BY t.user_id, t.tribe_id, t.room_id HAVING pnl > (bb * ?)",
        };
        return queryAll(sql, new Object[] {maxProfitXBB}, this::mapToRoomUserProfit);
    }

    @Override
    public List<TribeUserProfit> findHighRiskUsersBelowProfitInLastMinutes(int minProfit, int minutes) {
        String[] sql = {
                "SELECT h.user_id, h.tribe_id, IFNULL(t.pnl, 0) as pnl FROM tribe_high_risk_list h",
                "LEFT JOIN (SELECT t.user_id, t.tribe_id, SUM(t.integral) as pnl FROM user_integral_fee_contribution t",
                "WHERE t.create_time > NOW() - INTERVAL ? MINUTE",
                "GROUP BY t.user_id, t.tribe_id ) t ON h.user_id = t.user_id AND h.tribe_id = t.tribe_id",
                "WHERE h.enable_chase = 1 AND h.auto_flag_type IS NOT NULL",
                "AND IFNULL(t.pnl, 0) < ?",
        };
        return queryAll(sql, new Object[] {minutes, minProfit}, this::mapToTribeUserProfit);
    }

    @Override
    public List<TribeUserProfit> findHighRiskUsersBelowLifeCycleProfit(int minProfit) {
        String[] sql = {
                "SELECT h.user_id, h.tribe_id, IFNULL(t.pnl, 0) as pnl FROM tribe_high_risk_list h",
                "LEFT JOIN (SELECT t.user_id, t.tribe_id, SUM(t.integral) as pnl FROM user_integral_fee_contribution t",
                "GROUP BY t.user_id, t.tribe_id ) t ON h.user_id = t.user_id AND h.tribe_id = t.tribe_id",
                "WHERE h.enable_chase = 1 AND h.auto_flag_type IS NOT NULL",
                "AND IFNULL(t.pnl, 0) < ?",
        };
        return queryAll(sql, new Object[] {minProfit}, this::mapToTribeUserProfit);
    }

    private RoomUserProfit mapToRoomUserProfit(Object[] row) {
        return new RoomUserProfit((Integer) row[0], ((Number) row[1]).intValue(), (Integer) row[2], ((Number) row[3]).intValue(), ((Number) row[4]).intValue());
    }

    private TribeUserProfit mapToTribeUserProfit(Object[] row) {
        return new TribeUserProfit((Integer) row[0], ((Number) row[1]).intValue(), ((Number) row[2]).intValue());
    }

    @Override
    public int flagUser(boolean autoCreate, int userId, int tribeId, String comment, String type) {
        if (autoCreate) {
            return flagUserWithAutoCreate(userId, tribeId, comment, type);
        }
        return flagUser(userId, tribeId, comment, type);
    }

    private int flagUser(int userId, int tribeId, String comment, String type) {
        String[] sql = {
                "UPDATE tribe_high_risk_list",
                "SET enable_chase = 1, updated_time = NOW(), updated_by = 0, remark = ?, auto_flag_type = ?",
                "WHERE user_id = ? AND tribe_id = ? AND enable_chase = 0"
        };
        List<Object> params = new ArrayList<>();
        params.add(comment);
        params.add(type);
        params.add(userId);
        params.add(tribeId);
        return execute(sql, params.toArray());
    }

    private int flagUserWithAutoCreate(int userId, int tribeId, String comment, String type) {
        String[] sql = {
                "INSERT INTO tribe_high_risk_list (user_id, user_name, user_random_num, tribe_id, tribe_random_id, enable_chase, created_time, created_by, updated_time, updated_by, remark, auto_flag_type)",
                "SELECT distinct udi.user_id, udi.nike_name, udi.random_num, tm.tribe_id, tr.random_id, 1, now(), 0, now(), 0, ?, ?",
                "FROM user_details_info udi",
                "JOIN club_members cm ON udi.user_id = cm.user_id",
                "JOIN tribe_members tm ON cm.club_id = tm.club_id",
                "JOIN club_record cr ON cm.club_id = cr.id",
                "JOIN tribe_record tr ON tm.tribe_id = tr.id",
                "WHERE udi.user_id = ? AND tm.tribe_id = ?",
                "AND NOT EXISTS(SELECT 1 FROM tribe_high_risk_list t WHERE t.user_id = udi.user_id AND t.tribe_id = tm.tribe_id AND t.enable_chase = 1)",
                "ON DUPLICATE KEY UPDATE enable_chase = 1, updated_time = now(), updated_by = 0, remark = ?, auto_flag_type = ?"
        };
        List<Object> params = new ArrayList<>();
        params.add(comment);
        params.add(type);
        params.add(userId);
        params.add(tribeId);
        params.add(comment);
        params.add(type);
        return execute(sql, params.toArray());

    }

    @Override
    public int unflagUser(int userId, int tribeId, String type) {
        String[] sql = {
                "UPDATE tribe_high_risk_list",
                "SET enable_chase = 0, updated_time = NOW(), updated_by = 0, disabled_time = NOW(), disabled_by = 0, remark = NULL, auto_flag_type = NULL",
                "WHERE user_id = ? AND tribe_id = ? AND enable_chase = 1 AND auto_flag_type = ?"
        };
        List<Object> params = new ArrayList<>();
        params.add(userId);
        params.add(tribeId);
        params.add(type);
        return execute(sql, params.toArray());
    }

    @Override
    public void saveActivityLog(TribeHighRiskActivityLog log) {
        String[] sql = {
                "INSERT INTO tribe_high_risk_activity_log",
                "(user_id, tribe_id, created_time, action_by, action_type, auto_flag_type, auto_flag_reason, auto_flag_room_id)",
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
        };
        Object[] params = {
                log.getUserId(),
                log.getTribeId(), 
                log.getCreatedTime(),
                log.getActionBy(),
                log.getActionType(),
                log.getAutoFlagType(),
                log.getAutoFlagReason(),
                log.getAutoFlagRoomId()
        };
        execute(sql, params);
    }

    private <R> List<R> queryAll(String[] sql, Object[] params, Function<Object[], R> mapper) {
        List<R> result = queryAll(sql, params, mapper, Collections.emptyList());
        log.debug(GsonHelper.toJson(result, false));
        return result;
    }
}
