package com.ai.dz.room.util;

import com.ai.dz.config.AiRoomManager;
import com.ai.dz.config.AiRuleTemplate;
import com.ai.dz.config.EnterRoomAiPlayer;
import com.ai.dz.config.constant.EAiMode;
import com.ai.dz.room.constant.AiConstant;
import com.ai.dz.room.constant.AiDispatchType;
import com.dzpk.commission.repositories.mysql.ClubTribeDao;
import com.dzpk.commission.repositories.mysql.impl.ClubTribeDaoImpl;
import com.dzpk.commission.repositories.mysql.model.ClubTribeModel;
import com.dzpk.common.utils.LogUtil;
import com.dzpk.component.repositories.redis.RedisService;
import com.dzpk.db.dao.UserInfoDao;
import com.dzpk.db.imp.UserInfoDaoImp;
import com.dzpk.db.model.UserInfo;
import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.cache.Cache;
import com.i366.constant.Constant;
import com.i366.constant.RoomSeatChangeCode;
import com.i366.model.player.RoomPersion;
import com.i366.model.player.RoomPlayer;
import com.i366.model.room.Room;
import com.i366.util.PublisherUtil;
import com.i366.util.RabbitMqUtil;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.s2s.client.AppConfig;
import com.work.comm.s2s.client.ServerManager;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.Logger;

import java.sql.SQLException;
import java.util.*;

/**
 * A派遣相关
 * Created by peterguo on 2018/10/13.
 */
public class AiDispatch {

    private static final Logger logger = LogUtil.getLogger(AiDispatch.class);

    /**
     * ai派遣策略
     * 1）时间到后，根据空位数量配置表，查到派遣ai数量（表里面是范围值的需要随机），如果当前牌桌ai数量>=查到的数量，则停止派遣任务；如果小于派遣数量，判断当前是否在allin状态，如果不是则开始派遣；如果是allin状态则下一手再开始派遣，根据空位数量配置表，查到派遣ai数量，如果当前牌桌ai数量>=查到的数量，则停止派遣任务，否则继续派遣，往下走第2）步
     * 2）根据auto ai配置表，查询对应表里面的ai（牌局所在同盟下的社区、时间、盲注级别、ai类型不能重复），随机到其中一个ai（如果配置表里面的ai已经派遣完，就停止派遣任务）；判断该ai是否还在配置表里面所属社区或者是否已经有同种类型的ai在当前牌桌或者是否已经派遣，如果不在社区或者已有同种类型ai或者已派遣则随机另外一个，如果在则派遣到所有空位的随机一个空位上。并把该ai记录在已派遣集合中。
     * 3）如果已经达到派遣ai数量则取消该牌桌的派遣任务，不再进行派遣；如果未达到，则继续派遣，间隔ns（60-120s随机，需要可配置）派遣下一个ai，重新走第1）、2）、3）步
     *
     * @param room
     * @param aiDispatchType 1正常派遣  2暂停状态,下一手需要派遣
     * @param aiUserId       dispatchType为2时才使用
     */

    public static boolean dispatchStrategy(Room room, AiDispatchType aiDispatchType, int aiUserId) {
        logger.info("[R-{}:{}] 执行派遣策略,当前派遣方式={},当前是否属于爆桌模式={}",
                room.getRoomId(), room.getName(), aiDispatchType.getDesc(), room.getAtRoomConfig().isBoomDeckMode());
        try {
            if (!AiRoomManager.getStatusOfAutoMode(room.getRoomId())) { //再次判断是否自动模式标志位是否关闭 如果关闭的话则结束该任务
                return false;
            }

            int nowAutoAiCount = room.getAtRoomConfig().getAutoAiCount().get();  //获取当前auto at数量
            Set<Integer> seatSet = new HashSet<>(); //空位集合
            for (int i = 0; i < room.getPlayerCount(); i++) {
                seatSet.add(i);
            }
            int leftSeatNum = AiSeat.getLeftSeatNum(room, seatSet); //获取空位数

            boolean needDispatchAi = checkNeedToDisPatch(room, leftSeatNum, nowAutoAiCount);//是否需要派遣

            if (needDispatchAi) {

                if (room.getAtRoomConfig().isBoomDeckMode() && leftSeatNum == 0) {  //爆桌模式且是空位数为0,需要继续随机下一个派遣时间 有可能中途会有人离开有空位
                    checkNeedStillDispatch(room, leftSeatNum);
                    return false;
                }

                EnterRoomAiPlayer enterRoomAiPlayer; //派遣的at信息
                AppConfig appConfig = ServerManager.getInstance().getAppConfig();
                int minChip = room.getMinRate() * room.getChouma() * appConfig.getAiBringInChip();
                if (AiDispatchType.NORMOL_DISPATCH == aiDispatchType) {
                    enterRoomAiPlayer = AiRoomManager.ensureAiOfDispatching(room, minChip);  //获取一个可以派遣的ai
                } else {
                    enterRoomAiPlayer = AiRoomManager.getPlayerOfAuto(room.getRoomId(), aiUserId);
                }

                if (enterRoomAiPlayer != null) {
                    aiUserId = enterRoomAiPlayer.getUserId();
                    //FIXME 2025/2/2 sing:该AI若未在该房间内被派遣过，理论上不会出现在该房的踢出列表中，这里是不是多此一举了？
                    if (checkAiKickOut(room.getRoomId(), aiUserId)) {   //如果该ai已经被房主踢掉，则不能再被派遣到该房间，同时回收该ai，仍可派遣到符合条件的其他房间
                        AiRoomManager.releaseDispatchedAi(room.getRoomId(), aiUserId, true);
                        Cache.deleteOnlineUserInfo(aiUserId, room.getRoomId());  //移除在线用户集合
                        return false;
                    }

                    aiUserId = enterRoomAiPlayer.getUserId();
                    logger.debug("[R-{}:{}][U-{}][AT-{}] ai用戶登入,需要下一手再派遣,allowRoomMatching={}",
                            room.getRoomId(), room.getName(), aiUserId, enterRoomAiPlayer.getType(), enterRoomAiPlayer.getAllowRoomMatching());
                    if (AiLoginManager.isLoginRequired(aiUserId)) {
                        logger.debug("[R-{}:{}]ai用戶登入,userId={}", room.getRoomId(), room.getName(), aiUserId);
                        AiLoginManager.simulateLogin(aiUserId);
                    }
                    if (room.getRoomStatus() == 8) {  //暂停状态 需要下手再坐下
                        if (room.getAutoAiNextHandSeatUserId() != 0) {
                            logger.warn("[R-{}][U-{}][AT-{}] 正待下一手坐下userid={},取消派遣", room.getRoomId(), aiUserId, enterRoomAiPlayer.getType(), room.getAutoAiNextHandSeatUserId());
                            AiRoomManager.releaseDispatchedAi(room.getRoomId(), aiUserId, false);//这时AI应该下次可以再次派遣
                            Cache.deleteOnlineUserInfo(aiUserId, room.getRoomId());  //移除在线用户集合
                            return false;
                        }
                        logger.debug("[R-{}][U-{}][AT-{}] 当前房间处于暂停状态,需要下一手再派遣", room.getRoomId(), aiUserId, enterRoomAiPlayer.getType());
                        room.setAutoAiNextHandSeatUserId(enterRoomAiPlayer.getUserId());
                        return true;
                    } else {
                        int seat = -1;
                        if (!seatSet.isEmpty()) {
                            //随机选择一个座位
                            Random r = new Random();
                            int randomIndex = r.nextInt(seatSet.size());
                            seat = (int) seatSet.toArray()[randomIndex];
                            logger.debug("[R-{}][U-{}][AT-{}] 随机座位号,seat={}", room.getRoomId(), aiUserId, enterRoomAiPlayer.getType(), seat);
                            return doDispatch(room, aiUserId, seat, leftSeatNum, minChip); //执行派遣at
                        } else {
                            logger.info("[R-{}][U-{}][AT-{}] 房间满座，释放派遣", room.getRoomId(), aiUserId, enterRoomAiPlayer.getType());
                            AiRoomManager.releaseDispatchedAi(room.getRoomId(), aiUserId, false);//这时AI应该下次可以再次派遣
                            Cache.deleteOnlineUserInfo(aiUserId, room.getRoomId());  //移除在线用户集合
                        }
                    }
                } else {
                    logger.info("[R-{}][AI] 找不到可派遣的at信息,准备下一次派遣任务:minChip={}", room.getRoomId(), minChip);
                    checkNeedStillDispatch(room, leftSeatNum);
                }
            }

            return false;
        } catch (Exception e) {
            logger.error("[R-{}]执行派遣策略异常", room.getRoomId(), e);
            return false;
        }
    }

    /**
     * 进房，坐下，带入
     *
     * @param room
     * @param aiUserId
     * @param seat
     * @param minChip
     */
    private static boolean doDispatch(Room room, int aiUserId, int seat, int leftSeatNum, int minChip) {
        logger.debug("[R-{}:{}]开始派遣,派遣at={},座位号={},当前房间剩余座位数={},最少带入={}",
                room.getRoomId(), room.getName(), aiUserId, seat, leftSeatNum, minChip);
        boolean isSeatSuccess = enterAndDownRoom(room, aiUserId, seat, minChip);     // 进房坐下
        if (isSeatSuccess) {
            RabbitMqUtil.joinChatGroupRoom(room.getRoomId(), aiUserId);  //加入聊天组
            AiAddChips.delayAddChip(room, aiUserId, seat);  //延时带入
            checkNeedStillDispatch(room, leftSeatNum - 1);          // 检查是否仍需要派遣
            return true;
        } else {
            logger.debug("[R-{}:{}]派遣时坐下/进房失败需要释放at,派遣at={}",
                    room.getRoomId(), room.getName(), aiUserId);
            AiRoomManager.releaseDispatchedAi(room.getRoomId(), aiUserId, false); // 派遣失败，释放at
            Cache.deleteOnlineUserInfo(aiUserId, room.getRoomId());  //移除在线用户集合
            checkNeedStillDispatch(room, leftSeatNum);                          // 检查是否仍需要派遣
            return false;
        }
    }

    /**
     * auto at派遣任务
     *
     * @param room
     */
    public static void disPatchAi(Room room) {
        //PP57-FE1 闲置模式启动，停止派遣AI
        if (AiHelper.isIdleModeActivated(room)) {
            logger.info("[R-{}][房间指定派遣配置] 房间闲置模式开启, 停止派遣AI",room.getRoomId());
            return;
        }

        // ISS-090 限制同一时间只有一个派遣任务
        if (room.roomProcedure.delayTaskMap.values().stream()
                .filter(task -> task.getTaskId() == AiConstant.TASK_AI_AUTO_DISPATCH).count() > 0) {
            logger.info("[R-{}:{}] 上一次派遣任务尚未结束，跳过", room.getRoomId(), room.getName());
            return;
        }
        // PP50-FE5 还原：当没有指定房间派遣时，使用系统默认派瀢配置
        /*//PP50-FE3 如果没有指定房间派遣数据则返回不需要派遣AI
        if (room.getAtGamePlan() == null) {
            logger.debug("[R-{}][房间指定派遣配置] 房间没有指定派遣数据, 不需要派遣",room.getRoomId());
            return;
        }*/

        long gameEndTime = room.getStatus() == 1 ? 0 : room.getGameBeginTime() + room.getMaxPlayTime() * 60 * 1000L;
        int beginDispatchAiTime = AiRoomManager.getDispatchingTimeInSec(room.getCreateTime(), gameEndTime, room); //获取派遣任务的调度时间
        int autoAiCount = room.getAtRoomConfig().getAutoAiCount().get();
        if (beginDispatchAiTime > 0) {
            logger.info("[R-{}:{}]已有{}个at数量, {}秒后开始派遣任务", room.getRoomId(), room.getName(), autoAiCount, beginDispatchAiTime);
            Task task = new Task(AiConstant.TASK_AI_AUTO_DISPATCH, new HashMap<Integer, Object>(), room.getRoomId(), room.getRoomPath());
            WorkThreadService.submitDelayTask(room.getRoomId(), task, beginDispatchAiTime * 1000L);
            room.roomProcedure.delayTaskMap.put(task.getId(), task);
        } else {
            logger.info("[R-{}:{}]已有{}个at数量,不需要派遣任务", room.getRoomId(), room.getName(), autoAiCount);
        }
    }

    /**
     * auto ai自动进房和坐下
     *
     * @param room
     * @param aiUserId
     * @param seatId
     * @param minChip
     * @return
     */
    public static boolean enterAndDownRoom(Room room, int aiUserId, int seatId, int minChip) {

        int realSeatId = -1;

        boolean enterRoomStatus = enterRoom(room, aiUserId, minChip);
        logger.debug("[R-{}][U-{}] enterRoom: seatId={} ,status={} ", room.getRoomId(), aiUserId, seatId, enterRoomStatus);
        if (enterRoomStatus) {
            UserInfo userInfo = Cache.getOnlineUserInfo(aiUserId, room.getRoomId());
            realSeatId = room.getRoomService().downRoom2(aiUserId, seatId, userInfo);//坐下
            logger.debug("[R-{}][U-{}] 坐下seatId: {} realSeatId: {}", room.getRoomId(), aiUserId, seatId, realSeatId);
            if (realSeatId == seatId) {

                //通知别的玩家有人坐下了
                RoomPersion p = room.getRoomPersions()[realSeatId];
                if (p == null || p.getOnlinerType() == -1) {
                    p = room.getDdRoomPersions()[realSeatId];
                }

                if (p == null || p.getOnlinerType() == -1) {
                    p = room.getAudMap().get(aiUserId);
                }

                byte[] bytes;
                for (Integer uid : room.getAudMap().keySet()) {
                    if (uid != aiUserId) {
                        String head = p.getUserInfo().getHead();
                        if (p.getUserInfo().getUseCustom().equals(1)) {
                            if (!StringUtils.isBlank(p.getUserInfo().getCustomUrl())) {
                                head = p.getUserInfo().getCustomUrl();
                            }
                        }
                        Object[][] objs = {
                                {61, realSeatId, I366ClientPickUtil.TYPE_INT_1},
                                {62, p.getUserInfo().getSex(), I366ClientPickUtil.TYPE_INT_1},
                                {130, head, I366ClientPickUtil.TYPE_STRING_UTF16},
                                {131, p.getUserInfo().getNikeName(uid, aiUserId), I366ClientPickUtil.TYPE_STRING_UTF16},
                                {132, aiUserId, I366ClientPickUtil.TYPE_INT_4},
                                {133, p.getNowcounma(), I366ClientPickUtil.TYPE_INT_4},
                        };
                        bytes = I366ClientPickUtil.packAll(objs, Constant.REQ_GAME_RECV_SEAT_DOWN);
                        PublisherUtil.sendByUserId(room, bytes, uid);
                    }
                }

                room.getRoomSeatChangeService().checkSeatChangeTpye(room.getRoomId(), aiUserId, room.getStage(), 0, RoomSeatChangeCode.DOWN_ROOM);

                room.getRoomService().cancelOccupySeat(aiUserId, 4);  //设置为占座状态

                room.getAtRoomConfig().getAutoAiCount().getAndIncrement();  //房间ai数量+1
                logger.debug("[R-{}][U-{}] auto ai sit down successfully!!!", room.getRoomId(), aiUserId);
                return true;
            } else {
                logger.debug("[R-{}][U-{}] 自动坐下失败,realSeatId={}", room.getRoomId(), aiUserId, realSeatId);
                return false;
            }
        } else {
            logger.info("[R-{}][U-{}] 自动进房失败", room.getRoomId(), aiUserId);
            return false;
        }
    }

    /**
     * ai进入房间
     *
     * @param room
     * @param userId
     * @param minChip
     * @return
     */
    private static boolean enterRoom(Room room, int userId, int minChip) {
        logger.debug("at玩家进房 rid={},userId={},minChip={}", room.getRoomId(), userId, minChip);
        /** 房间即将结束,不允许进入 **/
        if (room.getFinished()) {
            logger.error("ai玩家进房失败,该房间即将结束,不允许进入");
            return false;
        }

        /** 判断是否到达房间人数上限 **/
        int size = room.getAudMap().size();
        if (size >= Constant.ROOM_MAX_AUD) {
            logger.error("ai玩家进房失败,该房间观众人数已经到达最大上线,不允许进入");
            return false;
        }

        /** 判断玩家是否被冻结 **/
        UserInfoDao userInfoDao = new UserInfoDaoImp();

        boolean frozen;
        try {
            frozen = userInfoDao.checkUserIfFozen(userId);
        } catch (SQLException e) {
            logger.error("checkUserIfFozen error", e);
            return false;
        }

        if (frozen) {
            logger.error("ai玩家进房失败,该玩家已经被冻结,不允许进入");
            return false;
        }

        ClubTribeModel clubTribeModel = null;
        // 通过AI所属的俱乐部ID获取俱乐部信息
        ClubTribeDao clubTribeDao = new ClubTribeDaoImpl();
        List<Integer> userClubOfTribe = clubTribeDao.getUserClubOfTribe(room.getTribeId(), userId);
        int clubId = 0;
        if (userClubOfTribe != null && !userClubOfTribe.isEmpty()) {
            // AI只会同时在一个俱乐部内
            clubId = userClubOfTribe.get(0);
            // 判断俱乐部、联盟是否正常
            clubTribeModel = clubTribeDao.getClubTribeModel(clubId);
            if (null != clubTribeModel && clubTribeModel.getClubId() != 0) {
                int clubTribeStatus = clubTribeModel.getClubTribeStatus();
                int clubStatus = clubTribeModel.getClubStatus();
//                int tribeStatus = clubTribeModel.getTribeStatus();

                if (2 == clubTribeStatus || 3 == clubTribeStatus) { //处于被踢出中或者转移中的俱乐部
                    logger.error("[R-{}:{}][U-{}]ai玩家进房失败,该玩家所在的俱乐部被踢出或处于转移中,不允许进入,clubId={},clubStatus={}",
                            room.getRoomId(), room.getName(), userId,
                            clubTribeModel.getClubId(), clubTribeStatus);
                    return false;
                }

                if (1 == clubStatus) { //俱乐部处于关闭状态
                    logger.error("[R-{}:{}][U-{}]ai玩家进房失败,该玩家所在的俱乐部已经被关闭,不允许进入,clubId={},clubStatus={}",
                            room.getRoomId(), room.getName(), userId,
                            clubTribeModel.getClubId(), clubStatus);
                    return false;
                }
            } else {
                logger.info("[R-{}:{}][U-{}]ai玩家进房失败,获取俱乐部信息异常,不允许进入,clubId={}",
                        room.getRoomId(), room.getName(), userId, clubId);
                return false;
            }
        } else {
            //PP50-FE5 如果AI玩家没有加入俱乐部则视为无效AI，不允许进入房间
            logger.info("[R-{}:{}][U-{}]ai玩家进房失败,ai没有加入俱乐部,不允许进入房间",
                    room.getRoomId(), room.getName(), userId);
            return false;
        }

        /** 重新初始化基本信息 **/
        UserInfo userInfo;
        try {
            userInfo = userInfoDao.getUserInfo(userId);
            //随机模拟gps,ip信息
            userInfo.setLatitude(Double.parseDouble(AiUtils.randomLonLat("LAT")));
            userInfo.setLongitude(Double.parseDouble(AiUtils.randomLonLat("LON")));
            userInfo.setIp(AiUtils.getRandomIp());
        } catch (SQLException e) {
            logger.error("[R-{}:{}][U-{}]ai玩家进房失败,获取玩家信息异常,不允许进入",
                    room.getRoomId(), room.getName(), userId, e);
            return false;
        }

        userInfo.setOnlineTime(System.currentTimeMillis());
        userInfo.setRoomId(room.getRoomId());
        userInfo.setRoomPath(room.getRoomPath());
        room.getUserDevicesMap().put(userId, 1); //统一为安卓模拟器
        if (clubTribeModel != null) {
            if (clubTribeModel.getClubId() != 0) {
                userInfo.setClubId(clubTribeModel.getClubId());
                userInfo.setClubProportion(clubTribeModel.getClubProportion());
            }
            if (clubTribeModel.getTribeId() != 0) {
                userInfo.setTribeId(clubTribeModel.getTribeId());
                userInfo.setTribeProportion(clubTribeModel.getTribeProportion());
                userInfo.settClubId(clubTribeModel.getTClubId());
            }
        }
        room.getRoomService().accessRoom(userInfo);//进入房间完成
        return true;
    }

    /**
     * 检查该房间是否仍需要派遣at
     *
     * @param room
     * @param leftSeatNum 房间的空位数量
     */
    private static void checkNeedStillDispatch(Room room, int leftSeatNum) {
        logger.debug("[R-{}]检查是否仍需要派遣at,空位数：{}", room.getRoomId(), leftSeatNum);
        int nowAutoAicount = room.getAtRoomConfig().getAutoAiCount().get();
        boolean needDispatch = checkNeedToDisPatch(room, leftSeatNum, nowAutoAicount);
        if (needDispatch && AiRoomManager.getStatusOfAutoMode(room.getRoomId())) {
            disPatchAi(room);
        }
    }

    /**
     * 停止派遣ai
     * 如果派遣开关关了，需要执行终止任务（终止任务：每个AI 随机打n（1-5之间，不用可配置）手后，站起
     *
     * @param room
     */
    public static void stopDispatch(Room room) {
        //如果关闭了需要将房间的操作派遣任务取消
        for (String taskId : room.roomProcedure.delayTaskMap.keySet()) {
            Task dispatchTask = (Task) room.roomProcedure.delayTaskMap.get(taskId);

            if (AiConstant.TASK_AI_AUTO_DISPATCH == dispatchTask.getTaskId()) {
                dispatchTask.getTaskFuture().cancel(true);//停掉该线程任务
                dispatchTask.setValid(false);

                room.roomProcedure.delayTaskMap.remove(dispatchTask.getId());
            }
        }

        for (int i = 0; i < room.getRoomPersions().length; i++) {
            RoomPersion rp = room.getRoomPersions()[i];
            if (rp != null) {
                int userId = rp.getUserId();
                if (AiRuleTemplate.isAiUser(userId) && EAiMode.auto == AiRoomManager.getModeOfPlayer(room.getRoomId(), rp.getUserId())) {  //如果是auto ai需要站起
                    RoomPlayer roomPlayer = room.getRoomPlayers().get(userId);

                    Random r = new Random();
                    int randomHand = r.nextInt(5) + 1;
                    roomPlayer.getAiPlayer().setAiPlayMaxHand(room.getStage() + randomHand);  //将站起手数重新赋值
                    logger.debug("stopDispatch auto ai userid: " + userId + " need to stand up after " + randomHand + " hand");
                }
            }
        }

        room.setStopDispatchAutoti(true);

    }

    /**
     * 判断auto ai是否被踢出该牌局
     *
     * @param roomId
     * @param aiUserId
     * @return
     */
    private static boolean checkAiKickOut(int roomId, int aiUserId) {

        Set<String> kickOutUserSet = RedisService.getRedisService().getKickOutUser(roomId);
        logger.debug("[AI][R-{}][U-{}] check kick out:{}", roomId, aiUserId, kickOutUserSet);
        if (kickOutUserSet.contains(aiUserId + "")) {
            logger.info("[R-{}][U-{}] enter user has been kicked out by this room!", roomId, aiUserId);
            return true;
        }

        return false;
    }

    /**
     * 检测是否有上一手中是否有该手需要坐下的玩家
     *
     * @param room
     */
    public static void checkNextHandSeat(Room room) {
        if (room.getAutoAiNextHandSeatUserId() != 0) {
            logger.debug("[R-{}] 检测到上一手中有该手需要坐下的玩家={}", room.getRoomId(), room.getAutoAiNextHandSeatUserId());
            boolean dispatchFlag = dispatchStrategy(room, AiDispatchType.PARSE_DISPATCH, room.getAutoAiNextHandSeatUserId()); //执行派遣ai策略
            logger.debug("[R-{}] 当前派遣是否成功={},派遣类型={}", room.getRoomId(), dispatchFlag, AiDispatchType.PARSE_DISPATCH);
            room.setAutoAiNextHandSeatUserId(0);  //无论派遣是否成功,都需要重置该字段
        }
    }

    /**
     * 检查当前是否需要派遣at
     * 不论任何派遣模式 牌局结束前30分钟内 停止派遣 todo 调整5分钟内
     * 普通模式 at数量是否已经满足
     * 爆桌模式 是否有空位
     *
     * @param room
     * @param leftSeatNum
     * @param nowAutoAicount
     */
    private static boolean checkNeedToDisPatch(Room room, int leftSeatNum, int nowAutoAicount) {
        //PP57-FE1 闲置模式启动，停止派遣AI，不再执行后边代码
        if (AiHelper.isIdleModeActivated(room)) {
            logger.info("[R-{}][房间指定派遣配置] 房间闲置模式开启,停止派遣",room.getRoomId());
            return false;
        }
        // PP50-FE5 还原：当没有指定房间派遣时，使用系统默认派瀢配置
        /*//PP50-FE3 如果没有指定房间派遣数据则返回不需要派遣AI
        if (room.getAtGamePlan() == null) {
            logger.debug("[R-{}][房间指定派遣配置] 房间没有指定派遣数据,停止派遣",room.getRoomId());
            return false;
        }*/
        if (room.getStatus() == 2) { //已经开局的才计算结束时间
            if (((room.getGameBeginTime() + room.getMaxPlayTime() * 60000L) - System.currentTimeMillis()) <= 5 * 60000) {
                logger.debug("[R-{}]房间剩余时间小于30min,停止派遣", room.getRoomId());
                return false;
            }
        }

        //停止爆桌开关后 爆桌模式需要变成正常模式
        if (room.getAtRoomConfig().isBoomDeckMode() && !AiRoomManager.getStatusOfExplosionTable(room.getRoomId())) {
            room.getAtRoomConfig().setBoomDeckMode(false);
        }

        boolean needDispatchAi = false;
        if (room.getAtRoomConfig().isBoomDeckMode()) {
            if (leftSeatNum <= 0) {
                logger.debug("[R-{}]当前房间已没有空位,但房间剩余时间大于30min", room.getRoomId());
            }
            needDispatchAi = true;

        } else {
            int needDispatchAiNum = AiRoomManager.ensureNumOfDispatching(room, leftSeatNum); //获取目前需要派遣数量at数量

            if (nowAutoAicount < 0) {
                logger.error("[R-{}]需要派遣的数量={},已经派遣的数量是为负数！！！ count={}", room.getRoomId(), needDispatchAiNum, nowAutoAicount);
            } else {
                logger.debug("[R-{}]需要派遣的数量={},已经派遣的数量={}", room.getRoomId(), needDispatchAiNum, nowAutoAicount);
            }
            if (nowAutoAicount >= needDispatchAiNum) {
                logger.debug("[R-{}]当前房间已满足最大派遣数量,停止派遣", room.getRoomId());
            } else {
                needDispatchAi = true;
            }
        }

        return needDispatchAi;
    }

    /**
     * 是否需要派遣at
     *
     * @param room
     * @return
     */
    public static boolean isAiDispatchRequired(Room room) {
        return room.getClubRoomType() != 2; // 0-普通房 1-俱乐部房 2-私人房
    }
}
