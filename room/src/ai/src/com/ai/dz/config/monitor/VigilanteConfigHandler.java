package com.ai.dz.config.monitor;

import com.ai.dz.config.cache.VigilanteConfigBo;
import com.ai.dz.config.cache.impl.AiRuleConfigJsonCacheImpl;
import com.dzpk.common.utils.GsonHelper;
import com.dzpk.common.utils.Helper;
import com.dzpk.component.file.IFileChangedHandler;
import com.dzpk.vigilance.auto.AutoRuleRegistry;
import com.google.common.reflect.TypeToken;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Type;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
public class VigilanteConfigHandler extends AbstractFileChangedHandler 
        implements IFileChangedHandler {
    
    private final String fileName;

    public String fileName() {
        return this.fileName;
    }

    public VigilanteConfigHandler(AiRuleConfigJsonCacheImpl cache) {
        super(cache);
        this.fileName = "vigilante_config.json";
    }

    public void handle(Path filePath) {
        if (null == filePath)
            return;

        log.debug("【Vigilante配置】: {}", filePath);

        try {
            String json = this.readJson(filePath);
            if(null == json || "".equals(json.trim())) {
                log.debug("【Vigilante配置】file content is empty -> skipped!!");
                return;
            }

            json = json.trim();
            Type type = new TypeToken<ConfigVo>(){}.getType();
            ConfigVo configVo = this.parseJsonAsSingle(json,type);
            if (null == configVo) {
                log.warn("【Vigilante配置】json is empty -> skipped!!");
                return;
            }

            // Validate and process player rules
            List<VigilanteConfigBo.PlayerRule> playerRules = null;
            if (configVo.getPlayerRules() != null && !configVo.getPlayerRules().isEmpty()) {
                playerRules = new ArrayList<>();
                
                for (PlayerRuleVo ruleVo : configVo.getPlayerRules()) {
                    if (ruleVo == null) continue;

                    // Validate player count range
                    if (ruleVo.getMin() <= 0 || ruleVo.getMax() <= 0 || ruleVo.getMin() > ruleVo.getMax()) {
                        log.warn("【Vigilante配置】playerRules invalid: min/max range invalid -> skipped! {}", 
                                GsonHelper.toJson(ruleVo, false));
                        continue;
                    }

                    // Process pot rules
                    List<VigilanteConfigBo.PotRule> potRules = null;
                    if (ruleVo.getPotRules() != null && !ruleVo.getPotRules().isEmpty()) {
                        potRules = new ArrayList<>();
                        
                        for (PotRuleVo potRuleVo : ruleVo.getPotRules()) {
                            if (potRuleVo == null) continue;

                            if (potRuleVo.getGteXBB() <= 0) {
                                log.warn("【Vigilante配置】potRule invalid: gteXBB must be positive -> skipped! {}", 
                                        GsonHelper.toJson(potRuleVo, false));
                                continue;
                            }

                            int chance = Helper.parseIntFromPercentFormat(potRuleVo.getChance(), -1);
                            if (chance < 0) {
                                log.warn("【Vigilante配置】potRule invalid: chance format invalid -> skipped! {}", 
                                        GsonHelper.toJson(potRuleVo, false));
                                continue;
                            }

                            VigilanteConfigBo.PotRule potRule = new VigilanteConfigBo.PotRule();
                            potRule.setGteXBB(potRuleVo.getGteXBB());
                            potRule.setChance(chance);
                            potRules.add(potRule);
                        }
                    }

                    if (potRules == null || potRules.isEmpty()) {
                        log.warn("【Vigilante配置】playerRule invalid: no valid pot rules -> skipped! {}", 
                                GsonHelper.toJson(ruleVo, false));
                        continue;
                    }

                    VigilanteConfigBo.PlayerRule playerRule = new VigilanteConfigBo.PlayerRule();
                    playerRule.setMin(ruleVo.getMin());
                    playerRule.setMax(ruleVo.getMax());
                    playerRule.setPotRules(potRules);
                    playerRules.add(playerRule);
                }
            }

            if (playerRules == null || playerRules.isEmpty()) {
                log.warn("【Vigilante配置】invalid: no valid player rules -> skipped!");
                return;
            }

            // Validate other fields
            if (configVo.getWinTypeMin() < 1 || configVo.getWinTypeMin() > 10) {
                log.warn("【Vigilante配置】invalid: winTypeMin must be between 1 and 10 -> skipped!");
                return;
            }

            if (configVo.getPreFlopAllinWeightMin() < 1 || configVo.getPreFlopAllinWeightMin() > 17) {
                log.warn("【Vigilante配置】invalid: preFlopAllinWeightMin must be between 1 and 17 -> skipped!");
                return;
            }

            if (configVo.getPreFlopWeightMin() < 1 || configVo.getPreFlopWeightMin() > 17) {
                log.warn("【Vigilante配置】invalid: preFlopWeightMin must be between 1 and 17 -> skipped!");
                return;
            }

            if (configVo.getPreFlopActionWeightMin() < 1 || configVo.getPreFlopActionWeightMin() > 17) {
                log.warn("【Vigilante配置】invalid: preFlopActionWeightMin must be between 1 and 17 -> skipped!");
                return;
            }

            if (configVo.getSearchIterations() <= 0) {
                log.warn("【Vigilante配置】invalid: searchIterations must be positive -> skipped!");
                return;
            }

            if (configVo.getSearchTimeout() <= 0) {
                log.warn("【Vigilante配置】invalid: searchTimeout must be positive -> skipped!");
                return;
            }

            List<VigilanteConfigBo.AutoFlagRule> autoFlagRules = new ArrayList<>();

            for (AutoFlagRuleVo autoFlagRuleVo: configVo.getAutoFlagRules()) {
                try {
                    autoFlagRules.add(autoFlagRuleVo.toAutoRule());
                } catch (Exception ex) {
                    log.error("autoRule init error", ex);
                    log.warn("【Vigilante配置】autoFlagRule invalid: {} -> skipped!", autoFlagRuleVo.getType());
                }
            }

            VigilanteConfigBo configBo = VigilanteConfigBo.initialize(
                configVo.isEnabled(),
                configVo.getWinTypeMin(),
                configVo.getPreFlopAllinWeightMin(),
                configVo.getPreFlopWeightMin(),
                configVo.getPreFlopActionWeightMin(),
                configVo.getFlopHandType(),
                configVo.getSearchIterations(),
                configVo.getSearchTimeout(),
                playerRules,
                autoFlagRules
            );

            this.cache.reloadVigilanteConfig(configBo);

        } catch (Exception ex) {
            log.warn("Handled 【Vigilante配置】failed: {} -> {}", ex.getMessage(), filePath);
            if (log.isDebugEnabled()) {
                log.debug("", ex);
            }
        }
    }

    @Getter
    @Setter
    private static class ConfigVo {
        private boolean enabled;
        private int winTypeMin;
        private int preFlopAllinWeightMin;
        private int preFlopWeightMin;
        private int preFlopActionWeightMin;
        private Map<String, Boolean> flopHandType;
        private int searchIterations;
        private int searchTimeout;
        private List<PlayerRuleVo> playerRules;
        private List<AutoFlagRuleVo> autoFlagRules;
    }

    @Getter
    @Setter
    private static class PlayerRuleVo {
        private int min;
        private int max;
        private List<PotRuleVo> potRules;
    }

    @Getter
    @Setter
    private static class PotRuleVo {
        private int gteXBB;
        private String chance;
    }

    @Getter
    @Setter
    private static class ExclusionsVo {
        private List<Integer> userLevelTags;
    }

    @Getter
    @Setter
    private static class AutoFlagRuleVo {
        private String type;
        private String comment;
        private boolean enabled;
        private String reasonTemplate;
        private int priority;
        private int checkInterval;
        private Integer checkPeriod;
        private Integer maxProfit;
        private Integer maxProfitXBB;
        private ExclusionsVo excludes;
        private List<AutoUnflagRuleVo> autoUnflagRules;

        public boolean isType(String type) {
            return this.type.equals(type);
        }

        private List<VigilanteConfigBo.AutoUnflagRule> buildAutoUnflagRules(VigilanteConfigBo.AutoFlagRule parent) {
            if (autoUnflagRules == null || autoUnflagRules.isEmpty()) {
                return Collections.emptyList();
            }
            List<VigilanteConfigBo.AutoUnflagRule> result = new ArrayList<>();
            for (AutoUnflagRuleVo ruleVo : autoUnflagRules) {
                result.add(ruleVo.toAutoRule(parent));
            }
            return result;
        }

        public VigilanteConfigBo.AutoFlagRule toAutoRule() {
            if (isType(AutoRuleRegistry.FLAG_ALL_USER_PROFIT)) {
                VigilanteConfigBo.FlagByAllUserProfitRule rule = new VigilanteConfigBo.FlagByAllUserProfitRule();
                rule.setType(type);
                rule.setComment(comment);
                rule.setEnabled(enabled);
                rule.setReasonTemplate(reasonTemplate);
                rule.setPriority(priority);
                rule.setCheckInterval(checkInterval);
                rule.setCheckPeriod(checkPeriod);
                rule.setMaxProfit(maxProfit);
                rule.setAutoUnflagRules(buildAutoUnflagRules(rule));
                return rule;
            }
            if (isType(AutoRuleRegistry.FLAG_ROOM_PROFIT)) {
                VigilanteConfigBo.FlagByRoomProfitRule rule = new VigilanteConfigBo.FlagByRoomProfitRule();
                rule.setType(type);
                rule.setComment(comment);
                rule.setEnabled(enabled);
                rule.setReasonTemplate(reasonTemplate);
                rule.setPriority(priority);
                rule.setCheckInterval(checkInterval);
                rule.setMaxProfitXBB(maxProfitXBB);
                VigilanteConfigBo.FlagByRoomProfitRule.Exclusions excludesBo = new VigilanteConfigBo.FlagByRoomProfitRule.Exclusions(excludes.getUserLevelTags());
                rule.setExcludes(excludesBo);
                rule.setAutoUnflagRules(buildAutoUnflagRules(rule));
                return rule;
            }
            if (isType(AutoRuleRegistry.FLAG_LIFE_CYCLE_PROFIT)) {
                VigilanteConfigBo.FlagByLifeCycleProfitRule rule = new VigilanteConfigBo.FlagByLifeCycleProfitRule();
                rule.setType(type);
                rule.setComment(comment);
                rule.setEnabled(enabled);
                rule.setReasonTemplate(reasonTemplate);
                rule.setPriority(priority);
                rule.setCheckInterval(checkInterval);
                rule.setMaxProfit(maxProfit);
                rule.setAutoUnflagRules(buildAutoUnflagRules(rule));
                return rule;
            }
            throw new IllegalStateException("Unsupported auto rule type: " + type);
        }
    }

    @Getter
    @Setter
    private static class AutoUnflagRuleVo {
        private String type;
        private boolean enabled;
        private String reasonTemplate;
        private int checkInterval;
        private Integer checkPeriod;
        private Integer minProfit;

        public boolean isType(String type) {
            return this.type.equals(type);
        }

        public VigilanteConfigBo.AutoUnflagRule toAutoRule(VigilanteConfigBo.AutoFlagRule parent) {
            if (isType(AutoRuleRegistry.UNFLAG_USER_PROFIT)) {
                VigilanteConfigBo.UnflagByUserProfitRule rule = new VigilanteConfigBo.UnflagByUserProfitRule();
                rule.setType(type);
                rule.setEnabled(enabled);
                rule.setReasonTemplate(reasonTemplate);
                rule.setCheckInterval(checkInterval);
                rule.setCheckPeriod(checkPeriod);
                rule.setMinProfit(minProfit);
                rule.setParent(parent);
                return rule;
            }
            if (isType(AutoRuleRegistry.UNFLAG_LIFE_CYCLE_PROFIT)) {
                VigilanteConfigBo.UnflagByLifeCycleProfitRule rule = new VigilanteConfigBo.UnflagByLifeCycleProfitRule();
                rule.setType(type);
                rule.setEnabled(enabled);
                rule.setReasonTemplate(reasonTemplate);
                rule.setCheckInterval(checkInterval);
                rule.setMinProfit(minProfit);
                rule.setParent(parent);
                return rule;
            }
            throw new IllegalStateException("Unsupported auto rule type: " + type);
        }
    }
}