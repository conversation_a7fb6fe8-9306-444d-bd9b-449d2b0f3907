package com.work.comm.appmessage.utils;

/**
 * AppMessageConstants
 *
 * <AUTHOR>
 * @date 2024/11/12
 */
public class AppMessageConstants {

    // 业务编码
    public static class BusinessCode {
        // 俱乐部加入申请
        public static final String CLUB_JOIN_APPLY = "CLUB_JOIN_APPLY";
        // 联盟加入申请
        public static final String TRIBE_JOIN_APPLY = "TRIBE_JOIN_APPLY";
        // 发放联盟币
        public static final String SEND_TRIBE_CHIP = "SEND_TRIBE_CHIP";
        // 回收联盟币
        public static final String RECYCLE_TRIBE_CHIP = "RECYCLE_TRIBE_CHIP";
    }

    // 分类编码
    public static class CategoryCode {
        // 系统消息
        public static final String SYSTEM = "system";
        // 申请消息
        public static final String APPLY = "apply";
        // 钱包消息
        public static final String WALLET = "wallet";
    }

    // 消息类型
    public static class RenderType {
        // 0[无标题],1[有标题],2[详情],3[审批]
        public static final int NO_TITLE = 0;
        public static final int HAVE_TITLE = 1;
        public static final int DETAIL = 2;
        public static final int APPROVAL = 3;
    }

    // 通知发送状态
    public static class NoticeSendStatus {
        // 0[待发送],1[发送中],2[发送成功],3[发送失败]
        public static final int PENDING = 0;
        public static final int SENDING = 1;
        public static final int SENT = 2;
        public static final int FAILED = 3;
    }

    // 模版编码
    public static class TplCode {
        // 俱乐部相关消息 start
        // 加入俱乐部
        public static final String CLUB0001 = "CLUB0001";
        // 退出俱乐部
        public static final String CLUB0002 = "CLUB0002";
        // 玩家加入俱乐部
        public static final String CLUB0003 = "CLUB0003";
        // 玩具退出俱乐部
        public static final String CLUB0004 = "CLUB0004";
        // 加入俱乐部失败
        public static final String CLUB0005 = "CLUB0005";
        // 加入俱乐部申请
        public static final String CLUB0006 = "CLUB0006";
        // 俱乐部创建完成
        public static final String CLUB0007 = "CLUB0007";
        // 俱乐部创建失败
        public static final String CLUB0008 = "CLUB0008";

        // 联盟相关消息 start
        // 联盟创建完成
        public static final String TRIBE0001 = "TRIBE0001";
        // 联盟创建失败
        public static final String TRIBE0002 = "TRIBE0002";
        // 俱乐部加入联盟
        public static final String TRIBE0003 = "TRIBE0003";
        // 俱乐部退出联盟
        public static final String TRIBE0004 = "TRIBE0004";
        // 加入联盟
        public static final String TRIBE0005 = "TRIBE0005";
        // 退出联盟
        public static final String TRIBE0006 = "TRIBE0006";
        // 加入联盟失败
        public static final String TRIBE0007 = "TRIBE0007";
        // 联盟加入申请
        public static final String TRIBE0008 = "TRIBE0008";

        // 系统相关消息 start
        // 系统公告
        public static final String SYSTEM0001 = "SYSTEM0001";

        // 钱包相关消息 start
        // 钻石发放
        public static final String WALLET0001 = "WALLET0001";
        // 钻石回收
        public static final String WALLET0002 = "WALLET0002";
        // 充值完成
        public static final String WALLET0003 = "WALLET0003";
        // 充值失败
        public static final String WALLET0004 = "WALLET0004";
        // 俱乐部发放金币
        public static final String WALLET0005 = "WALLET0005";
        // 俱乐部发放钻石
        public static final String WALLET0006 = "WALLET0006";
        // 俱乐部发放联盟币
        public static final String WALLET0007 = "WALLET0007";
        // 俱乐部回收联盟币
        public static final String WALLET0008 = "WALLET0008";
        // 发放联盟币
        public static final String WALLET0009 = "WALLET0009";
        // 回收联盟币
        public static final String WALLET0010 = "WALLET0010";
        // 联盟发放钻石
        public static final String WALLET0011 = "WALLET0011";
        // 联盟发放联盟币
        public static final String WALLET0012 = "WALLET0012";
        // 联盟回收联盟币
        public static final String WALLET0013 = "WALLET0013";
        // 联盟币转账
        public static final String WALLET0014 = "WALLET0014";
        // 金币局服务收入
        public static final String WALLET0015 = "WALLET0015";
        // 联盟币局服务收入
        public static final String WALLET0016 = "WALLET0016";
    }
}
