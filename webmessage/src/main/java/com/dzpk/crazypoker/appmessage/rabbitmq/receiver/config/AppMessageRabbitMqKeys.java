package com.dzpk.crazypoker.appmessage.rabbitmq.receiver.config;

/**
 * AppMessageRabbitMqKeys
 *
 * <AUTHOR>
 * @date 2024/11/10
 */
public class AppMessageRabbitMqKeys {

    // 交换机相关
    public static class Exchange {
        public static final String MESSAGE = "poker.topic.exchange.message";
        public static final String NOTICE = "poker.topic.exchange.notice";
        public static final String RELOAD_CONFIG = "poker.topic.exchange.reload_config";
    }

    // 队列相关
    public static class Queue {
        public static final String MESSAGE = "poker.queue.message";
        public static final String NOTICE = "poker.queue.notice";
        public static final String RELOAD_CONFIG = "poker.queue.reload_config";
    }

    // 路由键相关
    public static class RoutingKey {
        public static final String MESSAGE = "poker.routing.message";
        public static final String NOTICE = "poker.routing.notice";
        public static final String RELOAD_CONFIG = "poker.routing.reload_config";
    }

}