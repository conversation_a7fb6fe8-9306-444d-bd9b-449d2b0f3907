package com.dzpk.crazypoker.appmessage.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dzpk.crazypoker.appmessage.rabbitmq.receiver.bean.ClubRecordPo;
import com.dzpk.crazypoker.appmessage.rabbitmq.sender.bean.JoinClub;
import com.github.pagehelper.PageHelper;
import com.dzpk.crazypoker.appmessage.bean.AppMessageBean;
import com.dzpk.crazypoker.appmessage.cache.AppMessageConfigCache;
import com.dzpk.crazypoker.appmessage.mapper.AppMessageMapper;
import com.dzpk.crazypoker.appmessage.rabbitmq.receiver.bean.AppMessage;
import com.dzpk.crazypoker.appmessage.rabbitmq.receiver.bean.AppNotice;
import com.dzpk.crazypoker.appmessage.service.AppMessageService;
import com.dzpk.crazypoker.appmessage.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * AppMessageService
 *
 * <AUTHOR>
 * @date 2024/11/9
 */
@Slf4j
@Service
public class AppMessageServiceImpl implements AppMessageService {

    @Resource
    AppMessageMapper appMessageMapper;

    /**
     * 消息过期时间，默认30天
     */
    @Value("${message.expire.time:2592000000}")
    private Long messageExpireTime;

    @Override
    public AppMessageBean getUnreadCount(AppMessageBean param) {

        List<Map<String, Object>> result =  appMessageMapper.countUnreadMessageByUserId(param.getUserId());

        if (result == null || result.isEmpty()) {
            return new AppMessageBean();
        }

        long count = 0;

        Map<String, Long> counts = new HashMap<>();

        for (Map<String, Object> map : result) {
            String categoryCode = MapValueUtils.getString(map, "category_code");
            Long unreadCount = MapValueUtils.getLong(map, "unread_count");
            count += unreadCount;
            counts.put(categoryCode, unreadCount);
        }

        return AppMessageBean.builder()
                .unreadCount(count)
                .unreadCounts(counts)
                .build();
    }


    /**
     * 分页处理
     * @param isPagingEnabled 是否限制
     * @param param 参数
     */
    private void applyPagination(boolean isPagingEnabled, AppMessageBean param) {
        if (isPagingEnabled) {

            if (param.getPageIndex() == null) {
                param.setPageIndex(1);
            }
            if (param.getPageSize() == null) {
                param.setPageSize(20);
            }

            PageHelper.startPage(
                    param.getPageIndex(),
                    param.getPageSize()
            );
        }
    }

    /**
     * 解析json
     * @param jsonStr json字符串
     * @param logMessage 日志信息
     * @return json对象
     */
    private JSONObject parseJson(String jsonStr, String logMessage) {
        try {
            return JSONObject.parseObject(jsonStr);
        } catch (Exception e) {
            log.error(logMessage, e);
            return new JSONObject();
        }
    }


    /**
     * 初始化首次加载
     * @param param 参数
     */
    private void initializeFirstLoad(AppMessageBean param) {
        if (param.getLastReqTime() == null || param.getLastReqTime() == 0) {
            param.setDirection(1);
            param.setLastReqTime(System.currentTimeMillis());
        }
    }


    @Resource
    AppMessageConfigCache appMessageConfigCache;

    /**
     * 映射结果到item
     * @param map 结果
     * @param lang 语言
     * @return item
     */
    private JSONObject mapResultToItem(Map<String, Object> map, Integer lang) {
        JSONObject item = new JSONObject();
        item.put("messageId", MapValueUtils.getBigInteger(map, "message_id"));
        item.put("noticeId", MapValueUtils.getBigInteger(map, "notice_id"));
        item.put("categoryCode", MapValueUtils.getString(map, "category_code"));
        item.put("renderType", MapValueUtils.getInteger(map, "render_type"));
        item.put("isRead", MapValueUtils.getBoolean(map, "is_read"));
        item.put("tplCode", MapValueUtils.getString(map, "tpl_code"));
        item.put("senderId", MapValueUtils.getBigInteger(map, "sender_id"));
        item.put("createdAt", MapValueUtils.getTimestampLong(map, "created_at"));
        item.put("userId", MapValueUtils.getBigInteger(map, "user_id"));
        item.put("readAt", MapValueUtils.getTimestampLong(map, "read_at"));

        // 解析 content，params 和 businessData JSON 字段
        JSONObject contentJson = parseJson(MapValueUtils.getString(map, "content"), "解析消息内容异常");
        if (contentJson != null) {
            String lanCode = AppMessageLang.getCodeByValue(lang);
            JSONObject lanContent = contentJson.getJSONObject(lanCode);
            if (lanContent == null) {
                // 如果没，默认取中文
                lanContent = contentJson.getJSONObject("zh_CN");
            }
            item.put("title", lanContent.getString("title"));
            item.put("content", lanContent.getString("content"));
        }

        item.put("params", parseJson(MapValueUtils.getString(map, "params"), "解析消息参数异常"));

        String businessCode = MapValueUtils.getString(map, "business_code");
        item.put("businessCode", businessCode);
        if (businessCode != null) {
            // 如果业务编码不为空，说明是业务消息，需要封装业务data

            JSONObject businessConfig = appMessageConfigCache.getBusinessConfig(businessCode);

            JSONObject statusLabels = businessConfig.getJSONObject("status_labels");

            Integer businessStatus = MapValueUtils.getInteger(map, "business_status");
            // 直接取自
            JSONObject labelObj = statusLabels.getJSONObject(String.valueOf(businessStatus));

            // 根据语言key获取
            String statusLabel = labelObj.getString(AppMessageLang.getCodeByValue(lang));

            JSONObject businessData = parseJson(MapValueUtils.getString(map, "business_data"), "解析消息业务数据异常");

            Map<String, Object> baseData = MapBuilder.builder()
                    .put("businessRecordId", MapValueUtils.getBigInteger(map, "record_id"))
                    .put("businessName", MapValueUtils.getString(map, "business_name"))
                    .put("businessStatus", businessStatus)
                    .put("businessStatusLabel", statusLabel)
                    .put("operatorId", MapValueUtils.getBigInteger(map, "operator_id"))
                    .put("initiatorId", MapValueUtils.getBigInteger(map, "initiator_id"))
                    .put("operatedAt", MapValueUtils.getTimestampLong(map, "operated_at"))
                    .build();

            // 迭代businessData，将businessData加入到baseData中
            for (String key : businessData.keySet()) {
                baseData.put(key, businessData.get(key));
            }

            item.put("businessData", baseData);
        }



        return item;
    }

    @Override
    public AppMessageBean getHistory(AppMessageBean param) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", param.getUserId());
        queryParams.put("categoryCode", param.getCategoryCode());

        Integer lang = Optional.ofNullable(param.getLang()).orElse(0);

        if (param.getLastReqTime() == null) {
            param.setLastReqTime(0L);
        }

        queryParams.put("direction", param.getDirection());
        queryParams.put("lastReqTime", param.getLastReqTime());

        // 是否分页，首次加载、上拉加载都需要分页
        boolean isLimit = param.getLastReqTime() == 0L || param.getDirection() == 1;

        if (isLimit) {
            long timeDifference = param.getLastReqTime() - messageExpireTime;
            queryParams.put("timeDifference", timeDifference);
        }

        Long count = appMessageMapper.countUserMessageRecords(queryParams);
        if (count == null || count <= 0) {
            return AppMessageBean.builder()
                    .count(0L)
                    .direction(param.getDirection())
                    .lang(lang)
                    .list(Collections.emptyList())
                    .pageIndex(param.getPageIndex())
                    .pageSize(param.getPageSize())
                    .build();
        }

        applyPagination(isLimit, param);

        List<Map<String, Object>> result = appMessageMapper.findUserMessageRecords(queryParams);
        List<JSONObject> list = new ArrayList<>();

        if (!CollectionUtils.isEmpty(result)) {
            for (Map<String, Object> map : result) {
                list.add(mapResultToItem(map, lang));
            }
        }

        return AppMessageBean.builder()
                .count(count)
                .direction(param.getDirection())
                .lang(lang)
                .list(list)
                .pageIndex(param.getPageIndex())
                .pageSize(param.getPageSize())
                .build();
    }

    @Override
    public AppMessageBean clearUnread(AppMessageBean param) {

        return AppMessageBean.builder()
                .clearUnreadCount(Optional.ofNullable(appMessageMapper.updateMessageReadStatus(MapBuilder.builder()
                        .put("userId", param.getUserId())
                        .put("messageIds", param.getMessageIds())
                        .put("categoryCodes", param.getCategoryCodes())
                        .put("readAt", new Timestamp(System.currentTimeMillis()))
                        .build())).orElse(0L))
                .build();
    }


    // 雪花ID生成器
    private static final SnowflakeIdGenerator SNOWFLAKE_ID_GENERATOR = new SnowflakeIdGenerator(1, 1);

    /**
     * 发送消息
     * 消息优先级：业务消息（businessCode） > 模板消息（tplCode） > 直发消息（content）
     * @param param 参数
     * @return 是否发送成功
     */
    @Override
    public Boolean sendMessage(AppMessage param) {

        Map<String, Object> messageRecord = new HashMap<>();
        Map<String, Object> messageBusinessRecord = null;
        int autoApproval = 0; // 自动审批 0未开启 1开启
        int clubId;
        int userId = 0;
        ClubRecordPo clubRecordPo = null;

        // 处理ID
        String messageId = param.getMessageId();
        // 如果消息ID为空，生成一个
        if (messageId == null || messageId.isEmpty()) {
            messageId = SNOWFLAKE_ID_GENERATOR.nextCode();
        }
        messageRecord.put("messageId", messageId);

        // 先判断是否是为业务消息
        String businessCode = param.getBusinessCode();
        if (!StringUtils.isEmpty(businessCode)) {
            // 如果是业务消息，则从缓存中获取业务配置
            JSONObject businessConfig = appMessageConfigCache.getBusinessConfig(businessCode);
            // 如果业务配置为空，则不发送消息
            if (businessConfig == null) {
                log.error("Failed to send message, business config not found, businessCode: {}", businessCode);
                return false;
            }
            // 保存业务编码
            messageRecord.put("businessCode", businessCode);

            // 从业务配置中获取模板编码
            String tplCode = businessConfig.getString("tpl_code");
            if (StringUtils.isEmpty(tplCode)) {
                log.error("Failed to send message, tplCode not found, businessCode: {}", businessCode);
                return false;
            }

            // 从缓存中获取模版配置
            JSONObject tplConfig = appMessageConfigCache.getTplConfig(tplCode);
            // 如果没有则刷新缓存
            if (tplConfig == null) {
                appMessageConfigCache.reloadCache();
            }
            tplConfig = appMessageConfigCache.getTplConfig(tplCode);
            if (tplConfig == null) {
                // 还没有就不发送消息
                log.error("Failed to send message, tpl config not found, tplCode: {}", tplCode);
                return false;
            }

            // 保存模板编码
            messageRecord.put("tplCode", tplCode);

            // 从模版中获取业务分类
            String categoryCode = tplConfig.getString("category_code");
            if (StringUtils.isEmpty(categoryCode)) {
                log.error("Failed to send message, categoryCode not found, tplCode: {}", tplCode);
                return false;
            }

            // 保存分类编码
            messageRecord.put("categoryCode", categoryCode);

            // 获取类型
            messageRecord.put("render_type", tplConfig.getInteger("render_type"));

            // 读取模版内容
            JSONObject tplContent = tplConfig.getJSONObject("tpl_content");
            if (tplContent == null) {
                log.error("Failed to send message, tpl content not found, tplCode: {}", tplCode);
                return false;
            }
            // 获取业务参数，与模版参数
            Map<String, Object> businessData = param.getBusinessData();

            // 迭代模版内容，替换内容生成新的内容，并将替换的内容放到新的params中
            JSONObject content = new JSONObject();
            JSONObject params = new JSONObject();

            for (String lang : tplContent.keySet()) {
                // 获取每种语言的模版内容
                JSONObject langContent = tplContent.getJSONObject(lang);
                String titleTpl = langContent.getString("title");
                String contentTpl = langContent.getString("content");

                // 新的内容
                JSONObject newContent = new JSONObject();

                // 替换模版中的参数${key},具体的值用businessData中的值替换
                if (!StringUtils.isEmpty(titleTpl)) {
                    // 替换标题中的占位符
                    String title = replaceTemplateVariables(titleTpl, businessData, params);
                    newContent.put("title", title);
                }
                if (!StringUtils.isEmpty(contentTpl)) {
                    // 替换内容中的占位符
                    String contentText = replaceTemplateVariables(contentTpl, businessData, params);
                    newContent.put("content", contentText);
                }

                // 将每种语言的内容放到 content 中
                content.put(lang, newContent);
            }

            // 保存最终生成的内容和params
            messageRecord.put("content", JSON.toJSONString(content));
            messageRecord.put("params", JSON.toJSONString(params));

            // 生成业务记录ID
            String recordId = SNOWFLAKE_ID_GENERATOR.nextCode();
            messageRecord.put("recordId", recordId);

            if (AppMessageConstants.BusinessCode.CLUB_JOIN_APPLY.equals(businessCode)) {
                // 获取自动审批配置
                clubId = (int) businessData.get("clubId");
                userId = (int) businessData.get("userId");
                clubRecordPo = appMessageMapper.getClubById(clubId);
                if (clubRecordPo != null) {
                    autoApproval = clubRecordPo.getAutoApproval();
                }
                log.info("-------获取自动审批配置----clubId: {} ----- autoApproval: {}", clubId, autoApproval);
            }

            // 创建业务记录
            messageBusinessRecord = MapBuilder.builder()
                    .put("recordId", recordId)
                    .put("messageId", messageId)
                    .put("businessCode", businessCode)
                    .put("businessName", businessConfig.getString("business_name"))
                    .put("businessData", JSON.toJSONString(businessData))
                    .put("businessStatus", businessConfig.getInteger("default_status"))
                    .put("createdAt", new Timestamp(System.currentTimeMillis()))
                    .build();

        } else {

            // 先判断是否是为模板消息
            String tplCode = param.getTplCode();
            if (!StringUtils.isEmpty(tplCode)) {
                // 如果是模板消息，则从缓存中获取模板配置
                JSONObject tplConfig = appMessageConfigCache.getTplConfig(tplCode);
                if (tplConfig == null) {
                    // 如果没有则刷新缓存
                    appMessageConfigCache.reloadCache();
                }
                tplConfig = appMessageConfigCache.getTplConfig(tplCode);
                // 如果模板配置为空，则不发送消息
                if (tplConfig == null) {
                    log.error("Failed to send message, tpl config not found, tplCode: {}", tplCode);
                    return false;
                }
                // 保存模板编码
                messageRecord.put("tplCode", tplCode);

                // 从模版中获取业务分类
                String categoryCode = tplConfig.getString("category_code");
                if (StringUtils.isEmpty(categoryCode)) {
                    log.error("Failed to send message, categoryCode not found, tplCode: {}", tplCode);
                    return false;
                }
                // 保存分类编码
                messageRecord.put("categoryCode", categoryCode);

                // 设置渲染类型
                messageRecord.put("render_type", tplConfig.getInteger("render_type"));

                // 读取模版内容
                JSONObject tplContent = tplConfig.getJSONObject("tpl_content");
                if (tplContent == null) {
                    log.error("Failed to send message, tpl content not found, tplCode: {}", tplCode);
                    return false;
                }

                // 获取模版参数
                Map<String, Object> params = param.getParams();

                // 直接替换模版内容
                JSONObject content = new JSONObject();

                for (String lang : tplContent.keySet()) {
                    // 获取每种语言的模版内容
                    JSONObject langContent = tplContent.getJSONObject(lang);
                    String titleTpl = langContent.getString("title");
                    String contentTpl = langContent.getString("content");

                    // 新的内容
                    JSONObject newContent = new JSONObject();

                    // 替换模版中的参数${key},具体的值用params中的值替换
                    if (!StringUtils.isEmpty(titleTpl)) {
                        // 替换标题中的占位符
                        String title = replaceTemplateVariables(titleTpl, params, new JSONObject());
                        newContent.put("title", title);
                    }
                    if (!StringUtils.isEmpty(contentTpl)) {
                        // 替换内容中的占位符
                        String contentText = replaceTemplateVariables(contentTpl, params, new JSONObject());
                        newContent.put("content", contentText);
                    }

                    // 将每种语言的内容放到 content 中
                    content.put(lang, newContent);
                }

                // 保存最终生成的内容和params
                messageRecord.put("content", JSON.toJSONString(content));
                messageRecord.put("params", JSON.toJSONString(params));

            } else {

                // 判断分类不能为空
                String categoryCode = param.getCategoryCode();
                if (StringUtils.isEmpty(categoryCode)) {
                    log.error("Failed to send message, categoryCode is empty");
                    return false;
                }
                messageRecord.put("categoryCode", categoryCode);

                // 渲染类型
                Integer renderType = Optional.ofNullable(param.getRenderType()).orElse(AppMessageConstants.RenderType.NO_TITLE);
                messageRecord.put("render_type", renderType);

                // 如果不是业务消息，也不是模板消息，则直接发送消息
                String title = param.getTitle();
                messageRecord.put("title", title);

                String content = param.getContent();
                if (StringUtils.isEmpty(content)) {
                    log.error("Failed to send message, content is empty");
                    return false;
                }
                messageRecord.put("content", content);
            }
        }

        // 创建时间
        messageRecord.put("createdAt", new Timestamp(System.currentTimeMillis()));

        // 保存发送者
        messageRecord.put("senderId", param.getSenderId());
        if (messageBusinessRecord != null) {
            // 发送者为发起者
            messageBusinessRecord.put("initiatorId", param.getSenderId());
            // 接受人为操作者
            messageBusinessRecord.put("operatorId", param.getReceiverUserId());
        }

        // 创建接受者
        Map<String, Object> messageReceiver = new HashMap<>();
        // 生成接受者ID
        String receiverId = SNOWFLAKE_ID_GENERATOR.nextCode();
        messageReceiver.put("receiverId", receiverId);
        messageReceiver.put("messageId", messageId);
        messageReceiver.put("userId", param.getReceiverUserId());
        messageReceiver.put("categoryCode", messageRecord.get("categoryCode"));
        messageReceiver.put("isRead", false);
        messageReceiver.put("createdAt", messageRecord.get("createdAt"));

        // 插入业务记录
        if (messageBusinessRecord != null) {
            long insertBusinessRecord = appMessageMapper.insertMessageBusinessRecord(messageBusinessRecord);
            if (insertBusinessRecord <= 0) {
                log.error("Failed to send message, insert business record failed, messageId: {}", messageId);
                return false;
            }
            // 自动审批
            if (autoApproval == 1) {
                log.info("自动审批--进入处理");
                // 同意
                // 1.查询俱乐部
                // 2.先判断是否满员
                if (clubRecordPo.getUpperLimit() <= clubRecordPo.getClubMembers()) {
                    // 2.1 俱乐部已满员
                    log.info("自动审批--俱乐部已满员");
                } else {
                    log.info("自动审批--更新业务状态--messageBusinessRecord: {}", JSONObject.toJSONString(messageBusinessRecord));
                    // 3.update 业务状态
                    appMessageMapper.updateMessageBusinessRecordStatus(1, Long.valueOf((String) messageBusinessRecord.get("recordId")));
                    // 4.将玩家加入到俱乐部中
                    Map<String, Object> clubMember = MapBuilder.builder()
                            .put("clubId", clubRecordPo.getId())
                            .put("userId", userId)
                            .put("type", 2)// 2为俱乐部成员
                            .put("integral", 0)
                            .build();
                    long insertClubMember = appMessageMapper.insertClubMembers(clubMember);
                    if (insertClubMember <= 0) {
                        log.error("Failed to auto approval join club, insert club_members failed, messageId: {}, clubId: {}, userId: {}", messageId, clubRecordPo.getId(), userId);
                    } else {
                        log.info("自动审批--增加俱乐部、联盟当前成员的人数");
                        // 4.1 增加俱乐部当前成员的人数
                        appMessageMapper.updateClubMembers(clubRecordPo.getId(), 1);
                        // 4.2 需要更新联盟成员数，这条语句不阻塞加入流程
                        appMessageMapper.updateTribeMembers(clubRecordPo.getId(), 1);
                        // 4.3 删除俱乐部申请记录
                        appMessageMapper.deleteClubRequest(userId);

                        // 新增俱乐部分层数据
                        log.info("自动审批--俱乐部分层数据");
                        joinClubSaveTier(JoinClub.builder()
                                .userId(Long.valueOf(userId + ""))
                                .clubId(clubRecordPo.getId().longValue())
                                .build());

                        // 5.发送玩家加入俱乐部的消息
                        JSONObject businessData = JSONObject.parseObject(messageBusinessRecord.get("businessData").toString());
                        // 发送给玩家
                        log.info("自动审批--发送加入消息");
                        sendMessage(AppMessage.builder()
                                .tplCode(AppMessageConstants.TplCode.CLUB0001)
                                .params(MapBuilder.builder()
                                        .put("clubName", businessData.get("clubName"))
                                        .put("clubId", businessData.get("clubId"))
                                        .put("clubOwnerId", businessData.get("clubOwnerId"))
                                        .put("playerId", businessData.get("playerId"))
                                        .put("userId", businessData.get("userId"))
                                        .put("playerName", businessData.get("playerName"))
                                        .build())
                                .senderId("0")
                                .receiverUserId(String.valueOf(userId))
                                .build());

                        // 发送给俱乐部主
                        sendMessage(AppMessage.builder()
                                .tplCode(AppMessageConstants.TplCode.CLUB0003)
                                .params(MapBuilder.builder()
                                        .put("clubName", businessData.get("clubName"))
                                        .put("clubId", businessData.get("clubId"))
                                        .put("clubOwnerId", businessData.get("clubOwnerId"))
                                        .put("playerId", businessData.get("playerId"))
                                        .put("userId", businessData.get("userId"))
                                        .put("playerName", businessData.get("playerName"))
                                        .build())
                                .senderId("0")
                                .receiverUserId(String.valueOf(businessData.get("clubOwnerId")))
                                .build());
                    }

                }
            }
        }

        log.info("messageRecord: {}", messageRecord);

        // 插入消息记录
        long insertMessageRecord = appMessageMapper.insertMessageRecord(messageRecord);
        if (insertMessageRecord <= 0) {
            log.error("Failed to send message, insert message record failed, messageId: {}", messageId);
            return false;
        }

        // 插入消息接收者记录
        long insertMessageReceiver = appMessageMapper.insertMessageReceiver(messageReceiver);
        if (insertMessageReceiver <= 0) {
            log.error("Failed to send message, insert message receiver failed, messageId: {}", messageId);
            return false;
        }

        return true;
    }

    /**
     * 替换模版中的占位符变量（如 ${key}）为具体的业务数据，并记录替换的参数
     *
     * @param template 模版字符串，可能包含占位符（如 ${key}）
     * @param businessData 业务数据，作为替换占位符的值
     * @param params 用于存储替换的参数
     * @return 替换后的字符串
     */
    private String replaceTemplateVariables(String template, Map<String, Object> businessData, JSONObject params) {
        if (StringUtils.isEmpty(template)) {
            return template;
        }

        // 使用正则匹配${key}格式的占位符
        String regex = "\\$\\{([^}]+)}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(template);

        // 替换占位符
        StringBuffer replacedTemplate = new StringBuffer();
        while (matcher.find()) {
            // 获取占位符的key
            String placeholder = matcher.group(1);
            // 获取对应的业务数据
            Object value = businessData.get(placeholder);
            if (value != null) {
                String valueStr = value.toString();
                matcher.appendReplacement(replacedTemplate, valueStr);
                // 将替换的参数记录到params中
                params.put(placeholder, valueStr);
            } else {
                // 如果没有找到对应的值，可以保留原样或替换为空字符串
                matcher.appendReplacement(replacedTemplate, "");
                // 如果找不到替换值，也可以记录在params中
                params.put(placeholder, "");
            }
        }
        matcher.appendTail(replacedTemplate);

        return replacedTemplate.toString();
    }

    /**
     * 发送通知，是查询公告内容后直接发送给用户
     * @param param 参数
     * @return 是否发送成功
     */
    @Override
    public Boolean sendNotice(AppNotice param) {

        String noticeId = param.getNoticeId();
        if (StringUtils.isEmpty(noticeId)) {
            log.error("Failed to send notice, noticeId is empty");
            return false;
        }

        // 如果消息为空，则直接生成
        String messageId = param.getMessageId();
        if (StringUtils.isEmpty(messageId)) {
            messageId = SNOWFLAKE_ID_GENERATOR.nextCode();
        }


        // 直接查询公告内容
        List<Map<String, Object>> noticeByNoticeId = appMessageMapper.findNoticeByNoticeId(noticeId);
        if (CollectionUtils.isEmpty(noticeByNoticeId)) {
            log.error("Failed to send notice, notice not found, noticeId: {}", noticeId);
            return false;
        }

        Map<String, Object> notice = noticeByNoticeId.get(0);

        // 创建消息
        Map<String, Object> messageRecord = MapBuilder.builder()
                .put("messageId", messageId)
                .put("noticeId", noticeId)
                .put("render_type", MapValueUtils.getInteger(notice, "render_type"))
                .put("categoryCode", MapValueUtils.getString(notice, "category_code"))
                .put("content", MapValueUtils.getString(notice, "content"))
                .put("createdAt", new Timestamp(System.currentTimeMillis()))
                .put("senderId", 0) // 系统默认为0
                .build();

        // 获取send_type，判断发送给那种用户，0：所有用户，1：指定用户
        Integer sendType = MapValueUtils.getInteger(notice, "send_type");

        // 查询用户
        List<String> userIds = new ArrayList<>();
        if (sendType == 0) {
            userIds = findAllUserIds();
        } else {
            // 查询指定用户
            String targetUserIds = MapValueUtils.getString(notice, "target_user_ids");
            // 如果不为空，则根据逗号分隔获取通知
            if (!StringUtils.isEmpty(targetUserIds)) {
                List<String> userRandomNums = Arrays.asList(targetUserIds.split(","));
                // 查询userID
                userIds = appMessageMapper.findByRandomNums(userRandomNums);
            }
        }

        // 创建消息接收者
        List<Map<String, Object>> messageReceivers = new ArrayList<>();

        for (String userId : userIds) {
            Map<String, Object> messageReceiver = MapBuilder.builder()
                    .put("receiverId", SNOWFLAKE_ID_GENERATOR.nextCode())
                    .put("messageId", messageRecord.get("messageId"))
                    .put("userId", userId)
                    .put("categoryCode", messageRecord.get("categoryCode"))
                    .put("isRead", false)
                    .put("createdAt", messageRecord.get("createdAt"))
                    .build();
            messageReceivers.add(messageReceiver);
        }

        // 插入消息记录
        Long insertMessageRecord = appMessageMapper.insertMessageRecord(messageRecord);

        if (insertMessageRecord <= 0) {
            log.error("Failed to send notice, insert message record failed, noticeId: {}", noticeId);
            return false;
        }

        // 修改公告状态为发送中
        Long updateNoticeStatus = appMessageMapper.updateNoticeSendStatus(MapBuilder.builder()
                .put("noticeId", noticeId)
                .put("sendStatus", AppMessageConstants.NoticeSendStatus.SENDING)
                .put("sendAt", new Timestamp(System.currentTimeMillis()))
                .put("updateAt", new Timestamp(System.currentTimeMillis()))
                .build());
        if (updateNoticeStatus <= 0) {
            log.error("Failed to send notice, update notice status failed, noticeId: {}", noticeId);
            return false;
        }

        // 批量插入消息接收者
        Long batchInsertMessageReceiver = batchInsertMessageReceiver(messageReceivers);
        if (batchInsertMessageReceiver <= 0) {
            log.error("Failed to send notice, batch insert message receiver failed, noticeId: {}", noticeId);
            return false;
        }

        // 修改公告状态为已发送
        Long updateNoticeStatus2 = appMessageMapper.updateNoticeSendStatus(MapBuilder.builder()
                .put("noticeId", noticeId)
                .put("sendStatus", AppMessageConstants.NoticeSendStatus.SENT)
                .put("updateAt", new Timestamp(System.currentTimeMillis()))
                .build());
        if (updateNoticeStatus2 <= 0) {
            log.error("Failed to send notice, update notice status failed, noticeId: {}", noticeId);
            return false;
        }

        return true;
    }

    // 最大批量插入数量
    private static final Integer MAX_BATCH_INSERT_SIZE = 1000;

    /**
     * 批量插入消息接收者
     * @param messageReceivers 消息接收者
     * @return 插入记录数
     */
    private Long batchInsertMessageReceiver(List<Map<String, Object>> messageReceivers) {
        // 判断是否为空
        if (CollectionUtils.isEmpty(messageReceivers)) {
            return 0L;
        }
        // 判断是否超过最大批量插入数量
        if (messageReceivers.size() > MAX_BATCH_INSERT_SIZE) {
            // 超过最大批量插入数量，分批插入
            int size = messageReceivers.size();
            int batchCount = (size + MAX_BATCH_INSERT_SIZE - 1) / MAX_BATCH_INSERT_SIZE;
            long count = 0;
            for (int i = 0; i < batchCount; i++) {
                int fromIndex = i * MAX_BATCH_INSERT_SIZE;
                int toIndex = Math.min((i + 1) * MAX_BATCH_INSERT_SIZE, size);
                List<Map<String, Object>> subList = messageReceivers.subList(fromIndex, toIndex);
                count += appMessageMapper.batchInsertMessageReceiver(subList);
            }
            return count;
        }
        // 没有超过最大批量插入数量，直接插入
        return appMessageMapper.batchInsertMessageReceiver(messageReceivers);
    }

    /**
     * 最大查询数量
     */
    private static final Integer MAX_QUERY_SIZE = 2000;

    /**
     * 查询所有用户ID
     * @return
     */
    private List<String> findAllUserIds() {

        // 先统计用户数量
        Long userCount = appMessageMapper.countUserId();

        // 如果用户数量为空或者小于等于0，返回空列表
        if (userCount == null || userCount <= 0) {
            return new ArrayList<>();
        }

        // 如果用户数量小于等于最大查询数量，直接查询所有用户ID
        if (userCount <= MAX_QUERY_SIZE) {
            return appMessageMapper.findAllUserIds();
        }

        // 如果用户数量大于最大查询数量，分批查询
        List<String> userIds = new ArrayList<>();
        int batchCount = (int) ((userCount + MAX_QUERY_SIZE - 1) / MAX_QUERY_SIZE);
        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * MAX_QUERY_SIZE;
            int toIndex = Math.min((i + 1) * MAX_QUERY_SIZE, userCount.intValue());
            List<String> subList = appMessageMapper.findUserIdsByPage(fromIndex, toIndex);
            userIds.addAll(subList);
        }
        return userIds;
    }

    /**
     * 加入俱乐部后保存联盟房间的最小等级
     * @param joinClub 加入俱乐部信息
     */
    public void joinClubSaveTier(JoinClub joinClub) {
        log.info("自动审批--进入处理--俱乐部分层数据-- {}", JSONObject.toJSONString(joinClub));
        Integer tribeId = appMessageMapper.getTribeIdByClubId(joinClub.getClubId().intValue());
        log.info("自动审批----俱乐部分层数据--tribeId- {}", tribeId);
        // 如果没有加入俱乐部则不处理
        if (tribeId == null) {
            log.warn("俱乐部未加入联盟，userId:{}, clubId:{}", joinClub.getUserId(), joinClub.getClubId());
            return;
        }

        // 先查询用户是否已经有层级了
        Integer tribeUserPaymentActivityTierId = appMessageMapper.findTribeUserPaymentActivityTier(tribeId, joinClub.getClubId().intValue(), joinClub.getUserId().intValue());
        if (tribeUserPaymentActivityTierId == null) {
            Integer tpaTierId = appMessageMapper.getDefaultDefaultPaymentActivityTier(tribeId);
            appMessageMapper.insertUserDefaultPaymentActivityTier(
                    tribeId,
                    joinClub.getClubId().intValue(),
                    joinClub.getUserId().intValue(),
                    tpaTierId
            );
            log.info("自动审批----没有分层，则新增数据--id:{}", tribeUserPaymentActivityTierId);
        } else {
            log.info("自动审批----有分层，则修改状态--id:{}", tribeUserPaymentActivityTierId);
            // 如果有分层，则修改状态
            appMessageMapper.updateTribeUserPaymentActivityTierStatus(tribeUserPaymentActivityTierId, 1);
        }

        Long userTribeRoomTierId = appMessageMapper.findUserTribeRoomTier(joinClub.getUserId().intValue(), tribeId);
        // 如果有值，则不处理，如果没值，则插入
        log.info("自动审批----如果有值，则不处理，如果没值，则插入");
        if (userTribeRoomTierId == null || userTribeRoomTierId == 0) {
            Integer tribeMinTierId = appMessageMapper.getTribeMinTier();
            appMessageMapper.insertUserTribeRoomTier(joinClub.getUserId().intValue(), tribeId, tribeMinTierId);
        }
    }
}
