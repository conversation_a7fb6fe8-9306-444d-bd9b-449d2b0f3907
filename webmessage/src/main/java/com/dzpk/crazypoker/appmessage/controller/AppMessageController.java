package com.dzpk.crazypoker.appmessage.controller;

import com.dzpk.crazypoker.appmessage.bean.AppMessageBean;
import com.dzpk.crazypoker.appmessage.bean.CommonResponse;
import com.dzpk.crazypoker.appmessage.service.AppMessageService;
import com.dzpk.crazypoker.appmessage.utils.MapBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * AppMessageController
 *
 * <AUTHOR>
 * @date 2024/11/9
 */
@Slf4j
@RestController
@RequestMapping(
        value = "/app-message/",
        method = RequestMethod.POST,
        consumes = "application/json;charset=UTF-8",
        produces = "application/json;charset=UTF-8")
public class AppMessageController {


    @Resource
    AppMessageService appMessageService;

    /**
     * 获取用户未读消息数量
     * @param userId
     * @return
     */
    @RequestMapping("unread-count")
    public CommonResponse<Object> unreadCount(@RequestAttribute(value = "user_id")String userId){
        AppMessageBean result = appMessageService.getUnreadCount(AppMessageBean.builder().userId(userId).build());
        return CommonResponse.builder()
                .data(MapBuilder.builder()
                        .put("unreadCount", result.getUnreadCount())
                        .put("unreadCounts", result.getUnreadCounts())
                        .build())
                .build();
    }


    /**
     * 获取用户消息列表
     * @param userId
     * @param request
     * @return
     */
    @RequestMapping("list")
    public CommonResponse<Object> list(@RequestAttribute(value = "user_id")String userId, @RequestBody AppMessageBean request){

        if (StringUtils.isEmpty(request.getCategoryCode())) {
            return CommonResponse.builder()
                    .status(400)
                    .msg("categoryCode is required")
                    .build();
        }

        request.setUserId(userId);

        AppMessageBean result = appMessageService.getHistory(request);
        return CommonResponse.builder()
                .data(MapBuilder.builder()
                        .put("count", result.getCount())
                        .put("direction", result.getDirection())
                        .put("lang", result.getLang())
                        .put("list", result.getList())
                        .put("pageIndex", result.getPageIndex())
                        .put("pageSize", result.getPageSize())
                        .build())
                .build();
    }

    /**
     * 清空未读消息
     * @param userId
     * @param request
     * @return
     */
    @RequestMapping("clear-unread")
    public CommonResponse<Object> clearUnread(@RequestAttribute(value = "user_id")String userId, @RequestBody AppMessageBean request){

        // categoryCodes 与 messageIds 必须有一个不为空
        if (CollectionUtils.isEmpty(request.getCategoryCodes()) && CollectionUtils.isEmpty(request.getMessageIds())) {
            return CommonResponse.builder()
                    .status(400)
                    .msg("categoryCodes or messageIds is required")
                    .build();
        }

        request.setUserId(userId);
        AppMessageBean result = appMessageService.clearUnread(request);
        AppMessageBean count = appMessageService.getUnreadCount(AppMessageBean.builder().userId(userId).build());
        return CommonResponse.builder()
                .data(MapBuilder.builder()
                        .put("clearUnreadCount", result.getClearUnreadCount())
                        .put("unreadCount", count.getUnreadCount())
                        .put("unreadCounts", count.getUnreadCounts())
                        .build())
                .build();
    }

}
