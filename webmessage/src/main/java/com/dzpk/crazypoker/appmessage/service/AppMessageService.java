package com.dzpk.crazypoker.appmessage.service;

import com.dzpk.crazypoker.appmessage.bean.AppMessageBean;
import com.dzpk.crazypoker.appmessage.rabbitmq.receiver.bean.AppMessage;
import com.dzpk.crazypoker.appmessage.rabbitmq.receiver.bean.AppNotice;

/**
 * IAppMessageService
 *
 * <AUTHOR>
 * @date 2024/11/9
 */
public interface AppMessageService {

    /**
     * 获取用户未读消息数量
     * @param param 参数
     * @return 未读消息数量
     */
    AppMessageBean getUnreadCount(AppMessageBean param);

    /**
     * 获取消息历史
     * @param param 参数
     * @return 消息历史
     */
    AppMessageBean getHistory(AppMessageBean param);

    /**
     * 清除未读消息
     * @param param 参数
     */
    AppMessageBean clearUnread(AppMessageBean param);

    /**
     * 发送消息
     * @param param 参数
     */
    Boolean sendMessage(AppMessage param);

    /**
     * 发送通知
     * @param param 参数
     */
    Boolean sendNotice(AppNotice param);

}
