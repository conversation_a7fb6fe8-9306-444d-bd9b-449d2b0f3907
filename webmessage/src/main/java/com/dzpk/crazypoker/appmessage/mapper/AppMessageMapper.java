package com.dzpk.crazypoker.appmessage.mapper;

import com.dzpk.crazypoker.appmessage.rabbitmq.receiver.bean.ClubRecordPo;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * AppMessageMapper
 *
 * <AUTHOR>
 * @date 2024/11/9
 */
@Mapper
public interface AppMessageMapper {

    @Select({
            "<script>",
            "SELECT user_id FROM user_details_info WHERE random_num IN",
            "<foreach item='item' collection='randomNums' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    List<String> findByRandomNums(@Param("randomNums") List<String> randomNums);

    /**
     * 根据用户ID查询未读消息
     * @param userId 用户ID
     * @return 未读消息统计
     */
    List<Map<String, Object>> countUnreadMessageByUserId(@Param("userId") String userId);


    /**
     * 查询消息记录
     * @param queryParams 查询参数
     * @return 消息记录
     */
    List<Map<String, Object>> findUserMessageRecords(@Param("queryParams") Map<String, Object> queryParams);

    /**
     * 查询消息记录总数
     * @param queryParams 查询参数
     * @return 消息记录总数
     */
    Long countUserMessageRecords(@Param("queryParams") Map<String, Object> queryParams);

    /**
     * 修改消息已读状态
     * @param params 参数
     * @return 修改记录数
     */
    Long updateMessageReadStatus(@Param("params") Map<String, Object> params);


    @Select("SELECT * FROM app_message_category")
    List<Map<String, Object>> findAllCategory();

    @Select("SELECT * FROM app_message_tpl")
    List<Map<String, Object>> findAllTpl();

    @Select("SELECT * FROM app_message_business")
    List<Map<String, Object>> findAllBusiness();

    @Select("SELECT * FROM app_message_notice WHERE notice_id = #{noticeId}")
    List<Map<String, Object>> findNoticeByNoticeId(@Param("noticeId") String noticeId);

    /**
     * 插入消息记录
     * @param params 参数
     * @return 插入记录数
     */
    Long insertMessageRecord(Map<String, Object> params);

    /**
     * 插入消息接收者
     * @param params 参数
     * @return 插入记录数
     */
    Long insertMessageReceiver(Map<String, Object> params);

    /**
     * 插入消息业务记录
     * @param params 参数
     * @return 插入记录数
     */
    Long insertMessageBusinessRecord(Map<String, Object> params);

    /**
     * 修改消息业务记录状态
     * @param status 参数
     * @param recordId 参数
     * @return 插入记录数
     */
    Long updateMessageBusinessRecordStatus(@Param("status") Integer status, @Param("recordId") Long recordId);

    /**
     * 修改通知发送状态
     * @param params 参数
     * @return 修改记录数
     */
    Long updateNoticeSendStatus(@Param("params") Map<String, Object> params);

    /**
     * 批量插入消息接收者
     * @param messageReceivers 消息接收者
     * @return 插入记录数
     */
    Long batchInsertMessageReceiver(@Param("list") List<Map<String, Object>> messageReceivers);


    /**
     * 统计所有用户数量
     * @return 用户数量
     */
    @Select("SELECT COUNT(USER_ID) FROM user_basic_info")
    Long countUserId();

    /**
     * 查询所有用户ID
     * @return 用户ID列表
     */
    @Select("SELECT USER_ID FROM user_basic_info")
    List<String> findAllUserIds();

    /**
     * 分页查询用户ID
     * @param start 起始位置
     * @param pageSize 分页大小
     * @return 用户ID列表
     */
    @Select("SELECT USER_ID FROM user_basic_info LIMIT #{start}, #{pageSize}")
    List<String> findUserIdsByPage(@Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 获取俱乐部信息
     * @return
     */
    @Select({"SELECT id, random_id randomId, name, creator, club_status clubStatus, ",
            "club_members clubMembers, upper_limit upperLimit, auto_approval autoApproval ",
            "FROM club_record where id = #{clubId}"})
    ClubRecordPo getClubById(@Param("clubId") int clubId);

    /**
     * 插入俱乐部成员
     * @param params 参数
     * @return 插入记录数
     */
    Long insertClubMembers(Map<String, Object> params);

    /**
     * 修改俱乐部成员数量
     * @param clubId 参数
     * @param num 参数
     * @return 插入记录数
     */
    Long updateClubMembers(@Param("clubId") int clubId, @Param("num") int num);

    /**
     * 修改联盟成员数量
     * @param clubId 参数
     * @param num 参数
     * @return 插入记录数
     */
    Long updateTribeMembers(@Param("clubId") int clubId, @Param("num") int num);

    /**
     * 删除俱乐部申请记录
     * @param userId 参数
     * @return 插入记录数
     */
    Long deleteClubRequest(@Param("userId") int userId);

    @Select("SELECT tribe_id FROM tribe_members where club_id = #{clubId}")
    Map<String, Object> getTribeByClub(@Param("clubId") int clubId);

    @Select("SELECT id FROM crazy_poker.tribe_payment_activity_tier WHERE tribe_id = #{tribeId} AND tier_name = '未分层' AND manageable = 0 LIMIT 1")
    Map<String, Object> getTribePaymentTier(@Param("tribeId") long tribeId);

    Long insertTribePaymentTier(@Param("param") Map<String, Object> param);

    Long insertUserPaymentTier(@Param("param") Map<String, Object> param);

    @Select("SELECT * FROM tribe_user_payment_activity_tier WHERE user_id = #{userId} AND tribe_id = #{tribeId} AND club_id = #{clubId} AND tpa_tier_id = #{tpaTierId}")
    List<Map<String, Object>> getUserPaymentTierList(@Param("userId") int userId, @Param("clubId") int clubId, @Param("tribeId") long tribeId, @Param("tpaTierId") long tpaTierId);

    @Select("SELECT id FROM user_room_tier WHERE user_id = #{userId} AND tribe_id = #{tribeId}")
    List<Map<String, Object>> getUserTierList(@Param("userId") int userId, @Param("tribeId") long tribeId);

    @Insert("INSERT INTO user_room_tier (user_id, tribe_id, tier_id) VALUES (#{userId}, #{tribeId}, #{tierId})")
    Long insertUserRoomTier(@Param("userId") int userId, @Param("tribeId") long tribeId, @Param("tierId") long tierId);

    @Select("SELECT tribe_id FROM tribe_members WHERE club_id = #{clubId} LIMIT 1")
    Integer getTribeIdByClubId(@Param("clubId") Integer clubId);

    @Select("SELECT id FROM crazy_poker.tribe_payment_activity_tier WHERE tribe_id = #{tribeId} AND tier_name = '未分层' AND manageable = 0 LIMIT 1")
    Integer getDefaultDefaultPaymentActivityTier(Integer tribeId);

    @Insert({
            "insert into tribe_user_payment_activity_tier (tribe_id,club_id,user_id,tpa_tier_id) ",
            "values (#{tribeId},#{clubId},#{userId},#{tpaTierId})"
    })
    void insertUserDefaultPaymentActivityTier(
            @Param("tribeId") Integer tribeId,
            @Param("clubId") Integer clubId,
            @Param("userId") Integer userId,
            @Param("tpaTierId") Integer tpaTierId
    );

    @Select("SELECT id FROM user_room_tier WHERE user_id = #{userId} AND tribe_id = #{tribeId} LIMIT 1")
    Long findUserTribeRoomTier(@Param("userId") Integer userId, @Param("tribeId") Integer tribeId);

    @Select("SELECT id FROM room_tier WHERE manageable = 1 ORDER BY value ASC LIMIT 1")
    Integer getTribeMinTier();

    @Insert("INSERT INTO user_room_tier (user_id, tribe_id, tier_id) VALUES (#{userId}, #{tribeId}, #{tierId})")
    void insertUserTribeRoomTier(@Param("userId") Integer userId, @Param("tribeId") Integer tribeId, @Param("tierId") Integer tierId);

    @Update({
            "UPDATE tribe_user_payment_activity_tier SET status = #{status} WHERE id = #{id}"
    })
    void updateTribeUserPaymentActivityTierStatus(@Param("id") Integer id, @Param("status") Integer status);

    @Select({
            "select id from tribe_user_payment_activity_tier where tribe_id = #{tribeId} and club_id = #{clubId} and user_id = #{userId}"
    })
    Integer findTribeUserPaymentActivityTier(
            @Param("tribeId") Integer tribeId,
            @Param("clubId") Integer clubId,
            @Param("userId") Integer userId);

}
