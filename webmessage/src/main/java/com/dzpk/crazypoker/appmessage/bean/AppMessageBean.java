package com.dzpk.crazypoker.appmessage.bean;

import com.alibaba.fastjson.JSONObject;

import java.util.List;
import java.util.Map;
import lombok.*;

/**
 * AppMessageBean
 *
 * <AUTHOR>
 * @date 2024/11/9
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class AppMessageBean {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 分类编码，system = 系统消息，apply = 申请消息，wallet = 钱包消息
     */
    private String categoryCode;

    /**
     * 上一次请求时间，首次加载传0，之后传上一次请求时间
     */
    private Long lastReqTime = 0L;

    /**
     * 加载方向，0 = 下拉刷新，1 = 上拉加载
     */
    private Integer direction = 0;

    /**
     * 语言，0 = 中文，1 = 英文，2 = 繁体
     */
    private Integer lang;

    /**
     * 页码，从1开始
     */
    private Integer pageIndex = 1;

    /**
     * 每页大小，默认20
     */
    private Integer pageSize = 20;

    /**
     * 消息ID数组
     */
    private List<String> messageIds;

    /**
     * 分类编码数组
     */
    private List<String> categoryCodes;

    /**
     * 有效消息总数
     */
    private Long effectiveCount;

    /**
     * 未读消息总数
     */
    private Long unreadCount;

    /**
     * 列表总数
     */
    private Long count;

    /**
     * 未读消息分类统计
     * key: 消息分类编码
     * value: 未读消息数量
     */
    private Map<String, Long> unreadCounts;

    /**
     * 消息列表
     */
    private List<JSONObject> list;

    /**
     * 清除未读消息总数
     */
    private Long clearUnreadCount;



}
